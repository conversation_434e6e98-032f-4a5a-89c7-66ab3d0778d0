"use client";

import { useMemo, useState } from "react";
import { Brain, LayoutGrid, List, Search, ChevronDown } from "lucide-react";
import type { DetectionItem } from "./types";

export function BlockSidebar({ detectionItems }: { detectionItems: DetectionItem[] }) {
  const [q, setQ] = useState("");
  const [cat, setCat] = useState("all");
  const [open, setOpen] = useState<{general: boolean; categories: boolean; findings: boolean}>({ general: true, categories: true, findings: true });

  const categories = useMemo(() => Array.from(new Set(detectionItems.map((d) => d.category))), [detectionItems]);
  const filtered = useMemo(
    () => detectionItems.filter((d) => (cat === "all" || d.category === cat) && d.name.toLowerCase().includes(q.toLowerCase())),
    [detectionItems, q, cat]
  );

  function onDragStart(e: React.DragEvent, type: string, data: Record<string, unknown>) {
    e.dataTransfer.effectAllowed = "copy";
    e.dataTransfer.setData("application/json", JSON.stringify({ type, ...data }));
  }

  return (
    <div className="w-80 flex-shrink-0 border-l bg-muted/20 h-full flex flex-col">
      <h3 className="text-sm font-semibold px-4 py-3 border-b">Add Blocks</h3>
      <div className="flex-1 overflow-y-auto sidebar-scrollbar">
        <div className="border-b">
          <button className="w-full flex items-center justify-between px-3 py-2 text-xs uppercase text-muted-foreground" onClick={() => setOpen((o) => ({ ...o, general: !o.general }))}>
            <span>General</span><ChevronDown size={14} className={`transition-transform ${open.general ? "rotate-180" : ""}`} />
          </button>
          {open.general && (
            <div className="px-3 pb-3">
              <div
                draggable
                onDragStart={(e) => onDragStart(e, "AI_BLOCK", {})}
                className="p-2 rounded-lg border-2 border-orange-400 bg-orange-50 text-orange-700 flex items-center gap-2 cursor-grab"
              >
                <Brain size={16} /> <span className="text-sm font-medium">AI Instruction</span>
              </div>
            </div>
          )}
        </div>

        <div className="border-b">
          <button className="w-full flex items-center justify-between px-3 py-2 text-xs uppercase text-muted-foreground" onClick={() => setOpen((o) => ({ ...o, categories: !o.categories }))}>
            <span>Categories</span><ChevronDown size={14} className={`transition-transform ${open.categories ? "rotate-180" : ""}`} />
          </button>
          {open.categories && (
            <div className="px-3 pb-3 space-y-2">
              {categories.map((c) => (
                <div key={c}
                  draggable
                  onDragStart={(e) => onDragStart(e, "CATEGORY_BLOCK", { category: c, categoryName: c })}
                  className="p-2 rounded-lg border-2 border-border bg-muted/40 text-foreground flex items-center gap-2 cursor-grab"
                >
                  <LayoutGrid size={16} /> <span className="text-sm font-medium">{c}</span>
                </div>
              ))}
            </div>
          )}
        </div>

        <div>
          <button className="w-full flex items-center justify-between px-3 py-2 text-xs uppercase text-muted-foreground" onClick={() => setOpen((o) => ({ ...o, findings: !o.findings }))}>
            <span>Individual Findings</span><ChevronDown size={14} className={`transition-transform ${open.findings ? "rotate-180" : ""}`} />
          </button>
          {open.findings && (
            <div className="px-3 pb-3 space-y-2">
              <div className="relative">
                <Search size={14} className="absolute left-2 top-1/2 -translate-y-1/2 text-muted-foreground" />
                <input value={q} onChange={(e) => setQ(e.target.value)} placeholder="Search findings..." className="w-full pl-7 pr-2 py-2 border rounded-md text-sm" />
              </div>
              <select value={cat} onChange={(e) => setCat(e.target.value)} className="w-full p-2 border rounded-md text-sm">
                <option value="all">All Categories</option>
                {categories.map((c) => (<option key={c} value={c}>{c}</option>))}
              </select>
              <div className="max-h-[300px] overflow-y-auto sidebar-scrollbar pr-1 space-y-2">
                {filtered.map((it) => (
                  <div key={it.id}
                    draggable
                    onDragStart={(e) => onDragStart(e, "FINDING_BLOCK", { findingId: it.id, findingName: it.name })}
                    className="p-2 rounded-lg border-2 border-blue-400 bg-blue-50 text-blue-700 flex items-center gap-2 cursor-grab"
                  >
                    <List size={16} /> <span className="text-sm font-medium">{it.name}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

