{"version": 3, "sources": [], "sections": [{"offset": {"line": 22, "column": 0}, "map": {"version": 3, "sources": ["file:///T:/Development/TranscriptionModule/transcription-demo/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Tabs({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Root>) {\n  return (\n    <TabsPrimitive.Root\n      data-slot=\"tabs\"\n      className={cn(\"flex flex-col gap-2\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TabsList({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.List>) {\n  return (\n    <TabsPrimitive.List\n      data-slot=\"tabs-list\"\n      className={cn(\n        \"bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsTrigger({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\n  return (\n    <TabsPrimitive.Trigger\n      data-slot=\"tabs-trigger\"\n      className={cn(\n        \"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Content>) {\n  return (\n    <TabsPrimitive.Content\n      data-slot=\"tabs-content\"\n      className={cn(\"flex-1 outline-none\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,KAAK,EACZ,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,8OAAC,wKAAkB;QACjB,aAAU;QACV,WAAW,IAAA,yHAAE,EAAC,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,8OAAC,wKAAkB;QACjB,aAAU;QACV,WAAW,IAAA,yHAAE,EACX,uGACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,2KAAqB;QACpB,aAAU;QACV,WAAW,IAAA,yHAAE,EACX,mqBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,2KAAqB;QACpB,aAAU;QACV,WAAW,IAAA,yHAAE,EAAC,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 88, "column": 0}, "map": {"version": 3, "sources": ["file:///T:/Development/TranscriptionModule/transcription-demo/src/components/workspace-nav.tsx"], "sourcesContent": ["\"use client\";\n\nimport Link from \"next/link\";\nimport { usePathname } from \"next/navigation\";\nimport { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from \"@/components/ui/tabs\";\n\nconst items = [\n  { href: \"/recording\", label: \"Recording\" },\n  { href: \"/ai/dashboard\", label: \"AI Dashboard\" },\n  { href: \"/report-templates\", label: \"Report Templates\" },\n  { href: \"/templates/editor\", label: \"Template Editor\" },\n];\n\nexport function WorkspaceNav() {\n  const pathname = usePathname();\n  const value = items.find((i) => pathname.startsWith(i.href))?.href ?? \"/recording\";\n  return (\n    <Tabs value={value} className=\"w-full\">\n      <TabsList className=\"mb-4\">\n        {items.map((i) => (\n          <TabsTrigger key={i.href} value={i.href} asChild>\n            <Link href={i.href}>{i.label}</Link>\n          </TabsTrigger>\n        ))}\n      </TabsList>\n    </Tabs>\n  );\n}\n\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,MAAM,QAAQ;IACZ;QAAE,MAAM;QAAc,OAAO;IAAY;IACzC;QAAE,MAAM;QAAiB,OAAO;IAAe;IAC/C;QAAE,MAAM;QAAqB,OAAO;IAAmB;IACvD;QAAE,MAAM;QAAqB,OAAO;IAAkB;CACvD;AAEM,SAAS;IACd,MAAM,WAAW,IAAA,iJAAW;IAC5B,MAAM,QAAQ,MAAM,IAAI,CAAC,CAAC,IAAM,SAAS,UAAU,CAAC,EAAE,IAAI,IAAI,QAAQ;IACtE,qBACE,8OAAC,wIAAI;QAAC,OAAO;QAAO,WAAU;kBAC5B,cAAA,8OAAC,4IAAQ;YAAC,WAAU;sBACjB,MAAM,GAAG,CAAC,CAAC,kBACV,8OAAC,+IAAW;oBAAc,OAAO,EAAE,IAAI;oBAAE,OAAO;8BAC9C,cAAA,8OAAC,uKAAI;wBAAC,MAAM,EAAE,IAAI;kCAAG,EAAE,KAAK;;;;;;mBADZ,EAAE,IAAI;;;;;;;;;;;;;;;AAOlC", "debugId": null}}, {"offset": {"line": 158, "column": 0}, "map": {"version": 3, "sources": ["file:///T:/Development/TranscriptionModule/transcription-demo/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,IAAA,yHAAE,EACX,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 183, "column": 0}, "map": {"version": 3, "sources": ["file:///T:/Development/TranscriptionModule/transcription-demo/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,0KAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,2KAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,2KAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,6KAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,IAAA,yHAAE,EACX,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,0KAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,2OAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,8OAAC,4KAAsB;kBACrB,cAAA,8OAAC,6KAAuB;YACtB,aAAU;YACV,WAAW,IAAA,yHAAE,EACX,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,8KAAwB;oBACvB,WAAW,IAAA,yHAAE,EACX,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,2KAAqB;QACpB,aAAU;QACV,WAAW,IAAA,yHAAE,EAAC,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,8OAAC,0KAAoB;QACnB,aAAU;QACV,WAAW,IAAA,yHAAE,EACX,6aACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,mLAA6B;8BAC5B,cAAA,8OAAC,qNAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,8OAAC,8KAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,8OAAC,+KAAyB;QACxB,aAAU;QACV,WAAW,IAAA,yHAAE,EAAC,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,8OAAC,oLAA8B;QAC7B,aAAU;QACV,WAAW,IAAA,yHAAE,EACX,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,qOAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,sLAAgC;QAC/B,aAAU;QACV,WAAW,IAAA,yHAAE,EACX,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,2OAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 428, "column": 0}, "map": {"version": 3, "sources": ["file:///T:/Development/TranscriptionModule/transcription-demo/src/lib/convexClient.ts"], "sourcesContent": ["\"use client\";\n\nimport { ConvexHttpClient } from \"convex/browser\";\n\nexport function getConvex(): ConvexHttpClient | null {\n  const url = process.env.NEXT_PUBLIC_CONVEX_URL;\n  if (!url) return null;\n  try {\n    return new ConvexHttpClient(url);\n  } catch {\n    return null;\n  }\n}\n\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAFA;;AAIO,SAAS;IACd,MAAM;IACN;;IACA,IAAI;QACF,OAAO,IAAI,mLAAgB,CAAC;IAC9B,EAAE,OAAM;QACN,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 450, "column": 0}, "map": {"version": 3, "sources": ["file:///T:/Development/TranscriptionModule/transcription-demo/src/app/report-templates/_components/Toolbar.tsx"], "sourcesContent": ["\"use client\";\n\nimport { Bold, Italic, Underline, List, ListOrdered, AlignLeft, AlignCenter, AlignRight } from \"lucide-react\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\";\n\nexport function Toolbar() {\n  function exec(command: string, value?: string) {\n    try {\n      document.execCommand(command, false, value);\n    } catch {}\n  }\n\n  return (\n    <div className=\"flex flex-wrap items-center gap-2 p-2 rounded-lg border bg-muted/30\">\n      <Select defaultValue=\"Arial\" onValueChange={(v) => exec(\"fontName\", v)}>\n        <SelectTrigger className=\"h-8 w-[160px]\"><SelectValue placeholder=\"Font\"/></SelectTrigger>\n        <SelectContent>\n          <SelectItem value=\"Arial\">Arial</SelectItem>\n          <SelectItem value=\"Georgia\">Georgia</SelectItem>\n          <SelectItem value=\"Verdana\">Verdana</SelectItem>\n        </SelectContent>\n      </Select>\n      <Select defaultValue=\"3\" onValueChange={(v) => exec(\"fontSize\", v)}>\n        <SelectTrigger className=\"h-8 w-[140px]\"><SelectValue placeholder=\"Size\"/></SelectTrigger>\n        <SelectContent>\n          <SelectItem value=\"2\">Small</SelectItem>\n          <SelectItem value=\"3\">Normal</SelectItem>\n          <SelectItem value=\"5\">Heading</SelectItem>\n          <SelectItem value=\"6\">Title</SelectItem>\n        </SelectContent>\n      </Select>\n\n      <div className=\"w-px h-6 bg-border mx-1\"/>\n      <Button size=\"icon\" variant=\"ghost\" className=\"h-8 w-8\" onMouseDown={(e) => e.preventDefault()} onClick={() => exec(\"bold\")}>\n        <Bold className=\"h-4 w-4\" />\n      </Button>\n      <Button size=\"icon\" variant=\"ghost\" className=\"h-8 w-8\" onMouseDown={(e) => e.preventDefault()} onClick={() => exec(\"italic\")}>\n        <Italic className=\"h-4 w-4\" />\n      </Button>\n      <Button size=\"icon\" variant=\"ghost\" className=\"h-8 w-8\" onMouseDown={(e) => e.preventDefault()} onClick={() => exec(\"underline\")}>\n        <Underline className=\"h-4 w-4\" />\n      </Button>\n\n      <div className=\"w-px h-6 bg-border mx-1\"/>\n      <Button size=\"icon\" variant=\"ghost\" className=\"h-8 w-8\" onMouseDown={(e) => e.preventDefault()} onClick={() => exec(\"justifyLeft\")}>\n        <AlignLeft className=\"h-4 w-4\" />\n      </Button>\n      <Button size=\"icon\" variant=\"ghost\" className=\"h-8 w-8\" onMouseDown={(e) => e.preventDefault()} onClick={() => exec(\"justifyCenter\")}>\n        <AlignCenter className=\"h-4 w-4\" />\n      </Button>\n      <Button size=\"icon\" variant=\"ghost\" className=\"h-8 w-8\" onMouseDown={(e) => e.preventDefault()} onClick={() => exec(\"justifyRight\")}>\n        <AlignRight className=\"h-4 w-4\" />\n      </Button>\n\n      <div className=\"w-px h-6 bg-border mx-1\"/>\n      <Button size=\"icon\" variant=\"ghost\" className=\"h-8 w-8\" onMouseDown={(e) => e.preventDefault()} onClick={() => { document.execCommand(\"formatBlock\", false, \"div\"); exec(\"insertUnorderedList\"); }}>\n        <List className=\"h-4 w-4\" />\n      </Button>\n      <Button size=\"icon\" variant=\"ghost\" className=\"h-8 w-8\" onMouseDown={(e) => e.preventDefault()} onClick={() => { document.execCommand(\"formatBlock\", false, \"div\"); exec(\"insertOrderedList\"); }}>\n        <ListOrdered className=\"h-4 w-4\" />\n      </Button>\n    </div>\n  );\n}\n\n"], "names": [], "mappings": ";;;;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAJA;;;;;AAMO,SAAS;IACd,SAAS,KAAK,OAAe,EAAE,KAAc;QAC3C,IAAI;YACF,SAAS,WAAW,CAAC,SAAS,OAAO;QACvC,EAAE,OAAM,CAAC;IACX;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,4IAAM;gBAAC,cAAa;gBAAQ,eAAe,CAAC,IAAM,KAAK,YAAY;;kCAClE,8OAAC,mJAAa;wBAAC,WAAU;kCAAgB,cAAA,8OAAC,iJAAW;4BAAC,aAAY;;;;;;;;;;;kCAClE,8OAAC,mJAAa;;0CACZ,8OAAC,gJAAU;gCAAC,OAAM;0CAAQ;;;;;;0CAC1B,8OAAC,gJAAU;gCAAC,OAAM;0CAAU;;;;;;0CAC5B,8OAAC,gJAAU;gCAAC,OAAM;0CAAU;;;;;;;;;;;;;;;;;;0BAGhC,8OAAC,4IAAM;gBAAC,cAAa;gBAAI,eAAe,CAAC,IAAM,KAAK,YAAY;;kCAC9D,8OAAC,mJAAa;wBAAC,WAAU;kCAAgB,cAAA,8OAAC,iJAAW;4BAAC,aAAY;;;;;;;;;;;kCAClE,8OAAC,mJAAa;;0CACZ,8OAAC,gJAAU;gCAAC,OAAM;0CAAI;;;;;;0CACtB,8OAAC,gJAAU;gCAAC,OAAM;0CAAI;;;;;;0CACtB,8OAAC,gJAAU;gCAAC,OAAM;0CAAI;;;;;;0CACtB,8OAAC,gJAAU;gCAAC,OAAM;0CAAI;;;;;;;;;;;;;;;;;;0BAI1B,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC,4IAAM;gBAAC,MAAK;gBAAO,SAAQ;gBAAQ,WAAU;gBAAU,aAAa,CAAC,IAAM,EAAE,cAAc;gBAAI,SAAS,IAAM,KAAK;0BAClH,cAAA,8OAAC,0MAAI;oBAAC,WAAU;;;;;;;;;;;0BAElB,8OAAC,4IAAM;gBAAC,MAAK;gBAAO,SAAQ;gBAAQ,WAAU;gBAAU,aAAa,CAAC,IAAM,EAAE,cAAc;gBAAI,SAAS,IAAM,KAAK;0BAClH,cAAA,8OAAC,gNAAM;oBAAC,WAAU;;;;;;;;;;;0BAEpB,8OAAC,4IAAM;gBAAC,MAAK;gBAAO,SAAQ;gBAAQ,WAAU;gBAAU,aAAa,CAAC,IAAM,EAAE,cAAc;gBAAI,SAAS,IAAM,KAAK;0BAClH,cAAA,8OAAC,yNAAS;oBAAC,WAAU;;;;;;;;;;;0BAGvB,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC,4IAAM;gBAAC,MAAK;gBAAO,SAAQ;gBAAQ,WAAU;gBAAU,aAAa,CAAC,IAAM,EAAE,cAAc;gBAAI,SAAS,IAAM,KAAK;0BAClH,cAAA,8OAAC,sOAAS;oBAAC,WAAU;;;;;;;;;;;0BAEvB,8OAAC,4IAAM;gBAAC,MAAK;gBAAO,SAAQ;gBAAQ,WAAU;gBAAU,aAAa,CAAC,IAAM,EAAE,cAAc;gBAAI,SAAS,IAAM,KAAK;0BAClH,cAAA,8OAAC,2OAAW;oBAAC,WAAU;;;;;;;;;;;0BAEzB,8OAAC,4IAAM;gBAAC,MAAK;gBAAO,SAAQ;gBAAQ,WAAU;gBAAU,aAAa,CAAC,IAAM,EAAE,cAAc;gBAAI,SAAS,IAAM,KAAK;0BAClH,cAAA,8OAAC,sOAAU;oBAAC,WAAU;;;;;;;;;;;0BAGxB,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC,4IAAM;gBAAC,MAAK;gBAAO,SAAQ;gBAAQ,WAAU;gBAAU,aAAa,CAAC,IAAM,EAAE,cAAc;gBAAI,SAAS;oBAAQ,SAAS,WAAW,CAAC,eAAe,OAAO;oBAAQ,KAAK;gBAAwB;0BAC/L,cAAA,8OAAC,0MAAI;oBAAC,WAAU;;;;;;;;;;;0BAElB,8OAAC,4IAAM;gBAAC,MAAK;gBAAO,SAAQ;gBAAQ,WAAU;gBAAU,aAAa,CAAC,IAAM,EAAE,cAAc;gBAAI,SAAS;oBAAQ,SAAS,WAAW,CAAC,eAAe,OAAO;oBAAQ,KAAK;gBAAsB;0BAC7L,cAAA,8OAAC,mOAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI/B", "debugId": null}}, {"offset": {"line": 781, "column": 0}, "map": {"version": 3, "sources": ["file:///T:/Development/TranscriptionModule/transcription-demo/src/app/report-templates/_components/EditorCanvas.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useEffect, useRef, useState } from \"react\";\nimport { Toolbar } from \"./Toolbar\";\nimport { AiBlock as AiBlockCmp } from \"./blocks/AiBlock\";\nimport { CategoryBlock as CategoryBlockCmp } from \"./blocks/CategoryBlock\";\nimport type { Block, DetectionItem, AiBlock, CategoryBlock, TextBlock, FindingBlock } from \"./types\";\n\nexport function EditorCanvas({ blocks, setBlocks, detectionItems }: { blocks: Block[]; setBlocks: (updater: (prev: Block[]) => Block[]) => void; detectionItems: DetectionItem[]; }) {\n  const editorRef = useRef<HTMLDivElement | null>(null);\n  const [isDragging, setIsDragging] = useState(false);\n\n  // Get text content from blocks for the single contentEditable area\n  const textContent = blocks\n    .filter(b => b.type === \"TEXT\")\n    .map(b => (b as TextBlock).content)\n    .join(\"\");\n\n  // Initialize editor with basic content\n  useEffect(() => {\n    const editor = editorRef.current;\n    if (!editor) return;\n\n    // Initialize with basic content if empty\n    if (editor.innerHTML.trim() === \"\" && textContent.trim() === \"\") {\n      editor.innerHTML = \"<p>Start typing your report here...</p>\";\n    } else if (textContent && editor.innerHTML !== textContent) {\n      editor.innerHTML = textContent;\n    }\n\n    // Make inline finding chips draggable\n    const findingChips = editor.querySelectorAll('[data-inline-id]');\n    findingChips.forEach((chip) => {\n      const element = chip as HTMLElement;\n      element.draggable = true;\n      element.addEventListener('dragstart', (e) => {\n        const id = element.getAttribute('data-inline-id');\n        if (id) {\n          e.dataTransfer?.setData('application/x-source-id', id);\n          e.dataTransfer?.setData('text/plain', '');\n        }\n      });\n    });\n  }, [textContent]);\n\n  // Event handlers\n  function handleInput(e: React.FormEvent<HTMLDivElement>) {\n    const editor = editorRef.current;\n    if (!editor) return;\n\n    const content = editor.innerHTML;\n\n    // Update the first TEXT block or create one if none exists\n    setBlocks((prev) => {\n      const textBlocks = prev.filter(b => b.type === \"TEXT\");\n      const otherBlocks = prev.filter(b => b.type !== \"TEXT\");\n\n      if (textBlocks.length === 0) {\n        return [{ id: crypto.randomUUID(), type: \"TEXT\", content } as TextBlock, ...otherBlocks];\n      }\n\n      // Update the first text block\n      const updatedTextBlocks = textBlocks.map((b, i) =>\n        i === 0 ? { ...b, content } as TextBlock : b\n      );\n\n      return [...updatedTextBlocks, ...otherBlocks];\n    });\n  }\n\n  function handleAiInnerChange(id: string, html: string) {\n    setBlocks((prev) => prev.map((b) => (b.id === id && b.type === \"AI_BLOCK\" ? { ...(b as AiBlock), innerContent: html } : b)));\n  }\n\n  function handleCategorySelectionChange(id: string, selected: string[]) {\n    setBlocks((prev) => prev.map((b) => (b.id === id && b.type === \"CATEGORY_BLOCK\" ? { ...(b as CategoryBlock), selectedFindingIds: selected } : b)));\n  }\n\n  function handleDragStart(e: React.DragEvent, id: string) {\n    e.stopPropagation();\n    e.dataTransfer.setData(\"application/x-source-id\", id);\n  }\n\n  function handleDragOver(e: React.DragEvent) {\n    e.preventDefault();\n    e.stopPropagation();\n    e.dataTransfer.dropEffect = \"copy\";\n    setIsDragging(true);\n  }\n\n  function caretRangeAtPoint(e: React.DragEvent): Range | null {\n    const anyDoc = document as any;\n    if (anyDoc.caretRangeFromPoint) return anyDoc.caretRangeFromPoint(e.clientX, e.clientY);\n    const pos = (anyDoc.caretPositionFromPoint?.(e.clientX, e.clientY));\n    if (pos) { const r = document.createRange(); r.setStart(pos.offsetNode, pos.offset); r.collapse(true); return r; }\n    return null;\n  }\n\n  function insertNodeAtRange(range: Range, node: Node) {\n    range.deleteContents();\n    range.insertNode(node);\n    // Move caret after inserted node\n    const sel = window.getSelection();\n    if (sel) { sel.removeAllRanges(); const r = document.createRange(); r.setStartAfter(node); r.collapse(true); sel.addRange(r); }\n  }\n\n  function handleCanvasDrop(e: React.DragEvent<HTMLDivElement>) {\n    e.preventDefault();\n    setIsDragging(false);\n\n    const payloadRaw = e.dataTransfer.getData(\"application/json\");\n    const fromId = e.dataTransfer.getData(\"application/x-source-id\");\n    const data = payloadRaw ? JSON.parse(payloadRaw) : undefined;\n\n    // Find the target text block for inline insertion\n    const target = e.target as HTMLElement;\n    const textBlock = target.closest('[data-block-type=\"TEXT\"]') as HTMLElement;\n\n    if (!textBlock) return; // Only allow drops into text blocks\n\n    // Use native browser caret position from drop event\n    let range: Range | null = null;\n    if ((document as any).caretRangeFromPoint) {\n      range = (document as any).caretRangeFromPoint(e.clientX, e.clientY);\n    } else if ((document as any).caretPositionFromPoint) {\n      const pos = (document as any).caretPositionFromPoint(e.clientX, e.clientY);\n      if (pos) {\n        range = document.createRange();\n        range.setStart(pos.offsetNode, pos.offset);\n        range.collapse(true);\n      }\n    }\n\n    if (!range) return;\n\n    // Handle existing inline finding reordering\n    if (!data && fromId) {\n      const existingChip = editorRef.current?.querySelector(`[data-inline-id=\"${fromId}\"]`);\n      if (existingChip) {\n        existingChip.remove();\n        const newChip = existingChip.cloneNode(true) as HTMLElement;\n        newChip.draggable = true;\n        insertNodeAtRange(range, newChip);\n      }\n      return;\n    }\n\n    if (!data) return;\n\n    const newId = `b_${Date.now()}_${Math.random().toString(36).slice(2)}`;\n\n    if (data.type === \"FINDING_BLOCK\") {\n      // Only findings can be inserted inline\n      const chip = document.createElement(\"span\");\n      chip.setAttribute(\"data-inline-id\", newId);\n      chip.setAttribute(\"contenteditable\", \"false\");\n      chip.className = \"inline-flex items-center px-2 py-1 rounded-full bg-blue-100 text-blue-800 text-xs font-medium mx-1 cursor-grab\";\n      chip.textContent = data.findingName || \"Finding\";\n      chip.draggable = true;\n\n      chip.addEventListener('dragstart', (dragEvent) => {\n        dragEvent.dataTransfer?.setData('application/x-source-id', newId);\n        dragEvent.dataTransfer?.setData('text/plain', '');\n      });\n\n      insertNodeAtRange(range, chip);\n\n      setBlocks((prev) => {\n        const existing = prev.find(b => b.id === newId);\n        if (existing) return prev;\n        return [...prev, { id: newId, type: \"FINDING_BLOCK\", findingId: data.findingId, findingName: data.findingName, selectedValue: \"value\" } as FindingBlock];\n      });\n\n    } else if (data.type === \"AI_BLOCK\" || data.type === \"CATEGORY_BLOCK\") {\n      // Block-level components: insert between text blocks, not inline\n      const textBlockId = textBlock.getAttribute(\"data-block-id\");\n      const textBlockIndex = blocks.findIndex(b => b.id === textBlockId);\n\n      if (textBlockIndex === -1) return;\n\n      // Split the text block at cursor position if there's content after cursor\n      const textContent = textBlock.innerHTML;\n      const tempDiv = document.createElement(\"div\");\n      tempDiv.innerHTML = textContent;\n\n      // Create the new block\n      const newBlock = data.type === \"AI_BLOCK\"\n        ? ({ id: newId, type: \"AI_BLOCK\", innerContent: \"\" } as AiBlock)\n        : ({ id: newId, type: \"CATEGORY_BLOCK\", category: data.category, categoryName: data.categoryName, selectedFindingIds: [] } as CategoryBlock);\n\n      // Insert new block after current text block, and add a new text block after it\n      setBlocks((prev) => {\n        const newBlocks = [...prev];\n        newBlocks.splice(textBlockIndex + 1, 0,\n          newBlock,\n          { id: crypto.randomUUID(), type: \"TEXT\", content: \"<p></p>\" } as TextBlock\n        );\n        return newBlocks;\n      });\n    }\n  }\n\n  function handleBlockDrop(e: React.DragEvent, targetId: string) {\n    e.preventDefault(); e.stopPropagation();\n    const fromId = e.dataTransfer.getData(\"application/x-source-id\");\n    if (!fromId || fromId === targetId) return;\n    setBlocks((prev) => {\n      const arr = [...prev];\n      const si = arr.findIndex((b) => b.id === fromId);\n      const ti = arr.findIndex((b) => b.id === targetId);\n      if (si < 0 || ti < 0) return prev;\n      const [m] = arr.splice(si, 1);\n      arr.splice(ti, 0, m);\n      return arr;\n    });\n  }\n\n  function handleDropIntoInner(e: React.DragEvent<HTMLDivElement>, parentId: string) {\n    e.preventDefault(); e.stopPropagation();\n    const payloadRaw = e.dataTransfer.getData(\"application/json\");\n    if (!payloadRaw) return;\n    const data = JSON.parse(payloadRaw) as any;\n    if (data.type !== \"FINDING_BLOCK\") return;\n    const range = caretRangeAtPoint(e);\n    if (!range) return;\n    const span = document.createElement(\"span\");\n    const id = `b_${Date.now()}_${Math.random().toString(36).slice(2)}`;\n    span.setAttribute(\"data-inline-id\", id);\n    span.setAttribute(\"contenteditable\", \"false\");\n    insertNodeAtRange(range, span);\n\n    // Update parent's HTML and register block for inline mount\n    const host = e.currentTarget as HTMLDivElement;\n    const html = host.innerHTML;\n    setBlocks((prev) => {\n      const next = prev.map((b) => (b.id === parentId && b.type === \"AI_BLOCK\" ? { ...(b as AiBlock), innerContent: html } : b));\n      next.push({ id, type: \"FINDING_BLOCK\", findingId: data.findingId, findingName: data.findingName, selectedValue: \"value\" } as FindingBlock);\n      return next;\n    });\n  }\n\n  return (\n    <div className=\"flex flex-col h-full bg-background rounded-md border p-3\">\n      <Toolbar />\n      <div className=\"relative flex-1\">\n        <div\n          ref={editorRef}\n          className={`flex-1 p-4 min-h-[400px] overflow-auto focus:outline-none ${isDragging ? 'bg-blue-50' : ''}`}\n          onDrop={handleCanvasDrop}\n          onDragOver={handleDragOver}\n          onDragLeave={() => setIsDragging(false)}\n        />\n      </div>\n    </div>\n  );\n}\n\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAQO,SAAS,aAAa,EAAE,MAAM,EAAE,SAAS,EAAE,cAAc,EAAmH;IACjL,MAAM,YAAY,IAAA,+MAAM,EAAwB;IAChD,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,iNAAQ,EAAC;IAE7C,mEAAmE;IACnE,MAAM,cAAc,OACjB,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,QACvB,GAAG,CAAC,CAAA,IAAK,AAAC,EAAgB,OAAO,EACjC,IAAI,CAAC;IAER,uCAAuC;IACvC,IAAA,kNAAS,EAAC;QACR,MAAM,SAAS,UAAU,OAAO;QAChC,IAAI,CAAC,QAAQ;QAEb,yCAAyC;QACzC,IAAI,OAAO,SAAS,CAAC,IAAI,OAAO,MAAM,YAAY,IAAI,OAAO,IAAI;YAC/D,OAAO,SAAS,GAAG;QACrB,OAAO,IAAI,eAAe,OAAO,SAAS,KAAK,aAAa;YAC1D,OAAO,SAAS,GAAG;QACrB;QAEA,sCAAsC;QACtC,MAAM,eAAe,OAAO,gBAAgB,CAAC;QAC7C,aAAa,OAAO,CAAC,CAAC;YACpB,MAAM,UAAU;YAChB,QAAQ,SAAS,GAAG;YACpB,QAAQ,gBAAgB,CAAC,aAAa,CAAC;gBACrC,MAAM,KAAK,QAAQ,YAAY,CAAC;gBAChC,IAAI,IAAI;oBACN,EAAE,YAAY,EAAE,QAAQ,2BAA2B;oBACnD,EAAE,YAAY,EAAE,QAAQ,cAAc;gBACxC;YACF;QACF;IACF,GAAG;QAAC;KAAY;IAEhB,iBAAiB;IACjB,SAAS,YAAY,CAAkC;QACrD,MAAM,SAAS,UAAU,OAAO;QAChC,IAAI,CAAC,QAAQ;QAEb,MAAM,UAAU,OAAO,SAAS;QAEhC,2DAA2D;QAC3D,UAAU,CAAC;YACT,MAAM,aAAa,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK;YAC/C,MAAM,cAAc,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK;YAEhD,IAAI,WAAW,MAAM,KAAK,GAAG;gBAC3B,OAAO;oBAAC;wBAAE,IAAI,OAAO,UAAU;wBAAI,MAAM;wBAAQ;oBAAQ;uBAAmB;iBAAY;YAC1F;YAEA,8BAA8B;YAC9B,MAAM,oBAAoB,WAAW,GAAG,CAAC,CAAC,GAAG,IAC3C,MAAM,IAAI;oBAAE,GAAG,CAAC;oBAAE;gBAAQ,IAAiB;YAG7C,OAAO;mBAAI;mBAAsB;aAAY;QAC/C;IACF;IAEA,SAAS,oBAAoB,EAAU,EAAE,IAAY;QACnD,UAAU,CAAC,OAAS,KAAK,GAAG,CAAC,CAAC,IAAO,EAAE,EAAE,KAAK,MAAM,EAAE,IAAI,KAAK,aAAa;oBAAE,GAAI,CAAC;oBAAc,cAAc;gBAAK,IAAI;IAC1H;IAEA,SAAS,8BAA8B,EAAU,EAAE,QAAkB;QACnE,UAAU,CAAC,OAAS,KAAK,GAAG,CAAC,CAAC,IAAO,EAAE,EAAE,KAAK,MAAM,EAAE,IAAI,KAAK,mBAAmB;oBAAE,GAAI,CAAC;oBAAoB,oBAAoB;gBAAS,IAAI;IAChJ;IAEA,SAAS,gBAAgB,CAAkB,EAAE,EAAU;QACrD,EAAE,eAAe;QACjB,EAAE,YAAY,CAAC,OAAO,CAAC,2BAA2B;IACpD;IAEA,SAAS,eAAe,CAAkB;QACxC,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,EAAE,YAAY,CAAC,UAAU,GAAG;QAC5B,cAAc;IAChB;IAEA,SAAS,kBAAkB,CAAkB;QAC3C,MAAM,SAAS;QACf,IAAI,OAAO,mBAAmB,EAAE,OAAO,OAAO,mBAAmB,CAAC,EAAE,OAAO,EAAE,EAAE,OAAO;QACtF,MAAM,MAAO,OAAO,sBAAsB,GAAG,EAAE,OAAO,EAAE,EAAE,OAAO;QACjE,IAAI,KAAK;YAAE,MAAM,IAAI,SAAS,WAAW;YAAI,EAAE,QAAQ,CAAC,IAAI,UAAU,EAAE,IAAI,MAAM;YAAG,EAAE,QAAQ,CAAC;YAAO,OAAO;QAAG;QACjH,OAAO;IACT;IAEA,SAAS,kBAAkB,KAAY,EAAE,IAAU;QACjD,MAAM,cAAc;QACpB,MAAM,UAAU,CAAC;QACjB,iCAAiC;QACjC,MAAM,MAAM,OAAO,YAAY;QAC/B,IAAI,KAAK;YAAE,IAAI,eAAe;YAAI,MAAM,IAAI,SAAS,WAAW;YAAI,EAAE,aAAa,CAAC;YAAO,EAAE,QAAQ,CAAC;YAAO,IAAI,QAAQ,CAAC;QAAI;IAChI;IAEA,SAAS,iBAAiB,CAAkC;QAC1D,EAAE,cAAc;QAChB,cAAc;QAEd,MAAM,aAAa,EAAE,YAAY,CAAC,OAAO,CAAC;QAC1C,MAAM,SAAS,EAAE,YAAY,CAAC,OAAO,CAAC;QACtC,MAAM,OAAO,aAAa,KAAK,KAAK,CAAC,cAAc;QAEnD,kDAAkD;QAClD,MAAM,SAAS,EAAE,MAAM;QACvB,MAAM,YAAY,OAAO,OAAO,CAAC;QAEjC,IAAI,CAAC,WAAW,QAAQ,oCAAoC;QAE5D,oDAAoD;QACpD,IAAI,QAAsB;QAC1B,IAAI,AAAC,SAAiB,mBAAmB,EAAE;YACzC,QAAQ,AAAC,SAAiB,mBAAmB,CAAC,EAAE,OAAO,EAAE,EAAE,OAAO;QACpE,OAAO,IAAI,AAAC,SAAiB,sBAAsB,EAAE;YACnD,MAAM,MAAM,AAAC,SAAiB,sBAAsB,CAAC,EAAE,OAAO,EAAE,EAAE,OAAO;YACzE,IAAI,KAAK;gBACP,QAAQ,SAAS,WAAW;gBAC5B,MAAM,QAAQ,CAAC,IAAI,UAAU,EAAE,IAAI,MAAM;gBACzC,MAAM,QAAQ,CAAC;YACjB;QACF;QAEA,IAAI,CAAC,OAAO;QAEZ,4CAA4C;QAC5C,IAAI,CAAC,QAAQ,QAAQ;YACnB,MAAM,eAAe,UAAU,OAAO,EAAE,cAAc,CAAC,iBAAiB,EAAE,OAAO,EAAE,CAAC;YACpF,IAAI,cAAc;gBAChB,aAAa,MAAM;gBACnB,MAAM,UAAU,aAAa,SAAS,CAAC;gBACvC,QAAQ,SAAS,GAAG;gBACpB,kBAAkB,OAAO;YAC3B;YACA;QACF;QAEA,IAAI,CAAC,MAAM;QAEX,MAAM,QAAQ,CAAC,EAAE,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,KAAK,CAAC,IAAI;QAEtE,IAAI,KAAK,IAAI,KAAK,iBAAiB;YACjC,uCAAuC;YACvC,MAAM,OAAO,SAAS,aAAa,CAAC;YACpC,KAAK,YAAY,CAAC,kBAAkB;YACpC,KAAK,YAAY,CAAC,mBAAmB;YACrC,KAAK,SAAS,GAAG;YACjB,KAAK,WAAW,GAAG,KAAK,WAAW,IAAI;YACvC,KAAK,SAAS,GAAG;YAEjB,KAAK,gBAAgB,CAAC,aAAa,CAAC;gBAClC,UAAU,YAAY,EAAE,QAAQ,2BAA2B;gBAC3D,UAAU,YAAY,EAAE,QAAQ,cAAc;YAChD;YAEA,kBAAkB,OAAO;YAEzB,UAAU,CAAC;gBACT,MAAM,WAAW,KAAK,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBACzC,IAAI,UAAU,OAAO;gBACrB,OAAO;uBAAI;oBAAM;wBAAE,IAAI;wBAAO,MAAM;wBAAiB,WAAW,KAAK,SAAS;wBAAE,aAAa,KAAK,WAAW;wBAAE,eAAe;oBAAQ;iBAAkB;YAC1J;QAEF,OAAO,IAAI,KAAK,IAAI,KAAK,cAAc,KAAK,IAAI,KAAK,kBAAkB;YACrE,iEAAiE;YACjE,MAAM,cAAc,UAAU,YAAY,CAAC;YAC3C,MAAM,iBAAiB,OAAO,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YAEtD,IAAI,mBAAmB,CAAC,GAAG;YAE3B,0EAA0E;YAC1E,MAAM,cAAc,UAAU,SAAS;YACvC,MAAM,UAAU,SAAS,aAAa,CAAC;YACvC,QAAQ,SAAS,GAAG;YAEpB,uBAAuB;YACvB,MAAM,WAAW,KAAK,IAAI,KAAK,aAC1B;gBAAE,IAAI;gBAAO,MAAM;gBAAY,cAAc;YAAG,IAChD;gBAAE,IAAI;gBAAO,MAAM;gBAAkB,UAAU,KAAK,QAAQ;gBAAE,cAAc,KAAK,YAAY;gBAAE,oBAAoB,EAAE;YAAC;YAE3H,+EAA+E;YAC/E,UAAU,CAAC;gBACT,MAAM,YAAY;uBAAI;iBAAK;gBAC3B,UAAU,MAAM,CAAC,iBAAiB,GAAG,GACnC,UACA;oBAAE,IAAI,OAAO,UAAU;oBAAI,MAAM;oBAAQ,SAAS;gBAAU;gBAE9D,OAAO;YACT;QACF;IACF;IAEA,SAAS,gBAAgB,CAAkB,EAAE,QAAgB;QAC3D,EAAE,cAAc;QAAI,EAAE,eAAe;QACrC,MAAM,SAAS,EAAE,YAAY,CAAC,OAAO,CAAC;QACtC,IAAI,CAAC,UAAU,WAAW,UAAU;QACpC,UAAU,CAAC;YACT,MAAM,MAAM;mBAAI;aAAK;YACrB,MAAM,KAAK,IAAI,SAAS,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;YACzC,MAAM,KAAK,IAAI,SAAS,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;YACzC,IAAI,KAAK,KAAK,KAAK,GAAG,OAAO;YAC7B,MAAM,CAAC,EAAE,GAAG,IAAI,MAAM,CAAC,IAAI;YAC3B,IAAI,MAAM,CAAC,IAAI,GAAG;YAClB,OAAO;QACT;IACF;IAEA,SAAS,oBAAoB,CAAkC,EAAE,QAAgB;QAC/E,EAAE,cAAc;QAAI,EAAE,eAAe;QACrC,MAAM,aAAa,EAAE,YAAY,CAAC,OAAO,CAAC;QAC1C,IAAI,CAAC,YAAY;QACjB,MAAM,OAAO,KAAK,KAAK,CAAC;QACxB,IAAI,KAAK,IAAI,KAAK,iBAAiB;QACnC,MAAM,QAAQ,kBAAkB;QAChC,IAAI,CAAC,OAAO;QACZ,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,MAAM,KAAK,CAAC,EAAE,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,KAAK,CAAC,IAAI;QACnE,KAAK,YAAY,CAAC,kBAAkB;QACpC,KAAK,YAAY,CAAC,mBAAmB;QACrC,kBAAkB,OAAO;QAEzB,2DAA2D;QAC3D,MAAM,OAAO,EAAE,aAAa;QAC5B,MAAM,OAAO,KAAK,SAAS;QAC3B,UAAU,CAAC;YACT,MAAM,OAAO,KAAK,GAAG,CAAC,CAAC,IAAO,EAAE,EAAE,KAAK,YAAY,EAAE,IAAI,KAAK,aAAa;oBAAE,GAAI,CAAC;oBAAc,cAAc;gBAAK,IAAI;YACvH,KAAK,IAAI,CAAC;gBAAE;gBAAI,MAAM;gBAAiB,WAAW,KAAK,SAAS;gBAAE,aAAa,KAAK,WAAW;gBAAE,eAAe;YAAQ;YACxH,OAAO;QACT;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,uKAAO;;;;;0BACR,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,KAAK;oBACL,WAAW,CAAC,0DAA0D,EAAE,aAAa,eAAe,IAAI;oBACxG,QAAQ;oBACR,YAAY;oBACZ,aAAa,IAAM,cAAc;;;;;;;;;;;;;;;;;AAK3C", "debugId": null}}, {"offset": {"line": 1084, "column": 0}, "map": {"version": 3, "sources": ["file:///T:/Development/TranscriptionModule/transcription-demo/src/app/report-templates/_components/BlockSidebar.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useMemo, useState } from \"react\";\nimport { Brain, LayoutGrid, List, Search, ChevronDown } from \"lucide-react\";\nimport type { DetectionItem } from \"./types\";\n\nexport function BlockSidebar({ detectionItems }: { detectionItems: DetectionItem[] }) {\n  const [q, setQ] = useState(\"\");\n  const [cat, setCat] = useState(\"all\");\n  const [open, setOpen] = useState<{general: boolean; categories: boolean; findings: boolean}>({ general: true, categories: true, findings: true });\n\n  const categories = useMemo(() => Array.from(new Set(detectionItems.map((d) => d.category))), [detectionItems]);\n  const filtered = useMemo(\n    () => detectionItems.filter((d) => (cat === \"all\" || d.category === cat) && d.name.toLowerCase().includes(q.toLowerCase())),\n    [detectionItems, q, cat]\n  );\n\n  function onDragStart(e: React.DragEvent, type: string, data: Record<string, unknown>) {\n    e.dataTransfer.effectAllowed = \"copy\";\n    e.dataTransfer.setData(\"application/json\", JSON.stringify({ type, ...data }));\n  }\n\n  return (\n    <div className=\"w-80 flex-shrink-0 border-l bg-muted/20 h-full flex flex-col\">\n      <h3 className=\"text-sm font-semibold px-4 py-3 border-b\">Add Blocks</h3>\n      <div className=\"flex-1 overflow-y-auto sidebar-scrollbar\">\n        <div className=\"border-b\">\n          <button className=\"w-full flex items-center justify-between px-3 py-2 text-xs uppercase text-muted-foreground\" onClick={() => setOpen((o) => ({ ...o, general: !o.general }))}>\n            <span>General</span><ChevronDown size={14} className={`transition-transform ${open.general ? \"rotate-180\" : \"\"}`} />\n          </button>\n          {open.general && (\n            <div className=\"px-3 pb-3\">\n              <div\n                draggable\n                onDragStart={(e) => onDragStart(e, \"AI_BLOCK\", {})}\n                className=\"p-2 rounded-lg border-2 border-orange-400 bg-orange-50 text-orange-700 flex items-center gap-2 cursor-grab\"\n              >\n                <Brain size={16} /> <span className=\"text-sm font-medium\">AI Instruction</span>\n              </div>\n            </div>\n          )}\n        </div>\n\n        <div className=\"border-b\">\n          <button className=\"w-full flex items-center justify-between px-3 py-2 text-xs uppercase text-muted-foreground\" onClick={() => setOpen((o) => ({ ...o, categories: !o.categories }))}>\n            <span>Categories</span><ChevronDown size={14} className={`transition-transform ${open.categories ? \"rotate-180\" : \"\"}`} />\n          </button>\n          {open.categories && (\n            <div className=\"px-3 pb-3 space-y-2\">\n              {categories.map((c) => (\n                <div key={c}\n                  draggable\n                  onDragStart={(e) => onDragStart(e, \"CATEGORY_BLOCK\", { category: c, categoryName: c })}\n                  className=\"p-2 rounded-lg border-2 border-border bg-muted/40 text-foreground flex items-center gap-2 cursor-grab\"\n                >\n                  <LayoutGrid size={16} /> <span className=\"text-sm font-medium\">{c}</span>\n                </div>\n              ))}\n            </div>\n          )}\n        </div>\n\n        <div>\n          <button className=\"w-full flex items-center justify-between px-3 py-2 text-xs uppercase text-muted-foreground\" onClick={() => setOpen((o) => ({ ...o, findings: !o.findings }))}>\n            <span>Individual Findings</span><ChevronDown size={14} className={`transition-transform ${open.findings ? \"rotate-180\" : \"\"}`} />\n          </button>\n          {open.findings && (\n            <div className=\"px-3 pb-3 space-y-2\">\n              <div className=\"relative\">\n                <Search size={14} className=\"absolute left-2 top-1/2 -translate-y-1/2 text-muted-foreground\" />\n                <input value={q} onChange={(e) => setQ(e.target.value)} placeholder=\"Search findings...\" className=\"w-full pl-7 pr-2 py-2 border rounded-md text-sm\" />\n              </div>\n              <select value={cat} onChange={(e) => setCat(e.target.value)} className=\"w-full p-2 border rounded-md text-sm\">\n                <option value=\"all\">All Categories</option>\n                {categories.map((c) => (<option key={c} value={c}>{c}</option>))}\n              </select>\n              <div className=\"max-h-[300px] overflow-y-auto sidebar-scrollbar pr-1 space-y-2\">\n                {filtered.map((it) => (\n                  <div key={it.id}\n                    draggable\n                    onDragStart={(e) => onDragStart(e, \"FINDING_BLOCK\", { findingId: it.id, findingName: it.name })}\n                    className=\"p-2 rounded-lg border-2 border-blue-400 bg-blue-50 text-blue-700 flex items-center gap-2 cursor-grab\"\n                  >\n                    <List size={16} /> <span className=\"text-sm font-medium\">{it.name}</span>\n                  </div>\n                ))}\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAMO,SAAS,aAAa,EAAE,cAAc,EAAuC;IAClF,MAAM,CAAC,GAAG,KAAK,GAAG,IAAA,iNAAQ,EAAC;IAC3B,MAAM,CAAC,KAAK,OAAO,GAAG,IAAA,iNAAQ,EAAC;IAC/B,MAAM,CAAC,MAAM,QAAQ,GAAG,IAAA,iNAAQ,EAA6D;QAAE,SAAS;QAAM,YAAY;QAAM,UAAU;IAAK;IAE/I,MAAM,aAAa,IAAA,gNAAO,EAAC,IAAM,MAAM,IAAI,CAAC,IAAI,IAAI,eAAe,GAAG,CAAC,CAAC,IAAM,EAAE,QAAQ,KAAK;QAAC;KAAe;IAC7G,MAAM,WAAW,IAAA,gNAAO,EACtB,IAAM,eAAe,MAAM,CAAC,CAAC,IAAM,CAAC,QAAQ,SAAS,EAAE,QAAQ,KAAK,GAAG,KAAK,EAAE,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,EAAE,WAAW,MACvH;QAAC;QAAgB;QAAG;KAAI;IAG1B,SAAS,YAAY,CAAkB,EAAE,IAAY,EAAE,IAA6B;QAClF,EAAE,YAAY,CAAC,aAAa,GAAG;QAC/B,EAAE,YAAY,CAAC,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC;YAAE;YAAM,GAAG,IAAI;QAAC;IAC5E;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BAA2C;;;;;;0BACzD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAO,WAAU;gCAA6F,SAAS,IAAM,QAAQ,CAAC,IAAM,CAAC;4CAAE,GAAG,CAAC;4CAAE,SAAS,CAAC,EAAE,OAAO;wCAAC,CAAC;;kDACzK,8OAAC;kDAAK;;;;;;kDAAc,8OAAC,mOAAW;wCAAC,MAAM;wCAAI,WAAW,CAAC,qBAAqB,EAAE,KAAK,OAAO,GAAG,eAAe,IAAI;;;;;;;;;;;;4BAEjH,KAAK,OAAO,kBACX,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,SAAS;oCACT,aAAa,CAAC,IAAM,YAAY,GAAG,YAAY,CAAC;oCAChD,WAAU;;sDAEV,8OAAC,6MAAK;4CAAC,MAAM;;;;;;wCAAM;sDAAC,8OAAC;4CAAK,WAAU;sDAAsB;;;;;;;;;;;;;;;;;;;;;;;kCAMlE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAO,WAAU;gCAA6F,SAAS,IAAM,QAAQ,CAAC,IAAM,CAAC;4CAAE,GAAG,CAAC;4CAAE,YAAY,CAAC,EAAE,UAAU;wCAAC,CAAC;;kDAC/K,8OAAC;kDAAK;;;;;;kDAAiB,8OAAC,mOAAW;wCAAC,MAAM;wCAAI,WAAW,CAAC,qBAAqB,EAAE,KAAK,UAAU,GAAG,eAAe,IAAI;;;;;;;;;;;;4BAEvH,KAAK,UAAU,kBACd,8OAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC,kBACf,8OAAC;wCACC,SAAS;wCACT,aAAa,CAAC,IAAM,YAAY,GAAG,kBAAkB;gDAAE,UAAU;gDAAG,cAAc;4CAAE;wCACpF,WAAU;;0DAEV,8OAAC,gOAAU;gDAAC,MAAM;;;;;;4CAAM;0DAAC,8OAAC;gDAAK,WAAU;0DAAuB;;;;;;;uCALxD;;;;;;;;;;;;;;;;kCAYlB,8OAAC;;0CACC,8OAAC;gCAAO,WAAU;gCAA6F,SAAS,IAAM,QAAQ,CAAC,IAAM,CAAC;4CAAE,GAAG,CAAC;4CAAE,UAAU,CAAC,EAAE,QAAQ;wCAAC,CAAC;;kDAC3K,8OAAC;kDAAK;;;;;;kDAA0B,8OAAC,mOAAW;wCAAC,MAAM;wCAAI,WAAW,CAAC,qBAAqB,EAAE,KAAK,QAAQ,GAAG,eAAe,IAAI;;;;;;;;;;;;4BAE9H,KAAK,QAAQ,kBACZ,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,gNAAM;gDAAC,MAAM;gDAAI,WAAU;;;;;;0DAC5B,8OAAC;gDAAM,OAAO;gDAAG,UAAU,CAAC,IAAM,KAAK,EAAE,MAAM,CAAC,KAAK;gDAAG,aAAY;gDAAqB,WAAU;;;;;;;;;;;;kDAErG,8OAAC;wCAAO,OAAO;wCAAK,UAAU,CAAC,IAAM,OAAO,EAAE,MAAM,CAAC,KAAK;wCAAG,WAAU;;0DACrE,8OAAC;gDAAO,OAAM;0DAAM;;;;;;4CACnB,WAAW,GAAG,CAAC,CAAC,kBAAO,8OAAC;oDAAe,OAAO;8DAAI;mDAAd;;;;;;;;;;;kDAEvC,8OAAC;wCAAI,WAAU;kDACZ,SAAS,GAAG,CAAC,CAAC,mBACb,8OAAC;gDACC,SAAS;gDACT,aAAa,CAAC,IAAM,YAAY,GAAG,iBAAiB;wDAAE,WAAW,GAAG,EAAE;wDAAE,aAAa,GAAG,IAAI;oDAAC;gDAC7F,WAAU;;kEAEV,8OAAC,0MAAI;wDAAC,MAAM;;;;;;oDAAM;kEAAC,8OAAC;wDAAK,WAAU;kEAAuB,GAAG,IAAI;;;;;;;+CALzD,GAAG,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAejC", "debugId": null}}, {"offset": {"line": 1434, "column": 0}, "map": {"version": 3, "sources": ["file:///T:/Development/TranscriptionModule/transcription-demo/src/app/report-templates/_components/ReportPreview.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport { Button } from \"@/components/ui/button\";\nimport type { AutoDetectedFinding, Block, DetectionItem } from \"./types\";\n\nexport function ReportPreview({ blocks, detectionItems }: { blocks: Block[]; detectionItems: DetectionItem[] }) {\n  const [report, setReport] = useState<string>(\"\");\n  const [loading, setLoading] = useState(false);\n\n  function valueForFinding(id: string): string {\n    // Demo resolver: just return placeholder or the finding name\n    const meta = detectionItems.find((d) => d.id === id);\n    return meta ? `[${meta.name} value]` : \"[value]\";\n  }\n\n  async function generate() {\n    setLoading(true);\n    let content = \"\";\n    for (const b of blocks) {\n      if (b.type === \"TEXT\") {\n        const div = document.createElement(\"div\");\n        div.innerHTML = b.content;\n        content += div.innerText + \"\\n\";\n      } else if (b.type === \"AI_BLOCK\") {\n        content += `\\n--- AI Summary for \"${stripHtml(b.innerContent).slice(0, 80)}\" ---\\n[Simulating AI response...]\\n\\n`;\n      } else if (b.type === \"FINDING_BLOCK\") {\n        content += ` ${valueForFinding(b.findingId)} `;\n      } else if (b.type === \"CATEGORY_BLOCK\") {\n        content += `\\n**${b.categoryName}**\\n`;\n        for (const id of b.selectedFindingIds) {\n          const meta = detectionItems.find((d) => d.id === id);\n          content += `- ${meta?.name ?? id}: ${valueForFinding(id)}\\n`;\n        }\n        content += \"\\n\";\n      }\n    }\n    await new Promise((r) => setTimeout(r, 500));\n    setReport(content.replace(\"[Simulating AI response...]\", \"Based on findings, values are within expected ranges.\"));\n    setLoading(false);\n  }\n\n  return (\n    <div className=\"flex flex-col h-full\">\n      <div className=\"flex items-center justify-between gap-2 mb-2\">\n        <Button onClick={generate} disabled={loading || blocks.length === 0}>{loading ? \"Generating...\" : \"Generate Report\"}</Button>\n      </div>\n      <div className=\"flex-1 bg-muted/30 rounded-md p-3 whitespace-pre-wrap font-mono text-xs overflow-auto\">\n        {report || \"Report preview will appear here...\"}\n      </div>\n    </div>\n  );\n}\n\nfunction stripHtml(html: string) {\n  const d = document.createElement(\"div\"); d.innerHTML = html; return d.innerText;\n}\n\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAMO,SAAS,cAAc,EAAE,MAAM,EAAE,cAAc,EAAwD;IAC5G,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,iNAAQ,EAAS;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,iNAAQ,EAAC;IAEvC,SAAS,gBAAgB,EAAU;QACjC,6DAA6D;QAC7D,MAAM,OAAO,eAAe,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;QACjD,OAAO,OAAO,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,OAAO,CAAC,GAAG;IACzC;IAEA,eAAe;QACb,WAAW;QACX,IAAI,UAAU;QACd,KAAK,MAAM,KAAK,OAAQ;YACtB,IAAI,EAAE,IAAI,KAAK,QAAQ;gBACrB,MAAM,MAAM,SAAS,aAAa,CAAC;gBACnC,IAAI,SAAS,GAAG,EAAE,OAAO;gBACzB,WAAW,IAAI,SAAS,GAAG;YAC7B,OAAO,IAAI,EAAE,IAAI,KAAK,YAAY;gBAChC,WAAW,CAAC,sBAAsB,EAAE,UAAU,EAAE,YAAY,EAAE,KAAK,CAAC,GAAG,IAAI,sCAAsC,CAAC;YACpH,OAAO,IAAI,EAAE,IAAI,KAAK,iBAAiB;gBACrC,WAAW,CAAC,CAAC,EAAE,gBAAgB,EAAE,SAAS,EAAE,CAAC,CAAC;YAChD,OAAO,IAAI,EAAE,IAAI,KAAK,kBAAkB;gBACtC,WAAW,CAAC,IAAI,EAAE,EAAE,YAAY,CAAC,IAAI,CAAC;gBACtC,KAAK,MAAM,MAAM,EAAE,kBAAkB,CAAE;oBACrC,MAAM,OAAO,eAAe,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;oBACjD,WAAW,CAAC,EAAE,EAAE,MAAM,QAAQ,GAAG,EAAE,EAAE,gBAAgB,IAAI,EAAE,CAAC;gBAC9D;gBACA,WAAW;YACb;QACF;QACA,MAAM,IAAI,QAAQ,CAAC,IAAM,WAAW,GAAG;QACvC,UAAU,QAAQ,OAAO,CAAC,+BAA+B;QACzD,WAAW;IACb;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,4IAAM;oBAAC,SAAS;oBAAU,UAAU,WAAW,OAAO,MAAM,KAAK;8BAAI,UAAU,kBAAkB;;;;;;;;;;;0BAEpG,8OAAC;gBAAI,WAAU;0BACZ,UAAU;;;;;;;;;;;;AAInB;AAEA,SAAS,UAAU,IAAY;IAC7B,MAAM,IAAI,SAAS,aAAa,CAAC;IAAQ,EAAE,SAAS,GAAG;IAAM,OAAO,EAAE,SAAS;AACjF", "debugId": null}}, {"offset": {"line": 1521, "column": 0}, "map": {"version": 3, "sources": ["file:///T:/Development/TranscriptionModule/transcription-demo/src/app/report-templates/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useEffect, useMemo, useState } from \"react\";\nimport { WorkspaceNav } from \"@/components/workspace-nav\";\nimport { Button } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\";\nimport { getConvex } from \"@/lib/convexClient\";\n\nimport { EditorCanvas } from \"./_components/EditorCanvas\";\nimport { BlockSidebar } from \"./_components/BlockSidebar\";\nimport { ReportPreview } from \"./_components/ReportPreview\";\nimport type { Block, DetectionItem } from \"./_components/types\";\n\nexport default function ReportTemplatesPage() {\n  const [title, setTitle] = useState(\"Consultation Report\");\n  const [blocks, setBlocks] = useState<Block[]>([\n    { id: crypto.randomUUID(), type: \"TEXT\", content: \"<h2>Consultation Report</h2><div>Start typing your report here...</div>\" },\n  ]);\n  const [detectionItems, setDetectionItems] = useState<DetectionItem[]>([]);\n  const [previewOpen, setPreviewOpen] = useState(false);\n\n  const convex = getConvex();\n  const loose = convex as unknown as { query: (name: string, args?: unknown) => Promise<unknown>; mutation: (name: string, args?: unknown) => Promise<unknown>; action: (name: string, args?: unknown) => Promise<unknown>; };\n\n  // Template management\n  type RawTemplate = { _id?: string; id?: string; title?: string; blocks?: unknown[]; published?: boolean; ownerUserId?: string; updatedAt?: number };\n  const [templates, setTemplates] = useState<Array<{ id?: string; title: string; blocks: Block[] }>>([]);\n  const [currentId, setCurrentId] = useState<string | undefined>(undefined);\n\n  function normalizeBlocks(rawBlocks: unknown[] | undefined): Block[] {\n    if (!rawBlocks) return [];\n    return rawBlocks.map((rb) => {\n      const any = rb as any;\n      if (any && typeof any === \"object\" && typeof any.type === \"string\") return any as Block;\n      // Convert prior editor shapes to TEXT for compatibility\n      if (any.kind === \"text\") return { id: crypto.randomUUID(), type: \"TEXT\", content: (any.value ?? \"\") as string } as Block;\n      if (any.kind === \"section\") return { id: crypto.randomUUID(), type: \"TEXT\", content: `<h3>${any.value ?? \"\"}</h3>` } as Block;\n      if (any.kind === \"field\") return { id: crypto.randomUUID(), type: \"TEXT\", content: `<p><strong>${any.label ?? any.value ?? \"Field\"}</strong>: ______</p>` } as Block;\n      return { id: crypto.randomUUID(), type: \"TEXT\", content: \"\" } as Block;\n    });\n  }\n\n  function normalizeTemplate(raw: unknown) {\n    const r = raw as RawTemplate;\n    const id = r.id ?? (r._id ? String(r._id) : undefined);\n    return { id, title: r.title ?? \"Untitled\", blocks: normalizeBlocks(r.blocks) } as { id?: string; title: string; blocks: Block[] };\n  }\n\n  // Load templates from Convex\n  useEffect(() => {\n    let ignore = false;\n    async function loadTemplates() {\n      if (!convex) return;\n      try {\n        const list = await (loose.query(\"templates:listTemplates\", {}) as Promise<unknown>);\n        const items = (list as unknown[]).map(normalizeTemplate);\n        if (!ignore) {\n          setTemplates(items);\n          const first = items[0];\n          if (first) { setCurrentId(first.id); setTitle(first.title); setBlocks(first.blocks); }\n        }\n      } catch {}\n    }\n    loadTemplates();\n    return () => { ignore = true; };\n  }, [convex, loose]);\n\n  // Load available definitions for sidebar (Convex)\n  useEffect(() => {\n    let cancelled = false;\n    async function load() {\n      if (!convex) return;\n      try {\n        const res = await loose.query(\"detections:listDefinitions\", {});\n        const items = (res as unknown[]).map((r) => {\n          const doc = r as { id?: string; _id?: string; name?: string; category?: string };\n          const id = doc.id ?? (doc._id ? String(doc._id) : crypto.randomUUID());\n          return { id, name: doc.name ?? \"\", category: doc.category ?? \"Uncategorized\" } as DetectionItem;\n        });\n        if (!cancelled) setDetectionItems(items);\n      } catch {}\n    }\n    load();\n    return () => { cancelled = true; };\n  }, [convex, loose]);\n\n  const leftPanel = (\n    <div className=\"flex flex-col h-[calc(100vh-8rem)]\">\n      {/* Editor Panel (expanded by default, collapses when preview is open) */}\n      {!previewOpen ? (\n        <div className=\"flex-1 flex flex-col gap-3\">\n          <div className=\"flex items-center justify-between gap-2\">\n            <Input value={title} onChange={(e) => setTitle(e.target.value)} className=\"h-9\" />\n          </div>\n          <EditorCanvas blocks={blocks} setBlocks={(updater) => setBlocks((prev) => updater(prev))} detectionItems={detectionItems} />\n        </div>\n      ) : (\n        <div className=\"shrink-0 border rounded-md px-3 h-10 flex items-center justify-between\">\n          <div className=\"text-sm text-muted-foreground truncate\">(v) Hide Preview • {title}</div>\n          <Button variant=\"ghost\" size=\"sm\" onClick={() => setPreviewOpen(false)}>Hide Preview</Button>\n        </div>\n      )}\n\n      {/* Preview Panel (collapsed footer by default) */}\n      {!previewOpen ? (\n        <div className=\"shrink-0 border rounded-md px-3 py-2 mt-3 flex items-center justify-between bg-muted/20\">\n          <div className=\"text-sm text-muted-foreground\">(^) Show Preview</div>\n          <div className=\"flex items-center gap-2\">\n            <Button variant=\"secondary\" onClick={() => setPreviewOpen(true)}>Show Preview</Button>\n          </div>\n        </div>\n      ) : (\n        <div className=\"flex-1 mt-3 min-h-0\">\n          <ReportPreview blocks={blocks} detectionItems={detectionItems} />\n        </div>\n      )}\n    </div>\n  );\n\n  return (\n    <div className=\"container mx-auto p-6 space-y-4\">\n      <WorkspaceNav />\n      <style>{`[data-placeholder]:empty::before { content: attr(data-placeholder); color: #9ca3af; font-style: italic; } .sidebar-scrollbar::-webkit-scrollbar{width:8px}.sidebar-scrollbar::-webkit-scrollbar-thumb{background:#d1d5db;border-radius:4px}.sidebar-scrollbar::-webkit-scrollbar-thumb:hover{background:#9ca3af}`}</style>\n\n      {/* Template Management Header */}\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center gap-2\">\n          <span className=\"text-sm text-muted-foreground\">Template</span>\n          <Select value={currentId ?? \"\"} onValueChange={(id) => {\n            setCurrentId(id);\n            const t = templates.find((x) => x.id === id);\n            if (t) { setTitle(t.title); setBlocks(t.blocks); }\n          }}>\n            <SelectTrigger className=\"w-[260px]\"><SelectValue placeholder=\"Select template\" /></SelectTrigger>\n            <SelectContent>\n              {templates.map((t) => (<SelectItem key={t.id} value={t.id!}>{t.title}</SelectItem>))}\n            </SelectContent>\n          </Select>\n          <Button variant=\"outline\" onClick={() => {\n            const id = `local_${crypto.randomUUID()}`;\n            const t = { id, title: \"Untitled Template\", blocks: [] as Block[] };\n            setTemplates((prev) => [...prev, t]);\n            setCurrentId(id);\n            setTitle(t.title);\n            setBlocks(t.blocks);\n          }}>New</Button>\n          <Button variant=\"outline\" onClick={() => {\n            if (!currentId) return;\n            const base = templates.find((t) => t.id === currentId);\n            if (!base) return;\n            const id = `local_${crypto.randomUUID()}`;\n            const title = `${base.title} (copy)`;\n            const blocks = base.blocks.map((b) => ({ ...b, id: crypto.randomUUID() })) as Block[];\n            const clone = { id, title, blocks };\n            setTemplates((prev) => [...prev, clone]);\n            setCurrentId(id);\n            setTitle(title);\n            setBlocks(blocks);\n          }}>Clone</Button>\n          <Button variant=\"secondary\" onClick={async () => {\n            if (convex) {\n              await loose.mutation(\"templates:upsertTemplate\", { id: currentId, title, blocks, published: false, ownerUserId: \"demo-user\" });\n              const list = await (loose.query(\"templates:listTemplates\", {}) as Promise<unknown>);\n              setTemplates((list as unknown[]).map(normalizeTemplate));\n            }\n          }}>Save</Button>\n        </div>\n      </div>\n\n      <div className=\"grid grid-cols-[1fr_320px] gap-4\">\n        {leftPanel}\n        <BlockSidebar detectionItems={detectionItems} />\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAXA;;;;;;;;;;;AAce,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,iNAAQ,EAAC;IACnC,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,iNAAQ,EAAU;QAC5C;YAAE,IAAI,OAAO,UAAU;YAAI,MAAM;YAAQ,SAAS;QAA0E;KAC7H;IACD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,iNAAQ,EAAkB,EAAE;IACxE,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,iNAAQ,EAAC;IAE/C,MAAM,SAAS,IAAA,uIAAS;IACxB,MAAM,QAAQ;IAId,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,iNAAQ,EAAyD,EAAE;IACrG,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,iNAAQ,EAAqB;IAE/D,SAAS,gBAAgB,SAAgC;QACvD,IAAI,CAAC,WAAW,OAAO,EAAE;QACzB,OAAO,UAAU,GAAG,CAAC,CAAC;YACpB,MAAM,MAAM;YACZ,IAAI,OAAO,OAAO,QAAQ,YAAY,OAAO,IAAI,IAAI,KAAK,UAAU,OAAO;YAC3E,wDAAwD;YACxD,IAAI,IAAI,IAAI,KAAK,QAAQ,OAAO;gBAAE,IAAI,OAAO,UAAU;gBAAI,MAAM;gBAAQ,SAAU,IAAI,KAAK,IAAI;YAAc;YAC9G,IAAI,IAAI,IAAI,KAAK,WAAW,OAAO;gBAAE,IAAI,OAAO,UAAU;gBAAI,MAAM;gBAAQ,SAAS,CAAC,IAAI,EAAE,IAAI,KAAK,IAAI,GAAG,KAAK,CAAC;YAAC;YACnH,IAAI,IAAI,IAAI,KAAK,SAAS,OAAO;gBAAE,IAAI,OAAO,UAAU;gBAAI,MAAM;gBAAQ,SAAS,CAAC,WAAW,EAAE,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,QAAQ,qBAAqB,CAAC;YAAC;YAC1J,OAAO;gBAAE,IAAI,OAAO,UAAU;gBAAI,MAAM;gBAAQ,SAAS;YAAG;QAC9D;IACF;IAEA,SAAS,kBAAkB,GAAY;QACrC,MAAM,IAAI;QACV,MAAM,KAAK,EAAE,EAAE,IAAI,CAAC,EAAE,GAAG,GAAG,OAAO,EAAE,GAAG,IAAI,SAAS;QACrD,OAAO;YAAE;YAAI,OAAO,EAAE,KAAK,IAAI;YAAY,QAAQ,gBAAgB,EAAE,MAAM;QAAE;IAC/E;IAEA,6BAA6B;IAC7B,IAAA,kNAAS,EAAC;QACR,IAAI,SAAS;QACb,eAAe;YACb,IAAI,CAAC,QAAQ;YACb,IAAI;gBACF,MAAM,OAAO,MAAO,MAAM,KAAK,CAAC,2BAA2B,CAAC;gBAC5D,MAAM,QAAQ,AAAC,KAAmB,GAAG,CAAC;gBACtC,IAAI,CAAC,QAAQ;oBACX,aAAa;oBACb,MAAM,QAAQ,KAAK,CAAC,EAAE;oBACtB,IAAI,OAAO;wBAAE,aAAa,MAAM,EAAE;wBAAG,SAAS,MAAM,KAAK;wBAAG,UAAU,MAAM,MAAM;oBAAG;gBACvF;YACF,EAAE,OAAM,CAAC;QACX;QACA;QACA,OAAO;YAAQ,SAAS;QAAM;IAChC,GAAG;QAAC;QAAQ;KAAM;IAElB,kDAAkD;IAClD,IAAA,kNAAS,EAAC;QACR,IAAI,YAAY;QAChB,eAAe;YACb,IAAI,CAAC,QAAQ;YACb,IAAI;gBACF,MAAM,MAAM,MAAM,MAAM,KAAK,CAAC,8BAA8B,CAAC;gBAC7D,MAAM,QAAQ,AAAC,IAAkB,GAAG,CAAC,CAAC;oBACpC,MAAM,MAAM;oBACZ,MAAM,KAAK,IAAI,EAAE,IAAI,CAAC,IAAI,GAAG,GAAG,OAAO,IAAI,GAAG,IAAI,OAAO,UAAU,EAAE;oBACrE,OAAO;wBAAE;wBAAI,MAAM,IAAI,IAAI,IAAI;wBAAI,UAAU,IAAI,QAAQ,IAAI;oBAAgB;gBAC/E;gBACA,IAAI,CAAC,WAAW,kBAAkB;YACpC,EAAE,OAAM,CAAC;QACX;QACA;QACA,OAAO;YAAQ,YAAY;QAAM;IACnC,GAAG;QAAC;QAAQ;KAAM;IAElB,MAAM,0BACJ,8OAAC;QAAI,WAAU;;YAEZ,CAAC,4BACA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,0IAAK;4BAAC,OAAO;4BAAO,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;4BAAG,WAAU;;;;;;;;;;;kCAE5E,8OAAC,iLAAY;wBAAC,QAAQ;wBAAQ,WAAW,CAAC,UAAY,UAAU,CAAC,OAAS,QAAQ;wBAAQ,gBAAgB;;;;;;;;;;;qCAG5G,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;4BAAyC;4BAAoB;;;;;;;kCAC5E,8OAAC,4IAAM;wBAAC,SAAQ;wBAAQ,MAAK;wBAAK,SAAS,IAAM,eAAe;kCAAQ;;;;;;;;;;;;YAK3E,CAAC,4BACA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCAAgC;;;;;;kCAC/C,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4IAAM;4BAAC,SAAQ;4BAAY,SAAS,IAAM,eAAe;sCAAO;;;;;;;;;;;;;;;;qCAIrE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,mLAAa;oBAAC,QAAQ;oBAAQ,gBAAgB;;;;;;;;;;;;;;;;;IAMvD,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,sJAAY;;;;;0BACb,8OAAC;0BAAO,CAAC,gTAAgT,CAAC;;;;;;0BAG1T,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAK,WAAU;sCAAgC;;;;;;sCAChD,8OAAC,4IAAM;4BAAC,OAAO,aAAa;4BAAI,eAAe,CAAC;gCAC9C,aAAa;gCACb,MAAM,IAAI,UAAU,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;gCACzC,IAAI,GAAG;oCAAE,SAAS,EAAE,KAAK;oCAAG,UAAU,EAAE,MAAM;gCAAG;4BACnD;;8CACE,8OAAC,mJAAa;oCAAC,WAAU;8CAAY,cAAA,8OAAC,iJAAW;wCAAC,aAAY;;;;;;;;;;;8CAC9D,8OAAC,mJAAa;8CACX,UAAU,GAAG,CAAC,CAAC,kBAAO,8OAAC,gJAAU;4CAAY,OAAO,EAAE,EAAE;sDAAI,EAAE,KAAK;2CAA5B,EAAE,EAAE;;;;;;;;;;;;;;;;sCAGhD,8OAAC,4IAAM;4BAAC,SAAQ;4BAAU,SAAS;gCACjC,MAAM,KAAK,CAAC,MAAM,EAAE,OAAO,UAAU,IAAI;gCACzC,MAAM,IAAI;oCAAE;oCAAI,OAAO;oCAAqB,QAAQ,EAAE;gCAAY;gCAClE,aAAa,CAAC,OAAS;2CAAI;wCAAM;qCAAE;gCACnC,aAAa;gCACb,SAAS,EAAE,KAAK;gCAChB,UAAU,EAAE,MAAM;4BACpB;sCAAG;;;;;;sCACH,8OAAC,4IAAM;4BAAC,SAAQ;4BAAU,SAAS;gCACjC,IAAI,CAAC,WAAW;gCAChB,MAAM,OAAO,UAAU,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;gCAC5C,IAAI,CAAC,MAAM;gCACX,MAAM,KAAK,CAAC,MAAM,EAAE,OAAO,UAAU,IAAI;gCACzC,MAAM,QAAQ,GAAG,KAAK,KAAK,CAAC,OAAO,CAAC;gCACpC,MAAM,SAAS,KAAK,MAAM,CAAC,GAAG,CAAC,CAAC,IAAM,CAAC;wCAAE,GAAG,CAAC;wCAAE,IAAI,OAAO,UAAU;oCAAG,CAAC;gCACxE,MAAM,QAAQ;oCAAE;oCAAI;oCAAO;gCAAO;gCAClC,aAAa,CAAC,OAAS;2CAAI;wCAAM;qCAAM;gCACvC,aAAa;gCACb,SAAS;gCACT,UAAU;4BACZ;sCAAG;;;;;;sCACH,8OAAC,4IAAM;4BAAC,SAAQ;4BAAY,SAAS;gCACnC,IAAI,QAAQ;oCACV,MAAM,MAAM,QAAQ,CAAC,4BAA4B;wCAAE,IAAI;wCAAW;wCAAO;wCAAQ,WAAW;wCAAO,aAAa;oCAAY;oCAC5H,MAAM,OAAO,MAAO,MAAM,KAAK,CAAC,2BAA2B,CAAC;oCAC5D,aAAa,AAAC,KAAmB,GAAG,CAAC;gCACvC;4BACF;sCAAG;;;;;;;;;;;;;;;;;0BAIP,8OAAC;gBAAI,WAAU;;oBACZ;kCACD,8OAAC,iLAAY;wBAAC,gBAAgB;;;;;;;;;;;;;;;;;;AAItC", "debugId": null}}]}