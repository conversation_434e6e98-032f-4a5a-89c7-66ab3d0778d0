{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///T:/Development/TranscriptionModule/transcription-demo/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Tabs({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Root>) {\n  return (\n    <TabsPrimitive.Root\n      data-slot=\"tabs\"\n      className={cn(\"flex flex-col gap-2\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TabsList({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.List>) {\n  return (\n    <TabsPrimitive.List\n      data-slot=\"tabs-list\"\n      className={cn(\n        \"bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsTrigger({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\n  return (\n    <TabsPrimitive.Trigger\n      data-slot=\"tabs-trigger\"\n      className={cn(\n        \"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Content>) {\n  return (\n    <TabsPrimitive.Content\n      data-slot=\"tabs-content\"\n      className={cn(\"flex-1 outline-none\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,KAAK,KAGoC;QAHpC,EACZ,SAAS,EACT,GAAG,OAC6C,GAHpC;IAIZ,qBACE,6LAAC,2KAAkB;QACjB,aAAU;QACV,WAAW,IAAA,4HAAE,EAAC,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,SAAS,KAGgC;QAHhC,EAChB,SAAS,EACT,GAAG,OAC6C,GAHhC;IAIhB,qBACE,6LAAC,2KAAkB;QACjB,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,uGACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,YAAY,KAGgC;QAHhC,EACnB,SAAS,EACT,GAAG,OACgD,GAHhC;IAInB,qBACE,6LAAC,8KAAqB;QACpB,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,mqBACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,YAAY,KAGgC;QAHhC,EACnB,SAAS,EACT,GAAG,OACgD,GAHhC;IAInB,qBACE,6LAAC,8KAAqB;QACpB,aAAU;QACV,WAAW,IAAA,4HAAE,EAAC,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 86, "column": 0}, "map": {"version": 3, "sources": ["file:///T:/Development/TranscriptionModule/transcription-demo/src/components/workspace-nav.tsx"], "sourcesContent": ["\"use client\";\n\nimport Link from \"next/link\";\nimport { usePathname } from \"next/navigation\";\nimport { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from \"@/components/ui/tabs\";\n\nconst items = [\n  { href: \"/recording\", label: \"Recording\" },\n  { href: \"/ai/dashboard\", label: \"AI Dashboard\" },\n  { href: \"/report-templates\", label: \"Report Templates\" },\n  { href: \"/templates/editor\", label: \"Template Editor\" },\n];\n\nexport function WorkspaceNav() {\n  const pathname = usePathname();\n  const value = items.find((i) => pathname.startsWith(i.href))?.href ?? \"/recording\";\n  return (\n    <Tabs value={value} className=\"w-full\">\n      <TabsList className=\"mb-4\">\n        {items.map((i) => (\n          <TabsTrigger key={i.href} value={i.href} asChild>\n            <Link href={i.href}>{i.label}</Link>\n          </TabsTrigger>\n        ))}\n      </TabsList>\n    </Tabs>\n  );\n}\n\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;;;AAJA;;;;AAMA,MAAM,QAAQ;IACZ;QAAE,MAAM;QAAc,OAAO;IAAY;IACzC;QAAE,MAAM;QAAiB,OAAO;IAAe;IAC/C;QAAE,MAAM;QAAqB,OAAO;IAAmB;IACvD;QAAE,MAAM;QAAqB,OAAO;IAAkB;CACvD;AAEM,SAAS;QAEA;;IADd,MAAM,WAAW,IAAA,oJAAW;QACd;IAAd,MAAM,QAAQ,CAAA,oBAAA,cAAA,MAAM,IAAI,CAAC,CAAC,IAAM,SAAS,UAAU,CAAC,EAAE,IAAI,gBAA5C,kCAAA,YAAgD,IAAI,cAApD,8BAAA,mBAAwD;IACtE,qBACE,6LAAC,2IAAI;QAAC,OAAO;QAAO,WAAU;kBAC5B,cAAA,6LAAC,+IAAQ;YAAC,WAAU;sBACjB,MAAM,GAAG,CAAC,CAAC,kBACV,6LAAC,kJAAW;oBAAc,OAAO,EAAE,IAAI;oBAAE,OAAO;8BAC9C,cAAA,6LAAC,0KAAI;wBAAC,MAAM,EAAE,IAAI;kCAAG,EAAE,KAAK;;;;;;mBADZ,EAAE,IAAI;;;;;;;;;;;;;;;AAOlC;GAdgB;;QACG,oJAAW;;;KADd", "debugId": null}}, {"offset": {"line": 171, "column": 0}, "map": {"version": 3, "sources": ["file:///T:/Development/TranscriptionModule/transcription-demo/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;;AAEA;;;AAEA,SAAS,MAAM,KAA4D;QAA5D,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC,GAA5D;IACb,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 203, "column": 0}, "map": {"version": 3, "sources": ["file:///T:/Development/TranscriptionModule/transcription-demo/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,KAEoC;QAFpC,EACd,GAAG,OAC+C,GAFpC;IAGd,qBAAO,6LAAC,6KAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,YAAY,KAEgC;QAFhC,EACnB,GAAG,OACgD,GAFhC;IAGnB,qBAAO,6LAAC,8KAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,YAAY,KAEgC;QAFhC,EACnB,GAAG,OACgD,GAFhC;IAGnB,qBAAO,6LAAC,8KAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,KAOtB;QAPsB,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ,GAPsB;IAQrB,qBACE,6LAAC,gLAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,IAAA,4HAAE,EACX,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,6KAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,8OAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MAxBS;AA0BT,SAAS,cAAc,KAKgC;QALhC,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD,GALhC;IAMrB,qBACE,6LAAC,+KAAsB;kBACrB,cAAA,6LAAC,gLAAuB;YACtB,aAAU;YACV,WAAW,IAAA,4HAAE,EACX,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,iLAAwB;oBACvB,WAAW,IAAA,4HAAE,EACX,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,KAGgC;QAHhC,EACnB,SAAS,EACT,GAAG,OACgD,GAHhC;IAInB,qBACE,6LAAC,8KAAqB;QACpB,aAAU;QACV,WAAW,IAAA,4HAAE,EAAC,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,WAAW,KAIgC;QAJhC,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C,GAJhC;IAKlB,qBACE,6LAAC,6KAAoB;QACnB,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,6aACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,sLAA6B;8BAC5B,cAAA,6LAAC,wNAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,6LAAC,iLAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,KAGgC;QAHhC,EACvB,SAAS,EACT,GAAG,OACoD,GAHhC;IAIvB,qBACE,6LAAC,kLAAyB;QACxB,aAAU;QACV,WAAW,IAAA,4HAAE,EAAC,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,KAGgC;QAHhC,EAC5B,SAAS,EACT,GAAG,OACyD,GAHhC;IAI5B,qBACE,6LAAC,uLAA8B;QAC7B,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,wOAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAhBS;AAkBT,SAAS,uBAAuB,KAGgC;QAHhC,EAC9B,SAAS,EACT,GAAG,OAC2D,GAHhC;IAI9B,qBACE,6LAAC,yLAAgC;QAC/B,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,8OAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAhBS", "debugId": null}}, {"offset": {"line": 470, "column": 0}, "map": {"version": 3, "sources": ["file:///T:/Development/TranscriptionModule/transcription-demo/src/lib/convexClient.ts"], "sourcesContent": ["\"use client\";\n\nimport { ConvexHttpClient } from \"convex/browser\";\n\nexport function getConvex(): ConvexHttpClient | null {\n  const url = process.env.NEXT_PUBLIC_CONVEX_URL;\n  if (!url) return null;\n  try {\n    return new ConvexHttpClient(url);\n  } catch {\n    return null;\n  }\n}\n\n"], "names": [], "mappings": ";;;;AAKc;AAHd;AAAA;AAFA;;AAIO,SAAS;IACd,MAAM;IACN;;IACA,IAAI;QACF,OAAO,IAAI,sLAAgB,CAAC;IAC9B,EAAE,UAAM;QACN,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 496, "column": 0}, "map": {"version": 3, "sources": ["file:///T:/Development/TranscriptionModule/transcription-demo/src/app/report-templates/_components/Toolbar.tsx"], "sourcesContent": ["\"use client\";\n\nimport { Bold, Italic, Underline, List, ListOrdered, AlignLeft, AlignCenter, AlignRight } from \"lucide-react\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\";\n\nexport function Toolbar() {\n  function exec(command: string, value?: string) {\n    try {\n      document.execCommand(command, false, value);\n    } catch {}\n  }\n\n  return (\n    <div className=\"flex flex-wrap items-center gap-2 p-2 rounded-lg border bg-muted/30\">\n      <Select defaultValue=\"Arial\" onValueChange={(v) => exec(\"fontName\", v)}>\n        <SelectTrigger className=\"h-8 w-[160px]\"><SelectValue placeholder=\"Font\"/></SelectTrigger>\n        <SelectContent>\n          <SelectItem value=\"Arial\">Arial</SelectItem>\n          <SelectItem value=\"Georgia\">Georgia</SelectItem>\n          <SelectItem value=\"Verdana\">Verdana</SelectItem>\n        </SelectContent>\n      </Select>\n      <Select defaultValue=\"3\" onValueChange={(v) => exec(\"fontSize\", v)}>\n        <SelectTrigger className=\"h-8 w-[140px]\"><SelectValue placeholder=\"Size\"/></SelectTrigger>\n        <SelectContent>\n          <SelectItem value=\"2\">Small</SelectItem>\n          <SelectItem value=\"3\">Normal</SelectItem>\n          <SelectItem value=\"5\">Heading</SelectItem>\n          <SelectItem value=\"6\">Title</SelectItem>\n        </SelectContent>\n      </Select>\n\n      <div className=\"w-px h-6 bg-border mx-1\"/>\n      <Button size=\"icon\" variant=\"ghost\" className=\"h-8 w-8\" onMouseDown={(e) => e.preventDefault()} onClick={() => exec(\"bold\")}>\n        <Bold className=\"h-4 w-4\" />\n      </Button>\n      <Button size=\"icon\" variant=\"ghost\" className=\"h-8 w-8\" onMouseDown={(e) => e.preventDefault()} onClick={() => exec(\"italic\")}>\n        <Italic className=\"h-4 w-4\" />\n      </Button>\n      <Button size=\"icon\" variant=\"ghost\" className=\"h-8 w-8\" onMouseDown={(e) => e.preventDefault()} onClick={() => exec(\"underline\")}>\n        <Underline className=\"h-4 w-4\" />\n      </Button>\n\n      <div className=\"w-px h-6 bg-border mx-1\"/>\n      <Button size=\"icon\" variant=\"ghost\" className=\"h-8 w-8\" onMouseDown={(e) => e.preventDefault()} onClick={() => exec(\"justifyLeft\")}>\n        <AlignLeft className=\"h-4 w-4\" />\n      </Button>\n      <Button size=\"icon\" variant=\"ghost\" className=\"h-8 w-8\" onMouseDown={(e) => e.preventDefault()} onClick={() => exec(\"justifyCenter\")}>\n        <AlignCenter className=\"h-4 w-4\" />\n      </Button>\n      <Button size=\"icon\" variant=\"ghost\" className=\"h-8 w-8\" onMouseDown={(e) => e.preventDefault()} onClick={() => exec(\"justifyRight\")}>\n        <AlignRight className=\"h-4 w-4\" />\n      </Button>\n\n      <div className=\"w-px h-6 bg-border mx-1\"/>\n      <Button size=\"icon\" variant=\"ghost\" className=\"h-8 w-8\" onMouseDown={(e) => e.preventDefault()} onClick={() => { document.execCommand(\"formatBlock\", false, \"div\"); exec(\"insertUnorderedList\"); }}>\n        <List className=\"h-4 w-4\" />\n      </Button>\n      <Button size=\"icon\" variant=\"ghost\" className=\"h-8 w-8\" onMouseDown={(e) => e.preventDefault()} onClick={() => { document.execCommand(\"formatBlock\", false, \"div\"); exec(\"insertOrderedList\"); }}>\n        <ListOrdered className=\"h-4 w-4\" />\n      </Button>\n    </div>\n  );\n}\n\n"], "names": [], "mappings": ";;;;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAJA;;;;;AAMO,SAAS;IACd,SAAS,KAAK,OAAe,EAAE,KAAc;QAC3C,IAAI;YACF,SAAS,WAAW,CAAC,SAAS,OAAO;QACvC,EAAE,UAAM,CAAC;IACX;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,+IAAM;gBAAC,cAAa;gBAAQ,eAAe,CAAC,IAAM,KAAK,YAAY;;kCAClE,6LAAC,sJAAa;wBAAC,WAAU;kCAAgB,cAAA,6LAAC,oJAAW;4BAAC,aAAY;;;;;;;;;;;kCAClE,6LAAC,sJAAa;;0CACZ,6LAAC,mJAAU;gCAAC,OAAM;0CAAQ;;;;;;0CAC1B,6LAAC,mJAAU;gCAAC,OAAM;0CAAU;;;;;;0CAC5B,6LAAC,mJAAU;gCAAC,OAAM;0CAAU;;;;;;;;;;;;;;;;;;0BAGhC,6LAAC,+IAAM;gBAAC,cAAa;gBAAI,eAAe,CAAC,IAAM,KAAK,YAAY;;kCAC9D,6LAAC,sJAAa;wBAAC,WAAU;kCAAgB,cAAA,6LAAC,oJAAW;4BAAC,aAAY;;;;;;;;;;;kCAClE,6LAAC,sJAAa;;0CACZ,6LAAC,mJAAU;gCAAC,OAAM;0CAAI;;;;;;0CACtB,6LAAC,mJAAU;gCAAC,OAAM;0CAAI;;;;;;0CACtB,6LAAC,mJAAU;gCAAC,OAAM;0CAAI;;;;;;0CACtB,6LAAC,mJAAU;gCAAC,OAAM;0CAAI;;;;;;;;;;;;;;;;;;0BAI1B,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC,+IAAM;gBAAC,MAAK;gBAAO,SAAQ;gBAAQ,WAAU;gBAAU,aAAa,CAAC,IAAM,EAAE,cAAc;gBAAI,SAAS,IAAM,KAAK;0BAClH,cAAA,6LAAC,6MAAI;oBAAC,WAAU;;;;;;;;;;;0BAElB,6LAAC,+IAAM;gBAAC,MAAK;gBAAO,SAAQ;gBAAQ,WAAU;gBAAU,aAAa,CAAC,IAAM,EAAE,cAAc;gBAAI,SAAS,IAAM,KAAK;0BAClH,cAAA,6LAAC,mNAAM;oBAAC,WAAU;;;;;;;;;;;0BAEpB,6LAAC,+IAAM;gBAAC,MAAK;gBAAO,SAAQ;gBAAQ,WAAU;gBAAU,aAAa,CAAC,IAAM,EAAE,cAAc;gBAAI,SAAS,IAAM,KAAK;0BAClH,cAAA,6LAAC,4NAAS;oBAAC,WAAU;;;;;;;;;;;0BAGvB,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC,+IAAM;gBAAC,MAAK;gBAAO,SAAQ;gBAAQ,WAAU;gBAAU,aAAa,CAAC,IAAM,EAAE,cAAc;gBAAI,SAAS,IAAM,KAAK;0BAClH,cAAA,6LAAC,yOAAS;oBAAC,WAAU;;;;;;;;;;;0BAEvB,6LAAC,+IAAM;gBAAC,MAAK;gBAAO,SAAQ;gBAAQ,WAAU;gBAAU,aAAa,CAAC,IAAM,EAAE,cAAc;gBAAI,SAAS,IAAM,KAAK;0BAClH,cAAA,6LAAC,8OAAW;oBAAC,WAAU;;;;;;;;;;;0BAEzB,6LAAC,+IAAM;gBAAC,MAAK;gBAAO,SAAQ;gBAAQ,WAAU;gBAAU,aAAa,CAAC,IAAM,EAAE,cAAc;gBAAI,SAAS,IAAM,KAAK;0BAClH,cAAA,6LAAC,yOAAU;oBAAC,WAAU;;;;;;;;;;;0BAGxB,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC,+IAAM;gBAAC,MAAK;gBAAO,SAAQ;gBAAQ,WAAU;gBAAU,aAAa,CAAC,IAAM,EAAE,cAAc;gBAAI,SAAS;oBAAQ,SAAS,WAAW,CAAC,eAAe,OAAO;oBAAQ,KAAK;gBAAwB;0BAC/L,cAAA,6LAAC,6MAAI;oBAAC,WAAU;;;;;;;;;;;0BAElB,6LAAC,+IAAM;gBAAC,MAAK;gBAAO,SAAQ;gBAAQ,WAAU;gBAAU,aAAa,CAAC,IAAM,EAAE,cAAc;gBAAI,SAAS;oBAAQ,SAAS,WAAW,CAAC,eAAe,OAAO;oBAAQ,KAAK;gBAAsB;0BAC7L,cAAA,6LAAC,sOAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI/B;KA1DgB", "debugId": null}}, {"offset": {"line": 833, "column": 0}, "map": {"version": 3, "sources": ["file:///T:/Development/TranscriptionModule/transcription-demo/src/app/report-templates/_components/hooks/useDragInsertionCursor.ts"], "sourcesContent": ["import { useEffect, useRef, useState } from 'react';\n\ninterface DragInsertionCursorOptions {\n  blinkInterval?: number;\n  insertionSymbol?: string;\n  cooldownTime?: number;\n}\n\nexport function useDragInsertionCursor(\n  targetRef: React.RefObject<HTMLElement>,\n  isDragging: boolean,\n  options: DragInsertionCursorOptions = {}\n) {\n  const {\n    blinkInterval = 500,\n    insertionSymbol = '|',\n    cooldownTime = 1000\n  } = options;\n\n  const [isVisible, setIsVisible] = useState(false);\n  const [insertionPoint, setInsertionPoint] = useState<{ x: number; y: number } | null>(null);\n  const cooldownRef = useRef<number>(0);\n  const intervalRef = useRef<NodeJS.Timeout | null>(null);\n\n  // Start/stop blinking based on drag state\n  useEffect(() => {\n    if (isDragging && insertionPoint) {\n      // Reset cooldown and start visible\n      cooldownRef.current = cooldownTime;\n      setIsVisible(true);\n\n      // Clear any existing interval\n      if (intervalRef.current) {\n        clearInterval(intervalRef.current);\n      }\n\n      // Start blinking interval\n      intervalRef.current = setInterval(() => {\n        if (cooldownRef.current > 0) {\n          cooldownRef.current -= blinkInterval;\n          setIsVisible(true); // Stay visible during cooldown\n        } else {\n          setIsVisible(prev => !prev); // Start blinking after cooldown\n        }\n      }, blinkInterval);\n\n    } else {\n      // Stop blinking when not dragging\n      if (intervalRef.current) {\n        clearInterval(intervalRef.current);\n        intervalRef.current = null;\n      }\n      setIsVisible(false);\n      setInsertionPoint(null);\n    }\n\n    return () => {\n      if (intervalRef.current) {\n        clearInterval(intervalRef.current);\n      }\n    };\n  }, [isDragging, insertionPoint, blinkInterval, cooldownTime]);\n\n  // Function to update insertion point position\n  const updateInsertionPoint = (clientX: number, clientY: number) => {\n    const target = targetRef.current;\n    if (!target || !isDragging) return;\n\n    // Use caretRangeFromPoint to get precise text position\n    let range: Range | null = null;\n    if ((document as any).caretRangeFromPoint) {\n      range = (document as any).caretRangeFromPoint(clientX, clientY);\n    } else if ((document as any).caretPositionFromPoint) {\n      const pos = (document as any).caretPositionFromPoint(clientX, clientY);\n      if (pos) {\n        range = document.createRange();\n        range.setStart(pos.offsetNode, pos.offset);\n        range.collapse(true);\n      }\n    }\n\n    if (range) {\n      const rect = range.getBoundingClientRect();\n      const targetRect = target.getBoundingClientRect();\n      \n      setInsertionPoint({\n        x: rect.left - targetRect.left,\n        y: rect.top - targetRect.top\n      });\n      \n      // Reset cooldown when position updates\n      cooldownRef.current = cooldownTime;\n      setIsVisible(true);\n    }\n  };\n\n  // Reset insertion point\n  const resetInsertionPoint = () => {\n    setInsertionPoint(null);\n    setIsVisible(false);\n    if (intervalRef.current) {\n      clearInterval(intervalRef.current);\n      intervalRef.current = null;\n    }\n  };\n\n  return {\n    insertionPoint: isVisible ? insertionPoint : null,\n    updateInsertionPoint,\n    resetInsertionPoint\n  };\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAQO,SAAS,uBACd,SAAuC,EACvC,UAAmB;QACnB,UAAA,iEAAsC,CAAC;;IAEvC,MAAM,EACJ,gBAAgB,GAAG,EACnB,kBAAkB,GAAG,EACrB,eAAe,IAAI,EACpB,GAAG;IAEJ,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,yKAAQ,EAAC;IAC3C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,yKAAQ,EAAkC;IACtF,MAAM,cAAc,IAAA,uKAAM,EAAS;IACnC,MAAM,cAAc,IAAA,uKAAM,EAAwB;IAElD,0CAA0C;IAC1C,IAAA,0KAAS;4CAAC;YACR,IAAI,cAAc,gBAAgB;gBAChC,mCAAmC;gBACnC,YAAY,OAAO,GAAG;gBACtB,aAAa;gBAEb,8BAA8B;gBAC9B,IAAI,YAAY,OAAO,EAAE;oBACvB,cAAc,YAAY,OAAO;gBACnC;gBAEA,0BAA0B;gBAC1B,YAAY,OAAO,GAAG;wDAAY;wBAChC,IAAI,YAAY,OAAO,GAAG,GAAG;4BAC3B,YAAY,OAAO,IAAI;4BACvB,aAAa,OAAO,+BAA+B;wBACrD,OAAO;4BACL;oEAAa,CAAA,OAAQ,CAAC;oEAAO,gCAAgC;wBAC/D;oBACF;uDAAG;YAEL,OAAO;gBACL,kCAAkC;gBAClC,IAAI,YAAY,OAAO,EAAE;oBACvB,cAAc,YAAY,OAAO;oBACjC,YAAY,OAAO,GAAG;gBACxB;gBACA,aAAa;gBACb,kBAAkB;YACpB;YAEA;oDAAO;oBACL,IAAI,YAAY,OAAO,EAAE;wBACvB,cAAc,YAAY,OAAO;oBACnC;gBACF;;QACF;2CAAG;QAAC;QAAY;QAAgB;QAAe;KAAa;IAE5D,8CAA8C;IAC9C,MAAM,uBAAuB,CAAC,SAAiB;QAC7C,MAAM,SAAS,UAAU,OAAO;QAChC,IAAI,CAAC,UAAU,CAAC,YAAY;QAE5B,uDAAuD;QACvD,IAAI,QAAsB;QAC1B,IAAI,AAAC,SAAiB,mBAAmB,EAAE;YACzC,QAAQ,AAAC,SAAiB,mBAAmB,CAAC,SAAS;QACzD,OAAO,IAAI,AAAC,SAAiB,sBAAsB,EAAE;YACnD,MAAM,MAAM,AAAC,SAAiB,sBAAsB,CAAC,SAAS;YAC9D,IAAI,KAAK;gBACP,QAAQ,SAAS,WAAW;gBAC5B,MAAM,QAAQ,CAAC,IAAI,UAAU,EAAE,IAAI,MAAM;gBACzC,MAAM,QAAQ,CAAC;YACjB;QACF;QAEA,IAAI,OAAO;YACT,MAAM,OAAO,MAAM,qBAAqB;YACxC,MAAM,aAAa,OAAO,qBAAqB;YAE/C,kBAAkB;gBAChB,GAAG,KAAK,IAAI,GAAG,WAAW,IAAI;gBAC9B,GAAG,KAAK,GAAG,GAAG,WAAW,GAAG;YAC9B;YAEA,uCAAuC;YACvC,YAAY,OAAO,GAAG;YACtB,aAAa;QACf;IACF;IAEA,wBAAwB;IACxB,MAAM,sBAAsB;QAC1B,kBAAkB;QAClB,aAAa;QACb,IAAI,YAAY,OAAO,EAAE;YACvB,cAAc,YAAY,OAAO;YACjC,YAAY,OAAO,GAAG;QACxB;IACF;IAEA,OAAO;QACL,gBAAgB,YAAY,iBAAiB;QAC7C;QACA;IACF;AACF;GAvGgB", "debugId": null}}, {"offset": {"line": 946, "column": 0}, "map": {"version": 3, "sources": ["file:///T:/Development/TranscriptionModule/transcription-demo/src/app/report-templates/_components/EditorCanvas.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useEffect, useRef, useState } from \"react\";\nimport { createRoot, Root } from \"react-dom/client\";\nimport { Toolbar } from \"./Toolbar\";\nimport { CategoryBlock as CategoryBlockCmp } from \"./blocks/CategoryBlock\";\nimport { useDragInsertionCursor } from \"./hooks/useDragInsertionCursor\";\nimport type { Block, DetectionItem, AiBlock, CategoryBlock, TextBlock, FindingBlock } from \"./types\";\n\nexport function EditorCanvas({ blocks, setBlocks, detectionItems }: { blocks: Block[]; setBlocks: (updater: (prev: Block[]) => Block[]) => void; detectionItems: DetectionItem[]; }) {\n  const editorRef = useRef<HTMLDivElement>(null);\n  const [isDragging, setIsDragging] = useState(false);\n  const rootsRef = useRef<Map<string, Root>>(new Map());\n\n  // Use the drag insertion cursor hook\n  const { insertionPoint, updateInsertionPoint, resetInsertionPoint } = useDragInsertionCursor(\n    editorRef as React.RefObject<HTMLElement>,\n    isDragging,\n    { blinkInterval: 500, cooldownTime: 800 }\n  );\n\n  // Get text content from blocks for the single contentEditable area\n  const textContent = blocks\n    .filter(b => b.type === \"TEXT\")\n    .map(b => (b as TextBlock).content)\n    .join(\"\");\n\n  // Initialize editor with basic content and setup drag handlers\n  useEffect(() => {\n    const editor = editorRef.current;\n    if (!editor) return;\n\n    // Initialize with basic content if empty\n    if (editor.innerHTML.trim() === \"\" && textContent.trim() === \"\") {\n      editor.innerHTML = \"<p>Start typing your report here...</p>\";\n    } else if (textContent && editor.innerHTML !== textContent) {\n      editor.innerHTML = textContent;\n    }\n\n    // Make inline finding chips draggable with proper cleanup\n    const findingChips = editor.querySelectorAll('[data-inline-id]');\n    findingChips.forEach((chip) => {\n      const element = chip as HTMLElement;\n      element.draggable = true;\n\n      // Remove any existing listeners first\n      const existingHandler = (element as any)._dragHandler;\n      if (existingHandler) {\n        element.removeEventListener('dragstart', existingHandler);\n      }\n\n      // Create new handler and store reference\n      const dragHandler = (e: DragEvent) => {\n        const id = element.getAttribute('data-inline-id');\n        if (id && e.dataTransfer) {\n          e.dataTransfer.setData('application/x-source-id', id);\n          e.dataTransfer.setData('text/plain', '');\n          e.dataTransfer.effectAllowed = 'move';\n        }\n      };\n\n      (element as any)._dragHandler = dragHandler;\n      element.addEventListener('dragstart', dragHandler);\n    });\n  }, [textContent]);\n\n  // Event handlers\n  function handleInput(e: React.FormEvent<HTMLDivElement>) {\n    const editor = editorRef.current;\n    if (!editor) return;\n    \n    const content = editor.innerHTML;\n    \n    // Update the first TEXT block or create one if none exists\n    setBlocks((prev) => {\n      const textBlocks = prev.filter(b => b.type === \"TEXT\");\n      const otherBlocks = prev.filter(b => b.type !== \"TEXT\");\n      \n      if (textBlocks.length === 0) {\n        return [{ id: crypto.randomUUID(), type: \"TEXT\", content } as TextBlock, ...otherBlocks];\n      }\n      \n      // Update the first text block\n      const updatedTextBlocks = textBlocks.map((b, i) => \n        i === 0 ? { ...b, content } as TextBlock : b\n      );\n      \n      return [...updatedTextBlocks, ...otherBlocks];\n    });\n  }\n\n  function handleDragOver(e: React.DragEvent) {\n    e.preventDefault();\n    e.stopPropagation();\n    e.dataTransfer.dropEffect = \"copy\";\n    setIsDragging(true);\n\n    // Update insertion point for visual feedback\n    updateInsertionPoint(e.clientX, e.clientY);\n  }\n\n  function insertNodeAtRange(range: Range, node: Node) {\n    range.deleteContents();\n    range.insertNode(node);\n    range.setStartAfter(node);\n    range.collapse(true);\n  }\n\n  function handleCanvasDrop(e: React.DragEvent<HTMLDivElement>) {\n    e.preventDefault();\n    setIsDragging(false);\n    resetInsertionPoint();\n    \n    const payloadRaw = e.dataTransfer.getData(\"application/json\");\n    const fromId = e.dataTransfer.getData(\"application/x-source-id\");\n    const data = payloadRaw ? JSON.parse(payloadRaw) : undefined;\n    \n    // Use native browser caret position from drop event\n    let range: Range | null = null;\n    if ((document as any).caretRangeFromPoint) {\n      range = (document as any).caretRangeFromPoint(e.clientX, e.clientY);\n    } else if ((document as any).caretPositionFromPoint) {\n      const pos = (document as any).caretPositionFromPoint(e.clientX, e.clientY);\n      if (pos) {\n        range = document.createRange();\n        range.setStart(pos.offsetNode, pos.offset);\n        range.collapse(true);\n      }\n    }\n    \n    if (!range) return;\n\n    // Handle existing inline finding reordering\n    if (!data && fromId) {\n      const existingChip = editorRef.current?.querySelector(`[data-inline-id=\"${fromId}\"]`);\n      if (existingChip) {\n        existingChip.remove();\n        const newChip = existingChip.cloneNode(true) as HTMLElement;\n        newChip.draggable = true;\n        insertNodeAtRange(range, newChip);\n      }\n      return;\n    }\n\n    if (!data) return;\n\n    const newId = `b_${Date.now()}_${Math.random().toString(36).slice(2)}`;\n\n    if (data.type === \"FINDING_BLOCK\") {\n      // Create inline finding chip\n      const chip = document.createElement(\"span\");\n      chip.setAttribute(\"data-inline-id\", newId);\n      chip.setAttribute(\"contenteditable\", \"false\");\n      chip.className = \"inline-flex items-center px-2 py-1 rounded-full bg-blue-100 text-blue-800 text-xs font-medium mx-1 cursor-grab\";\n      chip.textContent = data.findingName || \"Finding\";\n      chip.draggable = true;\n      \n      chip.addEventListener('dragstart', (dragEvent) => {\n        dragEvent.dataTransfer?.setData('application/x-source-id', newId);\n        dragEvent.dataTransfer?.setData('text/plain', '');\n      });\n      \n      insertNodeAtRange(range, chip);\n      \n      setBlocks((prev) => {\n        const existing = prev.find(b => b.id === newId);\n        if (existing) return prev;\n        return [...prev, { id: newId, type: \"FINDING_BLOCK\", findingId: data.findingId, findingName: data.findingName, selectedValue: \"value\" } as FindingBlock];\n      });\n      \n    } else if (data.type === \"AI_BLOCK\" || data.type === \"CATEGORY_BLOCK\") {\n      // Create inline block placeholder that will be replaced with React component\n      const blockPlaceholder = document.createElement(\"div\");\n      blockPlaceholder.setAttribute(\"data-block-id\", newId);\n      blockPlaceholder.setAttribute(\"contenteditable\", \"false\");\n      blockPlaceholder.className = \"my-2 p-3 border rounded-lg bg-gray-50 not-prose\";\n\n      if (data.type === \"AI_BLOCK\") {\n        blockPlaceholder.className = \"my-2 p-3 border rounded-lg bg-orange-50 border-orange-200 not-prose\";\n        blockPlaceholder.innerHTML = `\n          <div class=\"flex items-center gap-2 mb-2\">\n            <span class=\"text-orange-600\">🧠</span>\n            <span class=\"font-medium text-orange-700\">AI Instruction Block</span>\n          </div>\n          <div class=\"text-sm bg-white border border-orange-200 rounded p-3\" contenteditable=\"true\" data-ai-content=\"${newId}\" placeholder=\"Type your AI instructions here...\">\n            Type your AI instructions here...\n          </div>\n        `;\n      } else {\n        // Category block - create container for React component\n        blockPlaceholder.className = \"my-2 not-prose\";\n        blockPlaceholder.innerHTML = \"\"; // Will be populated by React component\n      }\n\n      // Insert with line breaks for proper text flow\n      const br1 = document.createElement(\"br\");\n      const br2 = document.createElement(\"br\");\n      insertNodeAtRange(range, br1);\n      range.setStartAfter(br1);\n      range.insertNode(blockPlaceholder);\n      range.setStartAfter(blockPlaceholder);\n      range.insertNode(br2);\n\n      // Position cursor after the block\n      const sel = window.getSelection();\n      if (sel) {\n        sel.removeAllRanges();\n        range.setStartAfter(br2);\n        range.collapse(true);\n        sel.addRange(range);\n      }\n\n      // Add to blocks array for data management\n      const newBlock = data.type === \"AI_BLOCK\"\n        ? ({ id: newId, type: \"AI_BLOCK\", innerContent: \"\" } as AiBlock)\n        : ({ id: newId, type: \"CATEGORY_BLOCK\", category: data.category, categoryName: data.categoryName, selectedFindingIds: [] } as CategoryBlock);\n\n      setBlocks((prev) => [...prev, newBlock]);\n\n      // Setup AI content editing if it's an AI block\n      if (data.type === \"AI_BLOCK\") {\n        const aiContentArea = blockPlaceholder.querySelector(`[data-ai-content=\"${newId}\"]`) as HTMLElement;\n        if (aiContentArea) {\n          aiContentArea.addEventListener('input', () => {\n            setBlocks(prev => prev.map(b =>\n              b.id === newId && b.type === \"AI_BLOCK\"\n                ? { ...b, innerContent: aiContentArea.innerHTML } as AiBlock\n                : b\n            ));\n          });\n        }\n      }\n    }\n  }\n\n  return (\n    <div className=\"flex flex-col h-full bg-background rounded-md border p-3\">\n      <Toolbar />\n\n      {/* Main text editor with inline blocks */}\n      <div className=\"relative flex-1\">\n        <div\n          ref={editorRef}\n          contentEditable\n          suppressContentEditableWarning\n          className={`flex-1 p-4 min-h-[300px] overflow-auto focus:outline-none ${isDragging ? 'bg-blue-50' : ''}`}\n          onInput={handleInput}\n          onDrop={handleCanvasDrop}\n          onDragOver={handleDragOver}\n          onDragLeave={() => {\n            setIsDragging(false);\n            resetInsertionPoint();\n          }}\n        />\n\n        {/* Visual drag insertion cursor */}\n        {insertionPoint && (\n          <div\n            className=\"absolute w-0.5 h-5 bg-blue-600 pointer-events-none z-10 animate-pulse\"\n            style={{\n              left: insertionPoint.x,\n              top: insertionPoint.y,\n            }}\n          />\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;AAEA;;;AANA;;;;AASO,SAAS,aAAa,KAAsJ;QAAtJ,EAAE,MAAM,EAAE,SAAS,EAAE,cAAc,EAAmH,GAAtJ;;IAC3B,MAAM,YAAY,IAAA,uKAAM,EAAiB;IACzC,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,yKAAQ,EAAC;IAC7C,MAAM,WAAW,IAAA,uKAAM,EAAoB,IAAI;IAE/C,qCAAqC;IACrC,MAAM,EAAE,cAAc,EAAE,oBAAoB,EAAE,mBAAmB,EAAE,GAAG,IAAA,gNAAsB,EAC1F,WACA,YACA;QAAE,eAAe;QAAK,cAAc;IAAI;IAG1C,mEAAmE;IACnE,MAAM,cAAc,OACjB,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,QACvB,GAAG,CAAC,CAAA,IAAK,AAAC,EAAgB,OAAO,EACjC,IAAI,CAAC;IAER,+DAA+D;IAC/D,IAAA,0KAAS;kCAAC;YACR,MAAM,SAAS,UAAU,OAAO;YAChC,IAAI,CAAC,QAAQ;YAEb,yCAAyC;YACzC,IAAI,OAAO,SAAS,CAAC,IAAI,OAAO,MAAM,YAAY,IAAI,OAAO,IAAI;gBAC/D,OAAO,SAAS,GAAG;YACrB,OAAO,IAAI,eAAe,OAAO,SAAS,KAAK,aAAa;gBAC1D,OAAO,SAAS,GAAG;YACrB;YAEA,0DAA0D;YAC1D,MAAM,eAAe,OAAO,gBAAgB,CAAC;YAC7C,aAAa,OAAO;0CAAC,CAAC;oBACpB,MAAM,UAAU;oBAChB,QAAQ,SAAS,GAAG;oBAEpB,sCAAsC;oBACtC,MAAM,kBAAkB,AAAC,QAAgB,YAAY;oBACrD,IAAI,iBAAiB;wBACnB,QAAQ,mBAAmB,CAAC,aAAa;oBAC3C;oBAEA,yCAAyC;oBACzC,MAAM;8DAAc,CAAC;4BACnB,MAAM,KAAK,QAAQ,YAAY,CAAC;4BAChC,IAAI,MAAM,EAAE,YAAY,EAAE;gCACxB,EAAE,YAAY,CAAC,OAAO,CAAC,2BAA2B;gCAClD,EAAE,YAAY,CAAC,OAAO,CAAC,cAAc;gCACrC,EAAE,YAAY,CAAC,aAAa,GAAG;4BACjC;wBACF;;oBAEC,QAAgB,YAAY,GAAG;oBAChC,QAAQ,gBAAgB,CAAC,aAAa;gBACxC;;QACF;iCAAG;QAAC;KAAY;IAEhB,iBAAiB;IACjB,SAAS,YAAY,CAAkC;QACrD,MAAM,SAAS,UAAU,OAAO;QAChC,IAAI,CAAC,QAAQ;QAEb,MAAM,UAAU,OAAO,SAAS;QAEhC,2DAA2D;QAC3D,UAAU,CAAC;YACT,MAAM,aAAa,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK;YAC/C,MAAM,cAAc,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK;YAEhD,IAAI,WAAW,MAAM,KAAK,GAAG;gBAC3B,OAAO;oBAAC;wBAAE,IAAI,OAAO,UAAU;wBAAI,MAAM;wBAAQ;oBAAQ;uBAAmB;iBAAY;YAC1F;YAEA,8BAA8B;YAC9B,MAAM,oBAAoB,WAAW,GAAG,CAAC,CAAC,GAAG,IAC3C,MAAM,IAAI;oBAAE,GAAG,CAAC;oBAAE;gBAAQ,IAAiB;YAG7C,OAAO;mBAAI;mBAAsB;aAAY;QAC/C;IACF;IAEA,SAAS,eAAe,CAAkB;QACxC,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,EAAE,YAAY,CAAC,UAAU,GAAG;QAC5B,cAAc;QAEd,6CAA6C;QAC7C,qBAAqB,EAAE,OAAO,EAAE,EAAE,OAAO;IAC3C;IAEA,SAAS,kBAAkB,KAAY,EAAE,IAAU;QACjD,MAAM,cAAc;QACpB,MAAM,UAAU,CAAC;QACjB,MAAM,aAAa,CAAC;QACpB,MAAM,QAAQ,CAAC;IACjB;IAEA,SAAS,iBAAiB,CAAkC;QAC1D,EAAE,cAAc;QAChB,cAAc;QACd;QAEA,MAAM,aAAa,EAAE,YAAY,CAAC,OAAO,CAAC;QAC1C,MAAM,SAAS,EAAE,YAAY,CAAC,OAAO,CAAC;QACtC,MAAM,OAAO,aAAa,KAAK,KAAK,CAAC,cAAc;QAEnD,oDAAoD;QACpD,IAAI,QAAsB;QAC1B,IAAI,AAAC,SAAiB,mBAAmB,EAAE;YACzC,QAAQ,AAAC,SAAiB,mBAAmB,CAAC,EAAE,OAAO,EAAE,EAAE,OAAO;QACpE,OAAO,IAAI,AAAC,SAAiB,sBAAsB,EAAE;YACnD,MAAM,MAAM,AAAC,SAAiB,sBAAsB,CAAC,EAAE,OAAO,EAAE,EAAE,OAAO;YACzE,IAAI,KAAK;gBACP,QAAQ,SAAS,WAAW;gBAC5B,MAAM,QAAQ,CAAC,IAAI,UAAU,EAAE,IAAI,MAAM;gBACzC,MAAM,QAAQ,CAAC;YACjB;QACF;QAEA,IAAI,CAAC,OAAO;QAEZ,4CAA4C;QAC5C,IAAI,CAAC,QAAQ,QAAQ;gBACE;YAArB,MAAM,gBAAe,qBAAA,UAAU,OAAO,cAAjB,yCAAA,mBAAmB,aAAa,CAAC,AAAC,oBAA0B,OAAP,QAAO;YACjF,IAAI,cAAc;gBAChB,aAAa,MAAM;gBACnB,MAAM,UAAU,aAAa,SAAS,CAAC;gBACvC,QAAQ,SAAS,GAAG;gBACpB,kBAAkB,OAAO;YAC3B;YACA;QACF;QAEA,IAAI,CAAC,MAAM;QAEX,MAAM,QAAQ,AAAC,KAAkB,OAAd,KAAK,GAAG,IAAG,KAAuC,OAApC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,KAAK,CAAC;QAElE,IAAI,KAAK,IAAI,KAAK,iBAAiB;YACjC,6BAA6B;YAC7B,MAAM,OAAO,SAAS,aAAa,CAAC;YACpC,KAAK,YAAY,CAAC,kBAAkB;YACpC,KAAK,YAAY,CAAC,mBAAmB;YACrC,KAAK,SAAS,GAAG;YACjB,KAAK,WAAW,GAAG,KAAK,WAAW,IAAI;YACvC,KAAK,SAAS,GAAG;YAEjB,KAAK,gBAAgB,CAAC,aAAa,CAAC;oBAClC,yBACA;iBADA,0BAAA,UAAU,YAAY,cAAtB,8CAAA,wBAAwB,OAAO,CAAC,2BAA2B;iBAC3D,2BAAA,UAAU,YAAY,cAAtB,+CAAA,yBAAwB,OAAO,CAAC,cAAc;YAChD;YAEA,kBAAkB,OAAO;YAEzB,UAAU,CAAC;gBACT,MAAM,WAAW,KAAK,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBACzC,IAAI,UAAU,OAAO;gBACrB,OAAO;uBAAI;oBAAM;wBAAE,IAAI;wBAAO,MAAM;wBAAiB,WAAW,KAAK,SAAS;wBAAE,aAAa,KAAK,WAAW;wBAAE,eAAe;oBAAQ;iBAAkB;YAC1J;QAEF,OAAO,IAAI,KAAK,IAAI,KAAK,cAAc,KAAK,IAAI,KAAK,kBAAkB;YACrE,6EAA6E;YAC7E,MAAM,mBAAmB,SAAS,aAAa,CAAC;YAChD,iBAAiB,YAAY,CAAC,iBAAiB;YAC/C,iBAAiB,YAAY,CAAC,mBAAmB;YACjD,iBAAiB,SAAS,GAAG;YAE7B,IAAI,KAAK,IAAI,KAAK,YAAY;gBAC5B,iBAAiB,SAAS,GAAG;gBAC7B,iBAAiB,SAAS,GAAG,AAAC,0UAKuF,OAAN,OAAM;YAIvH,OAAO;gBACL,wDAAwD;gBACxD,iBAAiB,SAAS,GAAG;gBAC7B,iBAAiB,SAAS,GAAG,IAAI,uCAAuC;YAC1E;YAEA,+CAA+C;YAC/C,MAAM,MAAM,SAAS,aAAa,CAAC;YACnC,MAAM,MAAM,SAAS,aAAa,CAAC;YACnC,kBAAkB,OAAO;YACzB,MAAM,aAAa,CAAC;YACpB,MAAM,UAAU,CAAC;YACjB,MAAM,aAAa,CAAC;YACpB,MAAM,UAAU,CAAC;YAEjB,kCAAkC;YAClC,MAAM,MAAM,OAAO,YAAY;YAC/B,IAAI,KAAK;gBACP,IAAI,eAAe;gBACnB,MAAM,aAAa,CAAC;gBACpB,MAAM,QAAQ,CAAC;gBACf,IAAI,QAAQ,CAAC;YACf;YAEA,0CAA0C;YAC1C,MAAM,WAAW,KAAK,IAAI,KAAK,aAC1B;gBAAE,IAAI;gBAAO,MAAM;gBAAY,cAAc;YAAG,IAChD;gBAAE,IAAI;gBAAO,MAAM;gBAAkB,UAAU,KAAK,QAAQ;gBAAE,cAAc,KAAK,YAAY;gBAAE,oBAAoB,EAAE;YAAC;YAE3H,UAAU,CAAC,OAAS;uBAAI;oBAAM;iBAAS;YAEvC,+CAA+C;YAC/C,IAAI,KAAK,IAAI,KAAK,YAAY;gBAC5B,MAAM,gBAAgB,iBAAiB,aAAa,CAAC,AAAC,qBAA0B,OAAN,OAAM;gBAChF,IAAI,eAAe;oBACjB,cAAc,gBAAgB,CAAC,SAAS;wBACtC,UAAU,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,IACzB,EAAE,EAAE,KAAK,SAAS,EAAE,IAAI,KAAK,aACzB;oCAAE,GAAG,CAAC;oCAAE,cAAc,cAAc,SAAS;gCAAC,IAC9C;oBAER;gBACF;YACF;QACF;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,0KAAO;;;;;0BAGR,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,KAAK;wBACL,eAAe;wBACf,8BAA8B;wBAC9B,WAAW,AAAC,6DAA2F,OAA/B,aAAa,eAAe;wBACpG,SAAS;wBACT,QAAQ;wBACR,YAAY;wBACZ,aAAa;4BACX,cAAc;4BACd;wBACF;;;;;;oBAID,gCACC,6LAAC;wBACC,WAAU;wBACV,OAAO;4BACL,MAAM,eAAe,CAAC;4BACtB,KAAK,eAAe,CAAC;wBACvB;;;;;;;;;;;;;;;;;;AAMZ;GAnQgB;;QAMwD,gNAAsB;;;KAN9E", "debugId": null}}, {"offset": {"line": 1247, "column": 0}, "map": {"version": 3, "sources": ["file:///T:/Development/TranscriptionModule/transcription-demo/src/app/report-templates/_components/BlockSidebar.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useMemo, useState } from \"react\";\nimport { Brain, LayoutGrid, List, Search, ChevronDown } from \"lucide-react\";\nimport type { DetectionItem } from \"./types\";\n\nexport function BlockSidebar({ detectionItems }: { detectionItems: DetectionItem[] }) {\n  const [q, setQ] = useState(\"\");\n  const [cat, setCat] = useState(\"all\");\n  const [open, setOpen] = useState<{general: boolean; categories: boolean; findings: boolean}>({ general: true, categories: true, findings: true });\n\n  const categories = useMemo(() => Array.from(new Set(detectionItems.map((d) => d.category))), [detectionItems]);\n  const filtered = useMemo(\n    () => detectionItems.filter((d) => (cat === \"all\" || d.category === cat) && d.name.toLowerCase().includes(q.toLowerCase())),\n    [detectionItems, q, cat]\n  );\n\n  function onDragStart(e: React.DragEvent, type: string, data: Record<string, unknown>) {\n    e.dataTransfer.effectAllowed = \"copy\";\n    e.dataTransfer.setData(\"application/json\", JSON.stringify({ type, ...data }));\n  }\n\n  return (\n    <div className=\"w-80 flex-shrink-0 border-l bg-muted/20 h-full flex flex-col\">\n      <h3 className=\"text-sm font-semibold px-4 py-3 border-b\">Add Blocks</h3>\n      <div className=\"flex-1 overflow-y-auto sidebar-scrollbar\">\n        <div className=\"border-b\">\n          <button className=\"w-full flex items-center justify-between px-3 py-2 text-xs uppercase text-muted-foreground\" onClick={() => setOpen((o) => ({ ...o, general: !o.general }))}>\n            <span>General</span><ChevronDown size={14} className={`transition-transform ${open.general ? \"rotate-180\" : \"\"}`} />\n          </button>\n          {open.general && (\n            <div className=\"px-3 pb-3\">\n              <div\n                draggable\n                onDragStart={(e) => onDragStart(e, \"AI_BLOCK\", {})}\n                className=\"p-2 rounded-lg border-2 border-orange-400 bg-orange-50 text-orange-700 flex items-center gap-2 cursor-grab\"\n              >\n                <Brain size={16} /> <span className=\"text-sm font-medium\">AI Instruction</span>\n              </div>\n            </div>\n          )}\n        </div>\n\n        <div className=\"border-b\">\n          <button className=\"w-full flex items-center justify-between px-3 py-2 text-xs uppercase text-muted-foreground\" onClick={() => setOpen((o) => ({ ...o, categories: !o.categories }))}>\n            <span>Categories</span><ChevronDown size={14} className={`transition-transform ${open.categories ? \"rotate-180\" : \"\"}`} />\n          </button>\n          {open.categories && (\n            <div className=\"px-3 pb-3 space-y-2\">\n              {categories.map((c) => (\n                <div key={c}\n                  draggable\n                  onDragStart={(e) => onDragStart(e, \"CATEGORY_BLOCK\", { category: c, categoryName: c })}\n                  className=\"p-2 rounded-lg border-2 border-border bg-muted/40 text-foreground flex items-center gap-2 cursor-grab\"\n                >\n                  <LayoutGrid size={16} /> <span className=\"text-sm font-medium\">{c}</span>\n                </div>\n              ))}\n            </div>\n          )}\n        </div>\n\n        <div>\n          <button className=\"w-full flex items-center justify-between px-3 py-2 text-xs uppercase text-muted-foreground\" onClick={() => setOpen((o) => ({ ...o, findings: !o.findings }))}>\n            <span>Individual Findings</span><ChevronDown size={14} className={`transition-transform ${open.findings ? \"rotate-180\" : \"\"}`} />\n          </button>\n          {open.findings && (\n            <div className=\"px-3 pb-3 space-y-2\">\n              <div className=\"relative\">\n                <Search size={14} className=\"absolute left-2 top-1/2 -translate-y-1/2 text-muted-foreground\" />\n                <input value={q} onChange={(e) => setQ(e.target.value)} placeholder=\"Search findings...\" className=\"w-full pl-7 pr-2 py-2 border rounded-md text-sm\" />\n              </div>\n              <select value={cat} onChange={(e) => setCat(e.target.value)} className=\"w-full p-2 border rounded-md text-sm\">\n                <option value=\"all\">All Categories</option>\n                {categories.map((c) => (<option key={c} value={c}>{c}</option>))}\n              </select>\n              <div className=\"max-h-[300px] overflow-y-auto sidebar-scrollbar pr-1 space-y-2\">\n                {filtered.map((it) => (\n                  <div key={it.id}\n                    draggable\n                    onDragStart={(e) => onDragStart(e, \"FINDING_BLOCK\", { findingId: it.id, findingName: it.name })}\n                    className=\"p-2 rounded-lg border-2 border-blue-400 bg-blue-50 text-blue-700 flex items-center gap-2 cursor-grab\"\n                  >\n                    <List size={16} /> <span className=\"text-sm font-medium\">{it.name}</span>\n                  </div>\n                ))}\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;AAMO,SAAS,aAAa,KAAuD;QAAvD,EAAE,cAAc,EAAuC,GAAvD;;IAC3B,MAAM,CAAC,GAAG,KAAK,GAAG,IAAA,yKAAQ,EAAC;IAC3B,MAAM,CAAC,KAAK,OAAO,GAAG,IAAA,yKAAQ,EAAC;IAC/B,MAAM,CAAC,MAAM,QAAQ,GAAG,IAAA,yKAAQ,EAA6D;QAAE,SAAS;QAAM,YAAY;QAAM,UAAU;IAAK;IAE/I,MAAM,aAAa,IAAA,wKAAO;4CAAC,IAAM,MAAM,IAAI,CAAC,IAAI,IAAI,eAAe,GAAG;oDAAC,CAAC,IAAM,EAAE,QAAQ;;2CAAK;QAAC;KAAe;IAC7G,MAAM,WAAW,IAAA,wKAAO;0CACtB,IAAM,eAAe,MAAM;kDAAC,CAAC,IAAM,CAAC,QAAQ,SAAS,EAAE,QAAQ,KAAK,GAAG,KAAK,EAAE,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,EAAE,WAAW;;yCACvH;QAAC;QAAgB;QAAG;KAAI;IAG1B,SAAS,YAAY,CAAkB,EAAE,IAAY,EAAE,IAA6B;QAClF,EAAE,YAAY,CAAC,aAAa,GAAG;QAC/B,EAAE,YAAY,CAAC,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC;YAAE;YAAM,GAAG,IAAI;QAAC;IAC5E;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAA2C;;;;;;0BACzD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAO,WAAU;gCAA6F,SAAS,IAAM,QAAQ,CAAC,IAAM,CAAC;4CAAE,GAAG,CAAC;4CAAE,SAAS,CAAC,EAAE,OAAO;wCAAC,CAAC;;kDACzK,6LAAC;kDAAK;;;;;;kDAAc,6LAAC,sOAAW;wCAAC,MAAM;wCAAI,WAAW,AAAC,wBAAwD,OAAjC,KAAK,OAAO,GAAG,eAAe;;;;;;;;;;;;4BAE7G,KAAK,OAAO,kBACX,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,SAAS;oCACT,aAAa,CAAC,IAAM,YAAY,GAAG,YAAY,CAAC;oCAChD,WAAU;;sDAEV,6LAAC,gNAAK;4CAAC,MAAM;;;;;;wCAAM;sDAAC,6LAAC;4CAAK,WAAU;sDAAsB;;;;;;;;;;;;;;;;;;;;;;;kCAMlE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAO,WAAU;gCAA6F,SAAS,IAAM,QAAQ,CAAC,IAAM,CAAC;4CAAE,GAAG,CAAC;4CAAE,YAAY,CAAC,EAAE,UAAU;wCAAC,CAAC;;kDAC/K,6LAAC;kDAAK;;;;;;kDAAiB,6LAAC,sOAAW;wCAAC,MAAM;wCAAI,WAAW,AAAC,wBAA2D,OAApC,KAAK,UAAU,GAAG,eAAe;;;;;;;;;;;;4BAEnH,KAAK,UAAU,kBACd,6LAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC,kBACf,6LAAC;wCACC,SAAS;wCACT,aAAa,CAAC,IAAM,YAAY,GAAG,kBAAkB;gDAAE,UAAU;gDAAG,cAAc;4CAAE;wCACpF,WAAU;;0DAEV,6LAAC,mOAAU;gDAAC,MAAM;;;;;;4CAAM;0DAAC,6LAAC;gDAAK,WAAU;0DAAuB;;;;;;;uCALxD;;;;;;;;;;;;;;;;kCAYlB,6LAAC;;0CACC,6LAAC;gCAAO,WAAU;gCAA6F,SAAS,IAAM,QAAQ,CAAC,IAAM,CAAC;4CAAE,GAAG,CAAC;4CAAE,UAAU,CAAC,EAAE,QAAQ;wCAAC,CAAC;;kDAC3K,6LAAC;kDAAK;;;;;;kDAA0B,6LAAC,sOAAW;wCAAC,MAAM;wCAAI,WAAW,AAAC,wBAAyD,OAAlC,KAAK,QAAQ,GAAG,eAAe;;;;;;;;;;;;4BAE1H,KAAK,QAAQ,kBACZ,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,mNAAM;gDAAC,MAAM;gDAAI,WAAU;;;;;;0DAC5B,6LAAC;gDAAM,OAAO;gDAAG,UAAU,CAAC,IAAM,KAAK,EAAE,MAAM,CAAC,KAAK;gDAAG,aAAY;gDAAqB,WAAU;;;;;;;;;;;;kDAErG,6LAAC;wCAAO,OAAO;wCAAK,UAAU,CAAC,IAAM,OAAO,EAAE,MAAM,CAAC,KAAK;wCAAG,WAAU;;0DACrE,6LAAC;gDAAO,OAAM;0DAAM;;;;;;4CACnB,WAAW,GAAG,CAAC,CAAC,kBAAO,6LAAC;oDAAe,OAAO;8DAAI;mDAAd;;;;;;;;;;;kDAEvC,6LAAC;wCAAI,WAAU;kDACZ,SAAS,GAAG,CAAC,CAAC,mBACb,6LAAC;gDACC,SAAS;gDACT,aAAa,CAAC,IAAM,YAAY,GAAG,iBAAiB;wDAAE,WAAW,GAAG,EAAE;wDAAE,aAAa,GAAG,IAAI;oDAAC;gDAC7F,WAAU;;kEAEV,6LAAC,6MAAI;wDAAC,MAAM;;;;;;oDAAM;kEAAC,6LAAC;wDAAK,WAAU;kEAAuB,GAAG,IAAI;;;;;;;+CALzD,GAAG,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAejC;GAvFgB;KAAA", "debugId": null}}, {"offset": {"line": 1615, "column": 0}, "map": {"version": 3, "sources": ["file:///T:/Development/TranscriptionModule/transcription-demo/src/app/report-templates/_components/ReportPreview.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport { Button } from \"@/components/ui/button\";\nimport type { AutoDetectedFinding, Block, DetectionItem } from \"./types\";\n\nexport function ReportPreview({ blocks, detectionItems }: { blocks: Block[]; detectionItems: DetectionItem[] }) {\n  const [report, setReport] = useState<string>(\"\");\n  const [loading, setLoading] = useState(false);\n\n  function valueForFinding(id: string): string {\n    // Demo resolver: just return placeholder or the finding name\n    const meta = detectionItems.find((d) => d.id === id);\n    return meta ? `[${meta.name} value]` : \"[value]\";\n  }\n\n  async function generate() {\n    setLoading(true);\n    let content = \"\";\n    for (const b of blocks) {\n      if (b.type === \"TEXT\") {\n        const div = document.createElement(\"div\");\n        div.innerHTML = b.content;\n        content += div.innerText + \"\\n\";\n      } else if (b.type === \"AI_BLOCK\") {\n        content += `\\n--- AI Summary for \"${stripHtml(b.innerContent).slice(0, 80)}\" ---\\n[Simulating AI response...]\\n\\n`;\n      } else if (b.type === \"FINDING_BLOCK\") {\n        content += ` ${valueForFinding(b.findingId)} `;\n      } else if (b.type === \"CATEGORY_BLOCK\") {\n        content += `\\n**${b.categoryName}**\\n`;\n        for (const id of b.selectedFindingIds) {\n          const meta = detectionItems.find((d) => d.id === id);\n          content += `- ${meta?.name ?? id}: ${valueForFinding(id)}\\n`;\n        }\n        content += \"\\n\";\n      }\n    }\n    await new Promise((r) => setTimeout(r, 500));\n    setReport(content.replace(\"[Simulating AI response...]\", \"Based on findings, values are within expected ranges.\"));\n    setLoading(false);\n  }\n\n  return (\n    <div className=\"flex flex-col h-full\">\n      <div className=\"flex items-center justify-between gap-2 mb-2\">\n        <Button onClick={generate} disabled={loading || blocks.length === 0}>{loading ? \"Generating...\" : \"Generate Report\"}</Button>\n      </div>\n      <div className=\"flex-1 bg-muted/30 rounded-md p-3 whitespace-pre-wrap font-mono text-xs overflow-auto\">\n        {report || \"Report preview will appear here...\"}\n      </div>\n    </div>\n  );\n}\n\nfunction stripHtml(html: string) {\n  const d = document.createElement(\"div\"); d.innerHTML = html; return d.innerText;\n}\n\n"], "names": [], "mappings": ";;;;;AAEA;AACA;;;AAHA;;;AAMO,SAAS,cAAc,KAAgF;QAAhF,EAAE,MAAM,EAAE,cAAc,EAAwD,GAAhF;;IAC5B,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,yKAAQ,EAAS;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,yKAAQ,EAAC;IAEvC,SAAS,gBAAgB,EAAU;QACjC,6DAA6D;QAC7D,MAAM,OAAO,eAAe,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;QACjD,OAAO,OAAO,AAAC,IAAa,OAAV,KAAK,IAAI,EAAC,aAAW;IACzC;IAEA,eAAe;QACb,WAAW;QACX,IAAI,UAAU;QACd,KAAK,MAAM,KAAK,OAAQ;YACtB,IAAI,EAAE,IAAI,KAAK,QAAQ;gBACrB,MAAM,MAAM,SAAS,aAAa,CAAC;gBACnC,IAAI,SAAS,GAAG,EAAE,OAAO;gBACzB,WAAW,IAAI,SAAS,GAAG;YAC7B,OAAO,IAAI,EAAE,IAAI,KAAK,YAAY;gBAChC,WAAW,AAAC,yBAA+D,OAAvC,UAAU,EAAE,YAAY,EAAE,KAAK,CAAC,GAAG,KAAI;YAC7E,OAAO,IAAI,EAAE,IAAI,KAAK,iBAAiB;gBACrC,WAAW,AAAC,IAAgC,OAA7B,gBAAgB,EAAE,SAAS,GAAE;YAC9C,OAAO,IAAI,EAAE,IAAI,KAAK,kBAAkB;gBACtC,WAAW,AAAC,OAAqB,OAAf,EAAE,YAAY,EAAC;gBACjC,KAAK,MAAM,MAAM,EAAE,kBAAkB,CAAE;oBACrC,MAAM,OAAO,eAAe,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;wBACjC;oBAAhB,WAAW,AAAC,KAAyB,OAArB,CAAA,aAAA,iBAAA,2BAAA,KAAM,IAAI,cAAV,wBAAA,aAAc,IAAG,MAAwB,OAApB,gBAAgB,KAAI;gBAC3D;gBACA,WAAW;YACb;QACF;QACA,MAAM,IAAI,QAAQ,CAAC,IAAM,WAAW,GAAG;QACvC,UAAU,QAAQ,OAAO,CAAC,+BAA+B;QACzD,WAAW;IACb;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,+IAAM;oBAAC,SAAS;oBAAU,UAAU,WAAW,OAAO,MAAM,KAAK;8BAAI,UAAU,kBAAkB;;;;;;;;;;;0BAEpG,6LAAC;gBAAI,WAAU;0BACZ,UAAU;;;;;;;;;;;;AAInB;GA9CgB;KAAA;AAgDhB,SAAS,UAAU,IAAY;IAC7B,MAAM,IAAI,SAAS,aAAa,CAAC;IAAQ,EAAE,SAAS,GAAG;IAAM,OAAO,EAAE,SAAS;AACjF", "debugId": null}}, {"offset": {"line": 1713, "column": 0}, "map": {"version": 3, "sources": ["file:///T:/Development/TranscriptionModule/transcription-demo/src/app/report-templates/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useEffect, useMemo, useState } from \"react\";\nimport { WorkspaceNav } from \"@/components/workspace-nav\";\nimport { Button } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\";\nimport { getConvex } from \"@/lib/convexClient\";\n\nimport { EditorCanvas } from \"./_components/EditorCanvas\";\nimport { BlockSidebar } from \"./_components/BlockSidebar\";\nimport { ReportPreview } from \"./_components/ReportPreview\";\nimport type { Block, DetectionItem } from \"./_components/types\";\n\nexport default function ReportTemplatesPage() {\n  const [title, setTitle] = useState(\"Consultation Report\");\n  const [blocks, setBlocks] = useState<Block[]>([\n    { id: crypto.randomUUID(), type: \"TEXT\", content: \"<h2>Consultation Report</h2><div>Start typing your report here...</div>\" },\n  ]);\n  const [detectionItems, setDetectionItems] = useState<DetectionItem[]>([]);\n  const [previewOpen, setPreviewOpen] = useState(false);\n\n  const convex = getConvex();\n  const loose = convex as unknown as { query: (name: string, args?: unknown) => Promise<unknown>; mutation: (name: string, args?: unknown) => Promise<unknown>; action: (name: string, args?: unknown) => Promise<unknown>; };\n\n  // Template management\n  type RawTemplate = { _id?: string; id?: string; title?: string; blocks?: unknown[]; published?: boolean; ownerUserId?: string; updatedAt?: number };\n  const [templates, setTemplates] = useState<Array<{ id?: string; title: string; blocks: Block[] }>>([]);\n  const [currentId, setCurrentId] = useState<string | undefined>(undefined);\n\n  function normalizeBlocks(rawBlocks: unknown[] | undefined): Block[] {\n    if (!rawBlocks) return [];\n    return rawBlocks.map((rb) => {\n      const any = rb as any;\n      if (any && typeof any === \"object\" && typeof any.type === \"string\") return any as Block;\n      // Convert prior editor shapes to TEXT for compatibility\n      if (any.kind === \"text\") return { id: crypto.randomUUID(), type: \"TEXT\", content: (any.value ?? \"\") as string } as Block;\n      if (any.kind === \"section\") return { id: crypto.randomUUID(), type: \"TEXT\", content: `<h3>${any.value ?? \"\"}</h3>` } as Block;\n      if (any.kind === \"field\") return { id: crypto.randomUUID(), type: \"TEXT\", content: `<p><strong>${any.label ?? any.value ?? \"Field\"}</strong>: ______</p>` } as Block;\n      return { id: crypto.randomUUID(), type: \"TEXT\", content: \"\" } as Block;\n    });\n  }\n\n  function normalizeTemplate(raw: unknown) {\n    const r = raw as RawTemplate;\n    const id = r.id ?? (r._id ? String(r._id) : undefined);\n    return { id, title: r.title ?? \"Untitled\", blocks: normalizeBlocks(r.blocks) } as { id?: string; title: string; blocks: Block[] };\n  }\n\n  // Load templates from Convex (only once on mount)\n  useEffect(() => {\n    let ignore = false;\n    async function loadTemplates() {\n      if (!convex) return;\n      try {\n        const list = await (loose.query(\"templates:listTemplates\", {}) as Promise<unknown>);\n        const items = (list as unknown[]).map(normalizeTemplate);\n        if (!ignore) {\n          setTemplates(items);\n          // Only set initial template if we don't have one selected yet\n          if (!currentId && items.length > 0) {\n            const first = items[0];\n            setCurrentId(first.id);\n            setTitle(first.title);\n            setBlocks(first.blocks);\n          }\n        }\n      } catch {}\n    }\n    loadTemplates();\n    return () => { ignore = true; };\n  }, [convex, loose]); // Removed currentId dependency to prevent overwriting\n\n  // Load available definitions for sidebar (Convex)\n  useEffect(() => {\n    let cancelled = false;\n    async function load() {\n      if (!convex) return;\n      try {\n        const res = await loose.query(\"detections:listDefinitions\", {});\n        const items = (res as unknown[]).map((r) => {\n          const doc = r as { id?: string; _id?: string; name?: string; category?: string };\n          const id = doc.id ?? (doc._id ? String(doc._id) : crypto.randomUUID());\n          return { id, name: doc.name ?? \"\", category: doc.category ?? \"Uncategorized\" } as DetectionItem;\n        });\n        if (!cancelled) setDetectionItems(items);\n      } catch {}\n    }\n    load();\n    return () => { cancelled = true; };\n  }, [convex, loose]);\n\n  const leftPanel = (\n    <div className=\"flex flex-col h-[calc(100vh-8rem)]\">\n      {/* Editor Panel (expanded by default, collapses when preview is open) */}\n      {!previewOpen ? (\n        <div className=\"flex-1 flex flex-col gap-3\">\n          <div className=\"flex items-center justify-between gap-2\">\n            <Input value={title} onChange={(e) => setTitle(e.target.value)} className=\"h-9\" />\n          </div>\n          <EditorCanvas blocks={blocks} setBlocks={(updater) => setBlocks((prev) => updater(prev))} detectionItems={detectionItems} />\n        </div>\n      ) : (\n        <div className=\"shrink-0 border rounded-md px-3 h-10 flex items-center justify-between\">\n          <div className=\"text-sm text-muted-foreground truncate\">(v) Hide Preview • {title}</div>\n          <Button variant=\"ghost\" size=\"sm\" onClick={() => setPreviewOpen(false)}>Hide Preview</Button>\n        </div>\n      )}\n\n      {/* Preview Panel (collapsed footer by default) */}\n      {!previewOpen ? (\n        <div className=\"shrink-0 border rounded-md px-3 py-2 mt-3 flex items-center justify-between bg-muted/20\">\n          <div className=\"text-sm text-muted-foreground\">(^) Show Preview</div>\n          <div className=\"flex items-center gap-2\">\n            <Button variant=\"secondary\" onClick={() => setPreviewOpen(true)}>Show Preview</Button>\n          </div>\n        </div>\n      ) : (\n        <div className=\"flex-1 mt-3 min-h-0\">\n          <ReportPreview blocks={blocks} detectionItems={detectionItems} />\n        </div>\n      )}\n    </div>\n  );\n\n  return (\n    <div className=\"container mx-auto p-6 space-y-4\">\n      <WorkspaceNav />\n      <style>{`[data-placeholder]:empty::before { content: attr(data-placeholder); color: #9ca3af; font-style: italic; } .sidebar-scrollbar::-webkit-scrollbar{width:8px}.sidebar-scrollbar::-webkit-scrollbar-thumb{background:#d1d5db;border-radius:4px}.sidebar-scrollbar::-webkit-scrollbar-thumb:hover{background:#9ca3af}`}</style>\n\n      {/* Template Management Header */}\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center gap-2\">\n          <span className=\"text-sm text-muted-foreground\">Template</span>\n          <Select value={currentId ?? \"\"} onValueChange={(id) => {\n            setCurrentId(id);\n            const t = templates.find((x) => x.id === id);\n            if (t) { setTitle(t.title); setBlocks(t.blocks); }\n          }}>\n            <SelectTrigger className=\"w-[260px]\"><SelectValue placeholder=\"Select template\" /></SelectTrigger>\n            <SelectContent>\n              {templates.map((t) => (<SelectItem key={t.id} value={t.id!}>{t.title}</SelectItem>))}\n            </SelectContent>\n          </Select>\n          <Button variant=\"outline\" onClick={() => {\n            const id = `local_${crypto.randomUUID()}`;\n            const t = { id, title: \"Untitled Template\", blocks: [] as Block[] };\n            setTemplates((prev) => [...prev, t]);\n            setCurrentId(id);\n            setTitle(t.title);\n            setBlocks(t.blocks);\n          }}>New</Button>\n          <Button variant=\"outline\" onClick={() => {\n            if (!currentId) return;\n            const base = templates.find((t) => t.id === currentId);\n            if (!base) return;\n            const id = `local_${crypto.randomUUID()}`;\n            const title = `${base.title} (copy)`;\n            const blocks = base.blocks.map((b) => ({ ...b, id: crypto.randomUUID() })) as Block[];\n            const clone = { id, title, blocks };\n            setTemplates((prev) => [...prev, clone]);\n            setCurrentId(id);\n            setTitle(title);\n            setBlocks(blocks);\n          }}>Clone</Button>\n          <Button variant=\"secondary\" onClick={async () => {\n            if (convex) {\n              await loose.mutation(\"templates:upsertTemplate\", { id: currentId, title, blocks, published: false, ownerUserId: \"demo-user\" });\n              const list = await (loose.query(\"templates:listTemplates\", {}) as Promise<unknown>);\n              setTemplates((list as unknown[]).map(normalizeTemplate));\n            }\n          }}>Save</Button>\n        </div>\n      </div>\n\n      <div className=\"grid grid-cols-[1fr_320px] gap-4\">\n        {leftPanel}\n        <BlockSidebar detectionItems={detectionItems} />\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;;;AAXA;;;;;;;;;;AAce,SAAS;;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,yKAAQ,EAAC;IACnC,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,yKAAQ,EAAU;QAC5C;YAAE,IAAI,OAAO,UAAU;YAAI,MAAM;YAAQ,SAAS;QAA0E;KAC7H;IACD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,yKAAQ,EAAkB,EAAE;IACxE,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,yKAAQ,EAAC;IAE/C,MAAM,SAAS,IAAA,0IAAS;IACxB,MAAM,QAAQ;IAId,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,yKAAQ,EAAyD,EAAE;IACrG,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,yKAAQ,EAAqB;IAE/D,SAAS,gBAAgB,SAAgC;QACvD,IAAI,CAAC,WAAW,OAAO,EAAE;QACzB,OAAO,UAAU,GAAG,CAAC,CAAC;YACpB,MAAM,MAAM;YACZ,IAAI,OAAO,OAAO,QAAQ,YAAY,OAAO,IAAI,IAAI,KAAK,UAAU,OAAO;gBAEQ;YADnF,wDAAwD;YACxD,IAAI,IAAI,IAAI,KAAK,QAAQ,OAAO;gBAAE,IAAI,OAAO,UAAU;gBAAI,MAAM;gBAAQ,SAAU,CAAA,aAAA,IAAI,KAAK,cAAT,wBAAA,aAAa;YAAc;gBAClB;YAA5F,IAAI,IAAI,IAAI,KAAK,WAAW,OAAO;gBAAE,IAAI,OAAO,UAAU;gBAAI,MAAM;gBAAQ,SAAS,AAAC,OAAsB,OAAhB,CAAA,cAAA,IAAI,KAAK,cAAT,yBAAA,cAAa,IAAG;YAAO;gBAClB,YAAA;YAAjG,IAAI,IAAI,IAAI,KAAK,SAAS,OAAO;gBAAE,IAAI,OAAO,UAAU;gBAAI,MAAM;gBAAQ,SAAS,AAAC,cAA+C,OAAlC,CAAA,OAAA,CAAA,aAAA,IAAI,KAAK,cAAT,wBAAA,aAAa,IAAI,KAAK,cAAtB,kBAAA,OAA0B,SAAQ;YAAuB;YAC1J,OAAO;gBAAE,IAAI,OAAO,UAAU;gBAAI,MAAM;gBAAQ,SAAS;YAAG;QAC9D;IACF;IAEA,SAAS,kBAAkB,GAAY;QACrC,MAAM,IAAI;YACC;QAAX,MAAM,KAAK,CAAA,QAAA,EAAE,EAAE,cAAJ,mBAAA,QAAS,EAAE,GAAG,GAAG,OAAO,EAAE,GAAG,IAAI;YACxB;QAApB,OAAO;YAAE;YAAI,OAAO,CAAA,WAAA,EAAE,KAAK,cAAP,sBAAA,WAAW;YAAY,QAAQ,gBAAgB,EAAE,MAAM;QAAE;IAC/E;IAEA,kDAAkD;IAClD,IAAA,0KAAS;yCAAC;YACR,IAAI,SAAS;YACb,eAAe;gBACb,IAAI,CAAC,QAAQ;gBACb,IAAI;oBACF,MAAM,OAAO,MAAO,MAAM,KAAK,CAAC,2BAA2B,CAAC;oBAC5D,MAAM,QAAQ,AAAC,KAAmB,GAAG,CAAC;oBACtC,IAAI,CAAC,QAAQ;wBACX,aAAa;wBACb,8DAA8D;wBAC9D,IAAI,CAAC,aAAa,MAAM,MAAM,GAAG,GAAG;4BAClC,MAAM,QAAQ,KAAK,CAAC,EAAE;4BACtB,aAAa,MAAM,EAAE;4BACrB,SAAS,MAAM,KAAK;4BACpB,UAAU,MAAM,MAAM;wBACxB;oBACF;gBACF,EAAE,UAAM,CAAC;YACX;YACA;YACA;iDAAO;oBAAQ,SAAS;gBAAM;;QAChC;wCAAG;QAAC;QAAQ;KAAM,GAAG,sDAAsD;IAE3E,kDAAkD;IAClD,IAAA,0KAAS;yCAAC;YACR,IAAI,YAAY;YAChB,eAAe;gBACb,IAAI,CAAC,QAAQ;gBACb,IAAI;oBACF,MAAM,MAAM,MAAM,MAAM,KAAK,CAAC,8BAA8B,CAAC;oBAC7D,MAAM,QAAQ,AAAC,IAAkB,GAAG;oEAAC,CAAC;4BACpC,MAAM,MAAM;gCACD;4BAAX,MAAM,KAAK,CAAA,UAAA,IAAI,EAAE,cAAN,qBAAA,UAAW,IAAI,GAAG,GAAG,OAAO,IAAI,GAAG,IAAI,OAAO,UAAU;gCAChD,WAA0B;4BAA7C,OAAO;gCAAE;gCAAI,MAAM,CAAA,YAAA,IAAI,IAAI,cAAR,uBAAA,YAAY;gCAAI,UAAU,CAAA,gBAAA,IAAI,QAAQ,cAAZ,2BAAA,gBAAgB;4BAAgB;wBAC/E;;oBACA,IAAI,CAAC,WAAW,kBAAkB;gBACpC,EAAE,UAAM,CAAC;YACX;YACA;YACA;iDAAO;oBAAQ,YAAY;gBAAM;;QACnC;wCAAG;QAAC;QAAQ;KAAM;IAElB,MAAM,0BACJ,6LAAC;QAAI,WAAU;;YAEZ,CAAC,4BACA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,6IAAK;4BAAC,OAAO;4BAAO,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;4BAAG,WAAU;;;;;;;;;;;kCAE5E,6LAAC,oLAAY;wBAAC,QAAQ;wBAAQ,WAAW,CAAC,UAAY,UAAU,CAAC,OAAS,QAAQ;wBAAQ,gBAAgB;;;;;;;;;;;qCAG5G,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;4BAAyC;4BAAoB;;;;;;;kCAC5E,6LAAC,+IAAM;wBAAC,SAAQ;wBAAQ,MAAK;wBAAK,SAAS,IAAM,eAAe;kCAAQ;;;;;;;;;;;;YAK3E,CAAC,4BACA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCAAgC;;;;;;kCAC/C,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,+IAAM;4BAAC,SAAQ;4BAAY,SAAS,IAAM,eAAe;sCAAO;;;;;;;;;;;;;;;;qCAIrE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,sLAAa;oBAAC,QAAQ;oBAAQ,gBAAgB;;;;;;;;;;;;;;;;;IAMvD,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,yJAAY;;;;;0BACb,6LAAC;0BAAQ;;;;;;0BAGT,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAK,WAAU;sCAAgC;;;;;;sCAChD,6LAAC,+IAAM;4BAAC,OAAO,sBAAA,uBAAA,YAAa;4BAAI,eAAe,CAAC;gCAC9C,aAAa;gCACb,MAAM,IAAI,UAAU,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;gCACzC,IAAI,GAAG;oCAAE,SAAS,EAAE,KAAK;oCAAG,UAAU,EAAE,MAAM;gCAAG;4BACnD;;8CACE,6LAAC,sJAAa;oCAAC,WAAU;8CAAY,cAAA,6LAAC,oJAAW;wCAAC,aAAY;;;;;;;;;;;8CAC9D,6LAAC,sJAAa;8CACX,UAAU,GAAG,CAAC,CAAC,kBAAO,6LAAC,mJAAU;4CAAY,OAAO,EAAE,EAAE;sDAAI,EAAE,KAAK;2CAA5B,EAAE,EAAE;;;;;;;;;;;;;;;;sCAGhD,6LAAC,+IAAM;4BAAC,SAAQ;4BAAU,SAAS;gCACjC,MAAM,KAAK,AAAC,SAA4B,OAApB,OAAO,UAAU;gCACrC,MAAM,IAAI;oCAAE;oCAAI,OAAO;oCAAqB,QAAQ,EAAE;gCAAY;gCAClE,aAAa,CAAC,OAAS;2CAAI;wCAAM;qCAAE;gCACnC,aAAa;gCACb,SAAS,EAAE,KAAK;gCAChB,UAAU,EAAE,MAAM;4BACpB;sCAAG;;;;;;sCACH,6LAAC,+IAAM;4BAAC,SAAQ;4BAAU,SAAS;gCACjC,IAAI,CAAC,WAAW;gCAChB,MAAM,OAAO,UAAU,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;gCAC5C,IAAI,CAAC,MAAM;gCACX,MAAM,KAAK,AAAC,SAA4B,OAApB,OAAO,UAAU;gCACrC,MAAM,QAAQ,AAAC,GAAa,OAAX,KAAK,KAAK,EAAC;gCAC5B,MAAM,SAAS,KAAK,MAAM,CAAC,GAAG,CAAC,CAAC,IAAM,CAAC;wCAAE,GAAG,CAAC;wCAAE,IAAI,OAAO,UAAU;oCAAG,CAAC;gCACxE,MAAM,QAAQ;oCAAE;oCAAI;oCAAO;gCAAO;gCAClC,aAAa,CAAC,OAAS;2CAAI;wCAAM;qCAAM;gCACvC,aAAa;gCACb,SAAS;gCACT,UAAU;4BACZ;sCAAG;;;;;;sCACH,6LAAC,+IAAM;4BAAC,SAAQ;4BAAY,SAAS;gCACnC,IAAI,QAAQ;oCACV,MAAM,MAAM,QAAQ,CAAC,4BAA4B;wCAAE,IAAI;wCAAW;wCAAO;wCAAQ,WAAW;wCAAO,aAAa;oCAAY;oCAC5H,MAAM,OAAO,MAAO,MAAM,KAAK,CAAC,2BAA2B,CAAC;oCAC5D,aAAa,AAAC,KAAmB,GAAG,CAAC;gCACvC;4BACF;sCAAG;;;;;;;;;;;;;;;;;0BAIP,6LAAC;gBAAI,WAAU;;oBACZ;kCACD,6LAAC,oLAAY;wBAAC,gBAAgB;;;;;;;;;;;;;;;;;;AAItC;GAvKwB;KAAA", "debugId": null}}]}