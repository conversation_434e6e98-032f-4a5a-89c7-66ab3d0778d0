module.exports = [
"[project]/node_modules/convex/dist/esm/index.js [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "version",
    ()=>version
]);
"use strict";
const version = "1.27.0"; //# sourceMappingURL=index.js.map
}),
"[project]/node_modules/convex/dist/esm/values/base64.js [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "byteLength",
    ()=>byteLength,
    "fromByteArray",
    ()=>fromByteArray,
    "fromByteArrayUrlSafeNoPadding",
    ()=>fromByteArrayUrlSafeNoPadding,
    "toByteArray",
    ()=>toByteArray
]);
"use strict";
var lookup = [];
var revLookup = [];
var Arr = Uint8Array;
var code = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
for(var i = 0, len = code.length; i < len; ++i){
    lookup[i] = code[i];
    revLookup[code.charCodeAt(i)] = i;
}
revLookup["-".charCodeAt(0)] = 62;
revLookup["_".charCodeAt(0)] = 63;
function getLens(b64) {
    var len = b64.length;
    if (len % 4 > 0) {
        throw new Error("Invalid string. Length must be a multiple of 4");
    }
    var validLen = b64.indexOf("=");
    if (validLen === -1) validLen = len;
    var placeHoldersLen = validLen === len ? 0 : 4 - validLen % 4;
    return [
        validLen,
        placeHoldersLen
    ];
}
function byteLength(b64) {
    var lens = getLens(b64);
    var validLen = lens[0];
    var placeHoldersLen = lens[1];
    return (validLen + placeHoldersLen) * 3 / 4 - placeHoldersLen;
}
function _byteLength(_b64, validLen, placeHoldersLen) {
    return (validLen + placeHoldersLen) * 3 / 4 - placeHoldersLen;
}
function toByteArray(b64) {
    var tmp;
    var lens = getLens(b64);
    var validLen = lens[0];
    var placeHoldersLen = lens[1];
    var arr = new Arr(_byteLength(b64, validLen, placeHoldersLen));
    var curByte = 0;
    var len = placeHoldersLen > 0 ? validLen - 4 : validLen;
    var i;
    for(i = 0; i < len; i += 4){
        tmp = revLookup[b64.charCodeAt(i)] << 18 | revLookup[b64.charCodeAt(i + 1)] << 12 | revLookup[b64.charCodeAt(i + 2)] << 6 | revLookup[b64.charCodeAt(i + 3)];
        arr[curByte++] = tmp >> 16 & 255;
        arr[curByte++] = tmp >> 8 & 255;
        arr[curByte++] = tmp & 255;
    }
    if (placeHoldersLen === 2) {
        tmp = revLookup[b64.charCodeAt(i)] << 2 | revLookup[b64.charCodeAt(i + 1)] >> 4;
        arr[curByte++] = tmp & 255;
    }
    if (placeHoldersLen === 1) {
        tmp = revLookup[b64.charCodeAt(i)] << 10 | revLookup[b64.charCodeAt(i + 1)] << 4 | revLookup[b64.charCodeAt(i + 2)] >> 2;
        arr[curByte++] = tmp >> 8 & 255;
        arr[curByte++] = tmp & 255;
    }
    return arr;
}
function tripletToBase64(num) {
    return lookup[num >> 18 & 63] + lookup[num >> 12 & 63] + lookup[num >> 6 & 63] + lookup[num & 63];
}
function encodeChunk(uint8, start, end) {
    var tmp;
    var output = [];
    for(var i = start; i < end; i += 3){
        tmp = (uint8[i] << 16 & 16711680) + (uint8[i + 1] << 8 & 65280) + (uint8[i + 2] & 255);
        output.push(tripletToBase64(tmp));
    }
    return output.join("");
}
function fromByteArray(uint8) {
    var tmp;
    var len = uint8.length;
    var extraBytes = len % 3;
    var parts = [];
    var maxChunkLength = 16383;
    for(var i = 0, len2 = len - extraBytes; i < len2; i += maxChunkLength){
        parts.push(encodeChunk(uint8, i, i + maxChunkLength > len2 ? len2 : i + maxChunkLength));
    }
    if (extraBytes === 1) {
        tmp = uint8[len - 1];
        parts.push(lookup[tmp >> 2] + lookup[tmp << 4 & 63] + "==");
    } else if (extraBytes === 2) {
        tmp = (uint8[len - 2] << 8) + uint8[len - 1];
        parts.push(lookup[tmp >> 10] + lookup[tmp >> 4 & 63] + lookup[tmp << 2 & 63] + "=");
    }
    return parts.join("");
}
function fromByteArrayUrlSafeNoPadding(uint8) {
    return fromByteArray(uint8).replace(/\+/g, "-").replace(/\//g, "_").replace(/=/g, "");
} //# sourceMappingURL=base64.js.map
}),
"[project]/node_modules/convex/dist/esm/common/index.js [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "isSimpleObject",
    ()=>isSimpleObject,
    "parseArgs",
    ()=>parseArgs,
    "validateDeploymentUrl",
    ()=>validateDeploymentUrl
]);
"use strict";
function parseArgs(args) {
    if (args === void 0) {
        return {};
    }
    if (!isSimpleObject(args)) {
        throw new Error(`The arguments to a Convex function must be an object. Received: ${args}`);
    }
    return args;
}
function validateDeploymentUrl(deploymentUrl) {
    if (typeof deploymentUrl === "undefined") {
        throw new Error(`Client created with undefined deployment address. If you used an environment variable, check that it's set.`);
    }
    if (typeof deploymentUrl !== "string") {
        throw new Error(`Invalid deployment address: found ${deploymentUrl}".`);
    }
    if (!(deploymentUrl.startsWith("http:") || deploymentUrl.startsWith("https:"))) {
        throw new Error(`Invalid deployment address: Must start with "https://" or "http://". Found "${deploymentUrl}".`);
    }
    try {
        new URL(deploymentUrl);
    } catch  {
        throw new Error(`Invalid deployment address: "${deploymentUrl}" is not a valid URL. If you believe this URL is correct, use the \`skipConvexDeploymentUrlCheck\` option to bypass this.`);
    }
    if (deploymentUrl.endsWith(".convex.site")) {
        throw new Error(`Invalid deployment address: "${deploymentUrl}" ends with .convex.site, which is used for HTTP Actions. Convex deployment URLs typically end with .convex.cloud? If you believe this URL is correct, use the \`skipConvexDeploymentUrlCheck\` option to bypass this.`);
    }
}
function isSimpleObject(value) {
    const isObject = typeof value === "object";
    const prototype = Object.getPrototypeOf(value);
    const isSimple = prototype === null || prototype === Object.prototype || // Objects generated from other contexts (e.g. across Node.js `vm` modules) will not satisfy the previous
    // conditions but are still simple objects.
    prototype?.constructor?.name === "Object";
    return isObject && isSimple;
} //# sourceMappingURL=index.js.map
}),
"[project]/node_modules/convex/dist/esm/values/value.js [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "base64ToBigInt",
    ()=>base64ToBigInt,
    "bigIntToBase64",
    ()=>bigIntToBase64,
    "convexOrUndefinedToJson",
    ()=>convexOrUndefinedToJson,
    "convexToJson",
    ()=>convexToJson,
    "jsonToConvex",
    ()=>jsonToConvex,
    "modernBase64ToBigInt",
    ()=>modernBase64ToBigInt,
    "modernBigIntToBase64",
    ()=>modernBigIntToBase64,
    "patchValueToJson",
    ()=>patchValueToJson,
    "slowBase64ToBigInt",
    ()=>slowBase64ToBigInt,
    "slowBigIntToBase64",
    ()=>slowBigIntToBase64,
    "stringifyValueForError",
    ()=>stringifyValueForError
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$base64$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/values/base64.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$common$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/common/index.js [app-ssr] (ecmascript)");
"use strict";
;
;
const LITTLE_ENDIAN = true;
const MIN_INT64 = BigInt("-9223372036854775808");
const MAX_INT64 = BigInt("9223372036854775807");
const ZERO = BigInt("0");
const EIGHT = BigInt("8");
const TWOFIFTYSIX = BigInt("256");
function isSpecial(n) {
    return Number.isNaN(n) || !Number.isFinite(n) || Object.is(n, -0);
}
function slowBigIntToBase64(value) {
    if (value < ZERO) {
        value -= MIN_INT64 + MIN_INT64;
    }
    let hex = value.toString(16);
    if (hex.length % 2 === 1) hex = "0" + hex;
    const bytes = new Uint8Array(new ArrayBuffer(8));
    let i = 0;
    for (const hexByte of hex.match(/.{2}/g).reverse()){
        bytes.set([
            parseInt(hexByte, 16)
        ], i++);
        value >>= EIGHT;
    }
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$base64$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["fromByteArray"](bytes);
}
function slowBase64ToBigInt(encoded) {
    const integerBytes = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$base64$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toByteArray"](encoded);
    if (integerBytes.byteLength !== 8) {
        throw new Error(`Received ${integerBytes.byteLength} bytes, expected 8 for $integer`);
    }
    let value = ZERO;
    let power = ZERO;
    for (const byte of integerBytes){
        value += BigInt(byte) * TWOFIFTYSIX ** power;
        power++;
    }
    if (value > MAX_INT64) {
        value += MIN_INT64 + MIN_INT64;
    }
    return value;
}
function modernBigIntToBase64(value) {
    if (value < MIN_INT64 || MAX_INT64 < value) {
        throw new Error(`BigInt ${value} does not fit into a 64-bit signed integer.`);
    }
    const buffer = new ArrayBuffer(8);
    new DataView(buffer).setBigInt64(0, value, true);
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$base64$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["fromByteArray"](new Uint8Array(buffer));
}
function modernBase64ToBigInt(encoded) {
    const integerBytes = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$base64$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toByteArray"](encoded);
    if (integerBytes.byteLength !== 8) {
        throw new Error(`Received ${integerBytes.byteLength} bytes, expected 8 for $integer`);
    }
    const intBytesView = new DataView(integerBytes.buffer);
    return intBytesView.getBigInt64(0, true);
}
const bigIntToBase64 = DataView.prototype.setBigInt64 ? modernBigIntToBase64 : slowBigIntToBase64;
const base64ToBigInt = DataView.prototype.getBigInt64 ? modernBase64ToBigInt : slowBase64ToBigInt;
const MAX_IDENTIFIER_LEN = 1024;
function validateObjectField(k) {
    if (k.length > MAX_IDENTIFIER_LEN) {
        throw new Error(`Field name ${k} exceeds maximum field name length ${MAX_IDENTIFIER_LEN}.`);
    }
    if (k.startsWith("$")) {
        throw new Error(`Field name ${k} starts with a '$', which is reserved.`);
    }
    for(let i = 0; i < k.length; i += 1){
        const charCode = k.charCodeAt(i);
        if (charCode < 32 || charCode >= 127) {
            throw new Error(`Field name ${k} has invalid character '${k[i]}': Field names can only contain non-control ASCII characters`);
        }
    }
}
function jsonToConvex(value) {
    if (value === null) {
        return value;
    }
    if (typeof value === "boolean") {
        return value;
    }
    if (typeof value === "number") {
        return value;
    }
    if (typeof value === "string") {
        return value;
    }
    if (Array.isArray(value)) {
        return value.map((value2)=>jsonToConvex(value2));
    }
    if (typeof value !== "object") {
        throw new Error(`Unexpected type of ${value}`);
    }
    const entries = Object.entries(value);
    if (entries.length === 1) {
        const key = entries[0][0];
        if (key === "$bytes") {
            if (typeof value.$bytes !== "string") {
                throw new Error(`Malformed $bytes field on ${value}`);
            }
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$base64$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toByteArray"](value.$bytes).buffer;
        }
        if (key === "$integer") {
            if (typeof value.$integer !== "string") {
                throw new Error(`Malformed $integer field on ${value}`);
            }
            return base64ToBigInt(value.$integer);
        }
        if (key === "$float") {
            if (typeof value.$float !== "string") {
                throw new Error(`Malformed $float field on ${value}`);
            }
            const floatBytes = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$base64$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toByteArray"](value.$float);
            if (floatBytes.byteLength !== 8) {
                throw new Error(`Received ${floatBytes.byteLength} bytes, expected 8 for $float`);
            }
            const floatBytesView = new DataView(floatBytes.buffer);
            const float = floatBytesView.getFloat64(0, LITTLE_ENDIAN);
            if (!isSpecial(float)) {
                throw new Error(`Float ${float} should be encoded as a number`);
            }
            return float;
        }
        if (key === "$set") {
            throw new Error(`Received a Set which is no longer supported as a Convex type.`);
        }
        if (key === "$map") {
            throw new Error(`Received a Map which is no longer supported as a Convex type.`);
        }
    }
    const out = {};
    for (const [k, v] of Object.entries(value)){
        validateObjectField(k);
        out[k] = jsonToConvex(v);
    }
    return out;
}
function stringifyValueForError(value) {
    return JSON.stringify(value, (_key, value2)=>{
        if (value2 === void 0) {
            return "undefined";
        }
        if (typeof value2 === "bigint") {
            return `${value2.toString()}n`;
        }
        return value2;
    });
}
function convexToJsonInternal(value, originalValue, context, includeTopLevelUndefined) {
    if (value === void 0) {
        const contextText = context && ` (present at path ${context} in original object ${stringifyValueForError(originalValue)})`;
        throw new Error(`undefined is not a valid Convex value${contextText}. To learn about Convex's supported types, see https://docs.convex.dev/using/types.`);
    }
    if (value === null) {
        return value;
    }
    if (typeof value === "bigint") {
        if (value < MIN_INT64 || MAX_INT64 < value) {
            throw new Error(`BigInt ${value} does not fit into a 64-bit signed integer.`);
        }
        return {
            $integer: bigIntToBase64(value)
        };
    }
    if (typeof value === "number") {
        if (isSpecial(value)) {
            const buffer = new ArrayBuffer(8);
            new DataView(buffer).setFloat64(0, value, LITTLE_ENDIAN);
            return {
                $float: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$base64$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["fromByteArray"](new Uint8Array(buffer))
            };
        } else {
            return value;
        }
    }
    if (typeof value === "boolean") {
        return value;
    }
    if (typeof value === "string") {
        return value;
    }
    if (value instanceof ArrayBuffer) {
        return {
            $bytes: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$base64$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["fromByteArray"](new Uint8Array(value))
        };
    }
    if (Array.isArray(value)) {
        return value.map((value2, i)=>convexToJsonInternal(value2, originalValue, context + `[${i}]`, false));
    }
    if (value instanceof Set) {
        throw new Error(errorMessageForUnsupportedType(context, "Set", [
            ...value
        ], originalValue));
    }
    if (value instanceof Map) {
        throw new Error(errorMessageForUnsupportedType(context, "Map", [
            ...value
        ], originalValue));
    }
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$common$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isSimpleObject"])(value)) {
        const theType = value?.constructor?.name;
        const typeName = theType ? `${theType} ` : "";
        throw new Error(errorMessageForUnsupportedType(context, typeName, value, originalValue));
    }
    const out = {};
    const entries = Object.entries(value);
    entries.sort(([k1, _v1], [k2, _v2])=>k1 === k2 ? 0 : k1 < k2 ? -1 : 1);
    for (const [k, v] of entries){
        if (v !== void 0) {
            validateObjectField(k);
            out[k] = convexToJsonInternal(v, originalValue, context + `.${k}`, false);
        } else if (includeTopLevelUndefined) {
            validateObjectField(k);
            out[k] = convexOrUndefinedToJsonInternal(v, originalValue, context + `.${k}`);
        }
    }
    return out;
}
function errorMessageForUnsupportedType(context, typeName, value, originalValue) {
    if (context) {
        return `${typeName}${stringifyValueForError(value)} is not a supported Convex type (present at path ${context} in original object ${stringifyValueForError(originalValue)}). To learn about Convex's supported types, see https://docs.convex.dev/using/types.`;
    } else {
        return `${typeName}${stringifyValueForError(value)} is not a supported Convex type.`;
    }
}
function convexOrUndefinedToJsonInternal(value, originalValue, context) {
    if (value === void 0) {
        return {
            $undefined: null
        };
    } else {
        if (originalValue === void 0) {
            throw new Error(`Programming error. Current value is ${stringifyValueForError(value)} but original value is undefined`);
        }
        return convexToJsonInternal(value, originalValue, context, false);
    }
}
function convexToJson(value) {
    return convexToJsonInternal(value, value, "", false);
}
function convexOrUndefinedToJson(value) {
    return convexOrUndefinedToJsonInternal(value, value, "");
}
function patchValueToJson(value) {
    return convexToJsonInternal(value, value, "", true);
} //# sourceMappingURL=value.js.map
}),
"[project]/node_modules/convex/dist/esm/values/validators.js [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "VAny",
    ()=>VAny,
    "VArray",
    ()=>VArray,
    "VBoolean",
    ()=>VBoolean,
    "VBytes",
    ()=>VBytes,
    "VFloat64",
    ()=>VFloat64,
    "VId",
    ()=>VId,
    "VInt64",
    ()=>VInt64,
    "VLiteral",
    ()=>VLiteral,
    "VNull",
    ()=>VNull,
    "VObject",
    ()=>VObject,
    "VRecord",
    ()=>VRecord,
    "VString",
    ()=>VString,
    "VUnion",
    ()=>VUnion
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$value$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/values/value.js [app-ssr] (ecmascript)");
"use strict";
var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value)=>key in obj ? __defProp(obj, key, {
        enumerable: true,
        configurable: true,
        writable: true,
        value
    }) : obj[key] = value;
var __publicField = (obj, key, value)=>__defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
;
class BaseValidator {
    constructor({ isOptional }){
        /**
     * Only for TypeScript, the TS type of the JS values validated
     * by this validator.
     */ __publicField(this, "type");
        /**
     * Only for TypeScript, if this an Object validator, then
     * this is the TS type of its property names.
     */ __publicField(this, "fieldPaths");
        /**
     * Whether this is an optional Object property value validator.
     */ __publicField(this, "isOptional");
        /**
     * Always `"true"`.
     */ __publicField(this, "isConvexValidator");
        this.isOptional = isOptional;
        this.isConvexValidator = true;
    }
    /** @deprecated - use isOptional instead */ get optional() {
        return this.isOptional === "optional" ? true : false;
    }
}
class VId extends BaseValidator {
    /**
   * Usually you'd use `v.id(tableName)` instead.
   */ constructor({ isOptional, tableName }){
        super({
            isOptional
        });
        /**
     * The name of the table that the validated IDs must belong to.
     */ __publicField(this, "tableName");
        /**
     * The kind of validator, `"id"`.
     */ __publicField(this, "kind", "id");
        if (typeof tableName !== "string") {
            throw new Error("v.id(tableName) requires a string");
        }
        this.tableName = tableName;
    }
    /** @internal */ get json() {
        return {
            type: "id",
            tableName: this.tableName
        };
    }
    /** @internal */ asOptional() {
        return new VId({
            isOptional: "optional",
            tableName: this.tableName
        });
    }
}
class VFloat64 extends BaseValidator {
    constructor(){
        super(...arguments);
        /**
     * The kind of validator, `"float64"`.
     */ __publicField(this, "kind", "float64");
    }
    /** @internal */ get json() {
        return {
            type: "number"
        };
    }
    /** @internal */ asOptional() {
        return new VFloat64({
            isOptional: "optional"
        });
    }
}
class VInt64 extends BaseValidator {
    constructor(){
        super(...arguments);
        /**
     * The kind of validator, `"int64"`.
     */ __publicField(this, "kind", "int64");
    }
    /** @internal */ get json() {
        return {
            type: "bigint"
        };
    }
    /** @internal */ asOptional() {
        return new VInt64({
            isOptional: "optional"
        });
    }
}
class VBoolean extends BaseValidator {
    constructor(){
        super(...arguments);
        /**
     * The kind of validator, `"boolean"`.
     */ __publicField(this, "kind", "boolean");
    }
    /** @internal */ get json() {
        return {
            type: this.kind
        };
    }
    /** @internal */ asOptional() {
        return new VBoolean({
            isOptional: "optional"
        });
    }
}
class VBytes extends BaseValidator {
    constructor(){
        super(...arguments);
        /**
     * The kind of validator, `"bytes"`.
     */ __publicField(this, "kind", "bytes");
    }
    /** @internal */ get json() {
        return {
            type: this.kind
        };
    }
    /** @internal */ asOptional() {
        return new VBytes({
            isOptional: "optional"
        });
    }
}
class VString extends BaseValidator {
    constructor(){
        super(...arguments);
        /**
     * The kind of validator, `"string"`.
     */ __publicField(this, "kind", "string");
    }
    /** @internal */ get json() {
        return {
            type: this.kind
        };
    }
    /** @internal */ asOptional() {
        return new VString({
            isOptional: "optional"
        });
    }
}
class VNull extends BaseValidator {
    constructor(){
        super(...arguments);
        /**
     * The kind of validator, `"null"`.
     */ __publicField(this, "kind", "null");
    }
    /** @internal */ get json() {
        return {
            type: this.kind
        };
    }
    /** @internal */ asOptional() {
        return new VNull({
            isOptional: "optional"
        });
    }
}
class VAny extends BaseValidator {
    constructor(){
        super(...arguments);
        /**
     * The kind of validator, `"any"`.
     */ __publicField(this, "kind", "any");
    }
    /** @internal */ get json() {
        return {
            type: this.kind
        };
    }
    /** @internal */ asOptional() {
        return new VAny({
            isOptional: "optional"
        });
    }
}
class VObject extends BaseValidator {
    /**
   * Usually you'd use `v.object({ ... })` instead.
   */ constructor({ isOptional, fields }){
        super({
            isOptional
        });
        /**
     * An object with the validator for each property.
     */ __publicField(this, "fields");
        /**
     * The kind of validator, `"object"`.
     */ __publicField(this, "kind", "object");
        globalThis.Object.values(fields).forEach((v)=>{
            if (!v.isConvexValidator) {
                throw new Error("v.object() entries must be valiators");
            }
        });
        this.fields = fields;
    }
    /** @internal */ get json() {
        return {
            type: this.kind,
            value: globalThis.Object.fromEntries(globalThis.Object.entries(this.fields).map(([k, v])=>[
                    k,
                    {
                        fieldType: v.json,
                        optional: v.isOptional === "optional" ? true : false
                    }
                ]))
        };
    }
    /** @internal */ asOptional() {
        return new VObject({
            isOptional: "optional",
            fields: this.fields
        });
    }
}
class VLiteral extends BaseValidator {
    /**
   * Usually you'd use `v.literal(value)` instead.
   */ constructor({ isOptional, value }){
        super({
            isOptional
        });
        /**
     * The value that the validated values must be equal to.
     */ __publicField(this, "value");
        /**
     * The kind of validator, `"literal"`.
     */ __publicField(this, "kind", "literal");
        if (typeof value !== "string" && typeof value !== "boolean" && typeof value !== "number" && typeof value !== "bigint") {
            throw new Error("v.literal(value) must be a string, number, or boolean");
        }
        this.value = value;
    }
    /** @internal */ get json() {
        return {
            type: this.kind,
            value: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$value$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["convexToJson"])(this.value)
        };
    }
    /** @internal */ asOptional() {
        return new VLiteral({
            isOptional: "optional",
            value: this.value
        });
    }
}
class VArray extends BaseValidator {
    /**
   * Usually you'd use `v.array(element)` instead.
   */ constructor({ isOptional, element }){
        super({
            isOptional
        });
        /**
     * The validator for the elements of the array.
     */ __publicField(this, "element");
        /**
     * The kind of validator, `"array"`.
     */ __publicField(this, "kind", "array");
        this.element = element;
    }
    /** @internal */ get json() {
        return {
            type: this.kind,
            value: this.element.json
        };
    }
    /** @internal */ asOptional() {
        return new VArray({
            isOptional: "optional",
            element: this.element
        });
    }
}
class VRecord extends BaseValidator {
    /**
   * Usually you'd use `v.record(key, value)` instead.
   */ constructor({ isOptional, key, value }){
        super({
            isOptional
        });
        /**
     * The validator for the keys of the record.
     */ __publicField(this, "key");
        /**
     * The validator for the values of the record.
     */ __publicField(this, "value");
        /**
     * The kind of validator, `"record"`.
     */ __publicField(this, "kind", "record");
        if (key.isOptional === "optional") {
            throw new Error("Record validator cannot have optional keys");
        }
        if (value.isOptional === "optional") {
            throw new Error("Record validator cannot have optional values");
        }
        if (!key.isConvexValidator || !value.isConvexValidator) {
            throw new Error("Key and value of v.record() but be validators");
        }
        this.key = key;
        this.value = value;
    }
    /** @internal */ get json() {
        return {
            type: this.kind,
            // This cast is needed because TypeScript thinks the key type is too wide
            keys: this.key.json,
            values: {
                fieldType: this.value.json,
                optional: false
            }
        };
    }
    /** @internal */ asOptional() {
        return new VRecord({
            isOptional: "optional",
            key: this.key,
            value: this.value
        });
    }
}
class VUnion extends BaseValidator {
    /**
   * Usually you'd use `v.union(...members)` instead.
   */ constructor({ isOptional, members }){
        super({
            isOptional
        });
        /**
     * The array of validators, one of which must match the value.
     */ __publicField(this, "members");
        /**
     * The kind of validator, `"union"`.
     */ __publicField(this, "kind", "union");
        members.forEach((member)=>{
            if (!member.isConvexValidator) {
                throw new Error("All members of v.union() must be validators");
            }
        });
        this.members = members;
    }
    /** @internal */ get json() {
        return {
            type: this.kind,
            value: this.members.map((v)=>v.json)
        };
    }
    /** @internal */ asOptional() {
        return new VUnion({
            isOptional: "optional",
            members: this.members
        });
    }
} //# sourceMappingURL=validators.js.map
}),
"[project]/node_modules/convex/dist/esm/values/validator.js [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "asObjectValidator",
    ()=>asObjectValidator,
    "isValidator",
    ()=>isValidator,
    "v",
    ()=>v
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$validators$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/values/validators.js [app-ssr] (ecmascript)");
"use strict";
;
function isValidator(v2) {
    return !!v2.isConvexValidator;
}
function asObjectValidator(obj) {
    if (isValidator(obj)) {
        return obj;
    } else {
        return v.object(obj);
    }
}
const v = {
    /**
   * Validates that the value corresponds to an ID of a document in given table.
   * @param tableName The name of the table.
   */ id: (tableName)=>{
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$validators$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["VId"]({
            isOptional: "required",
            tableName
        });
    },
    /**
   * Validates that the value is of type Null.
   */ null: ()=>{
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$validators$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["VNull"]({
            isOptional: "required"
        });
    },
    /**
   * Validates that the value is of Convex type Float64 (Number in JS).
   *
   * Alias for `v.float64()`
   */ number: ()=>{
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$validators$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["VFloat64"]({
            isOptional: "required"
        });
    },
    /**
   * Validates that the value is of Convex type Float64 (Number in JS).
   */ float64: ()=>{
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$validators$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["VFloat64"]({
            isOptional: "required"
        });
    },
    /**
   * @deprecated Use `v.int64()` instead
   */ bigint: ()=>{
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$validators$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["VInt64"]({
            isOptional: "required"
        });
    },
    /**
   * Validates that the value is of Convex type Int64 (BigInt in JS).
   */ int64: ()=>{
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$validators$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["VInt64"]({
            isOptional: "required"
        });
    },
    /**
   * Validates that the value is of type Boolean.
   */ boolean: ()=>{
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$validators$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["VBoolean"]({
            isOptional: "required"
        });
    },
    /**
   * Validates that the value is of type String.
   */ string: ()=>{
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$validators$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["VString"]({
            isOptional: "required"
        });
    },
    /**
   * Validates that the value is of Convex type Bytes (constructed in JS via `ArrayBuffer`).
   */ bytes: ()=>{
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$validators$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["VBytes"]({
            isOptional: "required"
        });
    },
    /**
   * Validates that the value is equal to the given literal value.
   * @param literal The literal value to compare against.
   */ literal: (literal)=>{
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$validators$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["VLiteral"]({
            isOptional: "required",
            value: literal
        });
    },
    /**
   * Validates that the value is an Array of the given element type.
   * @param element The validator for the elements of the array.
   */ array: (element)=>{
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$validators$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["VArray"]({
            isOptional: "required",
            element
        });
    },
    /**
   * Validates that the value is an Object with the given properties.
   * @param fields An object specifying the validator for each property.
   */ object: (fields)=>{
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$validators$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["VObject"]({
            isOptional: "required",
            fields
        });
    },
    /**
   * Validates that the value is a Record with keys and values that match the given types.
   * @param keys The validator for the keys of the record. This cannot contain string literals.
   * @param values The validator for the values of the record.
   */ record: (keys, values)=>{
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$validators$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["VRecord"]({
            isOptional: "required",
            key: keys,
            value: values
        });
    },
    /**
   * Validates that the value matches one of the given validators.
   * @param members The validators to match against.
   */ union: (...members)=>{
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$validators$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["VUnion"]({
            isOptional: "required",
            members
        });
    },
    /**
   * Does not validate the value.
   */ any: ()=>{
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$validators$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["VAny"]({
            isOptional: "required"
        });
    },
    /**
   * Allows not specifying a value for a property in an Object.
   * @param value The property value validator to make optional.
   *
   * ```typescript
   * const objectWithOptionalFields = v.object({
   *   requiredField: v.string(),
   *   optionalField: v.optional(v.string()),
   * });
   * ```
   */ optional: (value)=>{
        return value.asOptional();
    }
}; //# sourceMappingURL=validator.js.map
}),
"[project]/node_modules/convex/dist/esm/values/errors.js [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "ConvexError",
    ()=>ConvexError
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$value$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/values/value.js [app-ssr] (ecmascript)");
"use strict";
var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value)=>key in obj ? __defProp(obj, key, {
        enumerable: true,
        configurable: true,
        writable: true,
        value
    }) : obj[key] = value;
var __publicField = (obj, key, value)=>__defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
var _a, _b;
;
const IDENTIFYING_FIELD = Symbol.for("ConvexError");
class ConvexError extends (_b = Error, _a = IDENTIFYING_FIELD, _b) {
    constructor(data){
        super(typeof data === "string" ? data : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$value$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["stringifyValueForError"])(data));
        __publicField(this, "name", "ConvexError");
        __publicField(this, "data");
        __publicField(this, _a, true);
        this.data = data;
    }
} //# sourceMappingURL=errors.js.map
}),
"[project]/node_modules/convex/dist/esm/values/compare_utf8.js [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "compareUTF8",
    ()=>compareUTF8,
    "greaterThan",
    ()=>greaterThan,
    "greaterThanEq",
    ()=>greaterThanEq,
    "lessThan",
    ()=>lessThan,
    "lessThanEq",
    ()=>lessThanEq,
    "utf16LengthForCodePoint",
    ()=>utf16LengthForCodePoint
]);
"use strict";
function compareUTF8(a, b) {
    const aLength = a.length;
    const bLength = b.length;
    const length = Math.min(aLength, bLength);
    for(let i = 0; i < length;){
        const aCodePoint = a.codePointAt(i);
        const bCodePoint = b.codePointAt(i);
        if (aCodePoint !== bCodePoint) {
            if (aCodePoint < 128 && bCodePoint < 128) {
                return aCodePoint - bCodePoint;
            }
            const aLength2 = utf8Bytes(aCodePoint, aBytes);
            const bLength2 = utf8Bytes(bCodePoint, bBytes);
            return compareArrays(aBytes, aLength2, bBytes, bLength2);
        }
        i += utf16LengthForCodePoint(aCodePoint);
    }
    return aLength - bLength;
}
function compareArrays(a, aLength, b, bLength) {
    const length = Math.min(aLength, bLength);
    for(let i = 0; i < length; i++){
        const aValue = a[i];
        const bValue = b[i];
        if (aValue !== bValue) {
            return aValue - bValue;
        }
    }
    return aLength - bLength;
}
function utf16LengthForCodePoint(aCodePoint) {
    return aCodePoint > 65535 ? 2 : 1;
}
const arr = ()=>Array.from({
        length: 4
    }, ()=>0);
const aBytes = arr();
const bBytes = arr();
function utf8Bytes(codePoint, bytes) {
    if (codePoint < 128) {
        bytes[0] = codePoint;
        return 1;
    }
    let count;
    let offset;
    if (codePoint <= 2047) {
        count = 1;
        offset = 192;
    } else if (codePoint <= 65535) {
        count = 2;
        offset = 224;
    } else if (codePoint <= 1114111) {
        count = 3;
        offset = 240;
    } else {
        throw new Error("Invalid code point");
    }
    bytes[0] = (codePoint >> 6 * count) + offset;
    let i = 1;
    for(; count > 0; count--){
        const temp = codePoint >> 6 * (count - 1);
        bytes[i++] = 128 | temp & 63;
    }
    return i;
}
function greaterThan(a, b) {
    return compareUTF8(a, b) > 0;
}
function greaterThanEq(a, b) {
    return compareUTF8(a, b) >= 0;
}
function lessThan(a, b) {
    return compareUTF8(a, b) < 0;
}
function lessThanEq(a, b) {
    return compareUTF8(a, b) <= 0;
} //# sourceMappingURL=compare_utf8.js.map
}),
"[project]/node_modules/convex/dist/esm/values/compare.js [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "compareValues",
    ()=>compareValues
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$compare_utf8$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/values/compare_utf8.js [app-ssr] (ecmascript)");
"use strict";
;
function compareValues(k1, k2) {
    return compareAsTuples(makeComparable(k1), makeComparable(k2));
}
function compareAsTuples(a, b) {
    if (a[0] === b[0]) {
        return compareSameTypeValues(a[1], b[1]);
    } else if (a[0] < b[0]) {
        return -1;
    }
    return 1;
}
function compareSameTypeValues(v1, v2) {
    if (v1 === void 0 || v1 === null) {
        return 0;
    }
    if (typeof v1 === "number") {
        if (typeof v2 !== "number") {
            throw new Error(`Unexpected type ${v2}`);
        }
        return compareNumbers(v1, v2);
    }
    if (typeof v1 === "string") {
        if (typeof v2 !== "string") {
            throw new Error(`Unexpected type ${v2}`);
        }
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$compare_utf8$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["compareUTF8"])(v1, v2);
    }
    if (typeof v1 === "bigint" || typeof v1 === "boolean" || typeof v1 === "string") {
        return v1 < v2 ? -1 : v1 === v2 ? 0 : 1;
    }
    if (!Array.isArray(v1) || !Array.isArray(v2)) {
        throw new Error(`Unexpected type ${v1}`);
    }
    for(let i = 0; i < v1.length && i < v2.length; i++){
        const cmp = compareAsTuples(v1[i], v2[i]);
        if (cmp !== 0) {
            return cmp;
        }
    }
    if (v1.length < v2.length) {
        return -1;
    }
    if (v1.length > v2.length) {
        return 1;
    }
    return 0;
}
function compareNumbers(v1, v2) {
    if (isNaN(v1) || isNaN(v2)) {
        const buffer1 = new ArrayBuffer(8);
        const buffer2 = new ArrayBuffer(8);
        new DataView(buffer1).setFloat64(0, v1, /* little-endian */ true);
        new DataView(buffer2).setFloat64(0, v2, /* little-endian */ true);
        const v1Bits = BigInt(new DataView(buffer1).getBigInt64(0, /* little-endian */ true));
        const v2Bits = BigInt(new DataView(buffer2).getBigInt64(0, /* little-endian */ true));
        const v1Sign = (v1Bits & 0x8000000000000000n) !== 0n;
        const v2Sign = (v2Bits & 0x8000000000000000n) !== 0n;
        if (isNaN(v1) !== isNaN(v2)) {
            if (isNaN(v1)) {
                return v1Sign ? -1 : 1;
            }
            return v2Sign ? 1 : -1;
        }
        if (v1Sign !== v2Sign) {
            return v1Sign ? -1 : 1;
        }
        return v1Bits < v2Bits ? -1 : v1Bits === v2Bits ? 0 : 1;
    }
    if (Object.is(v1, v2)) {
        return 0;
    }
    if (Object.is(v1, -0)) {
        return Object.is(v2, 0) ? -1 : -Math.sign(v2);
    }
    if (Object.is(v2, -0)) {
        return Object.is(v1, 0) ? 1 : Math.sign(v1);
    }
    return v1 < v2 ? -1 : 1;
}
function makeComparable(v) {
    if (v === void 0) {
        return [
            0,
            void 0
        ];
    }
    if (v === null) {
        return [
            1,
            null
        ];
    }
    if (typeof v === "bigint") {
        return [
            2,
            v
        ];
    }
    if (typeof v === "number") {
        return [
            3,
            v
        ];
    }
    if (typeof v === "boolean") {
        return [
            4,
            v
        ];
    }
    if (typeof v === "string") {
        return [
            5,
            v
        ];
    }
    if (v instanceof ArrayBuffer) {
        return [
            6,
            Array.from(new Uint8Array(v)).map(makeComparable)
        ];
    }
    if (Array.isArray(v)) {
        return [
            7,
            v.map(makeComparable)
        ];
    }
    const keys = Object.keys(v).sort();
    const pojo = keys.map((k)=>[
            k,
            v[k]
        ]);
    return [
        8,
        pojo.map(makeComparable)
    ];
} //# sourceMappingURL=compare.js.map
}),
"[project]/node_modules/convex/dist/esm/values/index.js [app-ssr] (ecmascript) <locals>", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$value$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/values/value.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$validator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/values/validator.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$base64$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/values/base64.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$errors$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/values/errors.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$compare$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/values/compare.js [app-ssr] (ecmascript)"); //# sourceMappingURL=index.js.map
"use strict";
;
;
;
;
;
;
}),
"[project]/node_modules/convex/dist/esm/browser/logging.js [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "DefaultLogger",
    ()=>DefaultLogger,
    "createHybridErrorStacktrace",
    ()=>createHybridErrorStacktrace,
    "forwardData",
    ()=>forwardData,
    "instantiateDefaultLogger",
    ()=>instantiateDefaultLogger,
    "instantiateNoopLogger",
    ()=>instantiateNoopLogger,
    "logFatalError",
    ()=>logFatalError,
    "logForFunction",
    ()=>logForFunction
]);
"use strict";
var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value)=>key in obj ? __defProp(obj, key, {
        enumerable: true,
        configurable: true,
        writable: true,
        value
    }) : obj[key] = value;
var __publicField = (obj, key, value)=>__defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
const INFO_COLOR = "color:rgb(0, 145, 255)";
function prefix_for_source(source) {
    switch(source){
        case "query":
            return "Q";
        case "mutation":
            return "M";
        case "action":
            return "A";
        case "any":
            return "?";
    }
}
class DefaultLogger {
    constructor(options){
        __publicField(this, "_onLogLineFuncs");
        __publicField(this, "_verbose");
        this._onLogLineFuncs = {};
        this._verbose = options.verbose;
    }
    addLogLineListener(func) {
        let id = Math.random().toString(36).substring(2, 15);
        for(let i = 0; i < 10; i++){
            if (this._onLogLineFuncs[id] === void 0) {
                break;
            }
            id = Math.random().toString(36).substring(2, 15);
        }
        this._onLogLineFuncs[id] = func;
        return ()=>{
            delete this._onLogLineFuncs[id];
        };
    }
    logVerbose(...args) {
        if (this._verbose) {
            for (const func of Object.values(this._onLogLineFuncs)){
                func("debug", `${/* @__PURE__ */ new Date().toISOString()}`, ...args);
            }
        }
    }
    log(...args) {
        for (const func of Object.values(this._onLogLineFuncs)){
            func("info", ...args);
        }
    }
    warn(...args) {
        for (const func of Object.values(this._onLogLineFuncs)){
            func("warn", ...args);
        }
    }
    error(...args) {
        for (const func of Object.values(this._onLogLineFuncs)){
            func("error", ...args);
        }
    }
}
function instantiateDefaultLogger(options) {
    const logger = new DefaultLogger(options);
    logger.addLogLineListener((level, ...args)=>{
        switch(level){
            case "debug":
                console.debug(...args);
                break;
            case "info":
                console.log(...args);
                break;
            case "warn":
                console.warn(...args);
                break;
            case "error":
                console.error(...args);
                break;
            default:
                {
                    level;
                    console.log(...args);
                }
        }
    });
    return logger;
}
function instantiateNoopLogger(options) {
    return new DefaultLogger(options);
}
function logForFunction(logger, type, source, udfPath, message) {
    const prefix = prefix_for_source(source);
    if (typeof message === "object") {
        message = `ConvexError ${JSON.stringify(message.errorData, null, 2)}`;
    }
    if (type === "info") {
        const match = message.match(/^\[.*?\] /);
        if (match === null) {
            logger.error(`[CONVEX ${prefix}(${udfPath})] Could not parse console.log`);
            return;
        }
        const level = message.slice(1, match[0].length - 2);
        const args = message.slice(match[0].length);
        logger.log(`%c[CONVEX ${prefix}(${udfPath})] [${level}]`, INFO_COLOR, args);
    } else {
        logger.error(`[CONVEX ${prefix}(${udfPath})] ${message}`);
    }
}
function logFatalError(logger, message) {
    const errorMessage = `[CONVEX FATAL ERROR] ${message}`;
    logger.error(errorMessage);
    return new Error(errorMessage);
}
function createHybridErrorStacktrace(source, udfPath, result) {
    const prefix = prefix_for_source(source);
    return `[CONVEX ${prefix}(${udfPath})] ${result.errorMessage}
  Called by client`;
}
function forwardData(result, error) {
    error.data = result.errorData;
    return error;
} //# sourceMappingURL=logging.js.map
}),
"[project]/node_modules/convex/dist/esm/browser/sync/udf_path_utils.js [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "canonicalizeUdfPath",
    ()=>canonicalizeUdfPath,
    "serializePathAndArgs",
    ()=>serializePathAndArgs
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/values/index.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$value$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/values/value.js [app-ssr] (ecmascript)");
"use strict";
;
function canonicalizeUdfPath(udfPath) {
    const pieces = udfPath.split(":");
    let moduleName;
    let functionName;
    if (pieces.length === 1) {
        moduleName = pieces[0];
        functionName = "default";
    } else {
        moduleName = pieces.slice(0, pieces.length - 1).join(":");
        functionName = pieces[pieces.length - 1];
    }
    if (moduleName.endsWith(".js")) {
        moduleName = moduleName.slice(0, -3);
    }
    return `${moduleName}:${functionName}`;
}
function serializePathAndArgs(udfPath, args) {
    return JSON.stringify({
        udfPath: canonicalizeUdfPath(udfPath),
        args: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$value$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["convexToJson"])(args)
    });
} //# sourceMappingURL=udf_path_utils.js.map
}),
"[project]/node_modules/convex/dist/esm/browser/sync/local_state.js [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "LocalSyncState",
    ()=>LocalSyncState
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/values/index.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$value$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/values/value.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$udf_path_utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/browser/sync/udf_path_utils.js [app-ssr] (ecmascript)");
"use strict";
var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value)=>key in obj ? __defProp(obj, key, {
        enumerable: true,
        configurable: true,
        writable: true,
        value
    }) : obj[key] = value;
var __publicField = (obj, key, value)=>__defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
;
;
class LocalSyncState {
    constructor(){
        __publicField(this, "nextQueryId");
        __publicField(this, "querySetVersion");
        __publicField(this, "querySet");
        __publicField(this, "queryIdToToken");
        __publicField(this, "identityVersion");
        __publicField(this, "auth");
        __publicField(this, "outstandingQueriesOlderThanRestart");
        __publicField(this, "outstandingAuthOlderThanRestart");
        __publicField(this, "paused");
        __publicField(this, "pendingQuerySetModifications");
        this.nextQueryId = 0;
        this.querySetVersion = 0;
        this.identityVersion = 0;
        this.querySet = /* @__PURE__ */ new Map();
        this.queryIdToToken = /* @__PURE__ */ new Map();
        this.outstandingQueriesOlderThanRestart = /* @__PURE__ */ new Set();
        this.outstandingAuthOlderThanRestart = false;
        this.paused = false;
        this.pendingQuerySetModifications = /* @__PURE__ */ new Map();
    }
    hasSyncedPastLastReconnect() {
        return this.outstandingQueriesOlderThanRestart.size === 0 && !this.outstandingAuthOlderThanRestart;
    }
    markAuthCompletion() {
        this.outstandingAuthOlderThanRestart = false;
    }
    subscribe(udfPath, args, journal, componentPath) {
        const canonicalizedUdfPath = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$udf_path_utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["canonicalizeUdfPath"])(udfPath);
        const queryToken = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$udf_path_utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["serializePathAndArgs"])(canonicalizedUdfPath, args);
        const existingEntry = this.querySet.get(queryToken);
        if (existingEntry !== void 0) {
            existingEntry.numSubscribers += 1;
            return {
                queryToken,
                modification: null,
                unsubscribe: ()=>this.removeSubscriber(queryToken)
            };
        } else {
            const queryId = this.nextQueryId++;
            const query = {
                id: queryId,
                canonicalizedUdfPath,
                args,
                numSubscribers: 1,
                journal,
                componentPath
            };
            this.querySet.set(queryToken, query);
            this.queryIdToToken.set(queryId, queryToken);
            const baseVersion = this.querySetVersion;
            const newVersion = this.querySetVersion + 1;
            const add = {
                type: "Add",
                queryId,
                udfPath: canonicalizedUdfPath,
                args: [
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$value$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["convexToJson"])(args)
                ],
                journal,
                componentPath
            };
            if (this.paused) {
                this.pendingQuerySetModifications.set(queryId, add);
            } else {
                this.querySetVersion = newVersion;
            }
            const modification = {
                type: "ModifyQuerySet",
                baseVersion,
                newVersion,
                modifications: [
                    add
                ]
            };
            return {
                queryToken,
                modification,
                unsubscribe: ()=>this.removeSubscriber(queryToken)
            };
        }
    }
    transition(transition) {
        for (const modification of transition.modifications){
            switch(modification.type){
                case "QueryUpdated":
                case "QueryFailed":
                    {
                        this.outstandingQueriesOlderThanRestart.delete(modification.queryId);
                        const journal = modification.journal;
                        if (journal !== void 0) {
                            const queryToken = this.queryIdToToken.get(modification.queryId);
                            if (queryToken !== void 0) {
                                this.querySet.get(queryToken).journal = journal;
                            }
                        }
                        break;
                    }
                case "QueryRemoved":
                    {
                        this.outstandingQueriesOlderThanRestart.delete(modification.queryId);
                        break;
                    }
                default:
                    {
                        modification;
                        throw new Error(`Invalid modification ${modification.type}`);
                    }
            }
        }
    }
    queryId(udfPath, args) {
        const canonicalizedUdfPath = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$udf_path_utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["canonicalizeUdfPath"])(udfPath);
        const queryToken = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$udf_path_utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["serializePathAndArgs"])(canonicalizedUdfPath, args);
        const existingEntry = this.querySet.get(queryToken);
        if (existingEntry !== void 0) {
            return existingEntry.id;
        }
        return null;
    }
    isCurrentOrNewerAuthVersion(version) {
        return version >= this.identityVersion;
    }
    setAuth(value) {
        this.auth = {
            tokenType: "User",
            value
        };
        const baseVersion = this.identityVersion;
        if (!this.paused) {
            this.identityVersion = baseVersion + 1;
        }
        return {
            type: "Authenticate",
            baseVersion,
            ...this.auth
        };
    }
    setAdminAuth(value, actingAs) {
        const auth = {
            tokenType: "Admin",
            value,
            impersonating: actingAs
        };
        this.auth = auth;
        const baseVersion = this.identityVersion;
        if (!this.paused) {
            this.identityVersion = baseVersion + 1;
        }
        return {
            type: "Authenticate",
            baseVersion,
            ...auth
        };
    }
    clearAuth() {
        this.auth = void 0;
        this.markAuthCompletion();
        const baseVersion = this.identityVersion;
        if (!this.paused) {
            this.identityVersion = baseVersion + 1;
        }
        return {
            type: "Authenticate",
            tokenType: "None",
            baseVersion
        };
    }
    hasAuth() {
        return !!this.auth;
    }
    isNewAuth(value) {
        return this.auth?.value !== value;
    }
    queryPath(queryId) {
        const pathAndArgs = this.queryIdToToken.get(queryId);
        if (pathAndArgs) {
            return this.querySet.get(pathAndArgs).canonicalizedUdfPath;
        }
        return null;
    }
    queryArgs(queryId) {
        const pathAndArgs = this.queryIdToToken.get(queryId);
        if (pathAndArgs) {
            return this.querySet.get(pathAndArgs).args;
        }
        return null;
    }
    queryToken(queryId) {
        return this.queryIdToToken.get(queryId) ?? null;
    }
    queryJournal(queryToken) {
        return this.querySet.get(queryToken)?.journal;
    }
    restart(oldRemoteQueryResults) {
        this.unpause();
        this.outstandingQueriesOlderThanRestart.clear();
        const modifications = [];
        for (const localQuery of this.querySet.values()){
            const add = {
                type: "Add",
                queryId: localQuery.id,
                udfPath: localQuery.canonicalizedUdfPath,
                args: [
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$value$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["convexToJson"])(localQuery.args)
                ],
                journal: localQuery.journal,
                componentPath: localQuery.componentPath
            };
            modifications.push(add);
            if (!oldRemoteQueryResults.has(localQuery.id)) {
                this.outstandingQueriesOlderThanRestart.add(localQuery.id);
            }
        }
        this.querySetVersion = 1;
        const querySet = {
            type: "ModifyQuerySet",
            baseVersion: 0,
            newVersion: 1,
            modifications
        };
        if (!this.auth) {
            this.identityVersion = 0;
            return [
                querySet,
                void 0
            ];
        }
        this.outstandingAuthOlderThanRestart = true;
        const authenticate = {
            type: "Authenticate",
            baseVersion: 0,
            ...this.auth
        };
        this.identityVersion = 1;
        return [
            querySet,
            authenticate
        ];
    }
    pause() {
        this.paused = true;
    }
    resume() {
        const querySet = this.pendingQuerySetModifications.size > 0 ? {
            type: "ModifyQuerySet",
            baseVersion: this.querySetVersion,
            newVersion: ++this.querySetVersion,
            modifications: Array.from(this.pendingQuerySetModifications.values())
        } : void 0;
        const authenticate = this.auth !== void 0 ? {
            type: "Authenticate",
            baseVersion: this.identityVersion++,
            ...this.auth
        } : void 0;
        this.unpause();
        return [
            querySet,
            authenticate
        ];
    }
    unpause() {
        this.paused = false;
        this.pendingQuerySetModifications.clear();
    }
    removeSubscriber(queryToken) {
        const localQuery = this.querySet.get(queryToken);
        if (localQuery.numSubscribers > 1) {
            localQuery.numSubscribers -= 1;
            return null;
        } else {
            this.querySet.delete(queryToken);
            this.queryIdToToken.delete(localQuery.id);
            this.outstandingQueriesOlderThanRestart.delete(localQuery.id);
            const baseVersion = this.querySetVersion;
            const newVersion = this.querySetVersion + 1;
            const remove = {
                type: "Remove",
                queryId: localQuery.id
            };
            if (this.paused) {
                if (this.pendingQuerySetModifications.has(localQuery.id)) {
                    this.pendingQuerySetModifications.delete(localQuery.id);
                } else {
                    this.pendingQuerySetModifications.set(localQuery.id, remove);
                }
            } else {
                this.querySetVersion = newVersion;
            }
            return {
                type: "ModifyQuerySet",
                baseVersion,
                newVersion,
                modifications: [
                    remove
                ]
            };
        }
    }
} //# sourceMappingURL=local_state.js.map
}),
"[project]/node_modules/convex/dist/esm/browser/sync/request_manager.js [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "RequestManager",
    ()=>RequestManager
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/values/index.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$value$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/values/value.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$logging$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/browser/logging.js [app-ssr] (ecmascript)");
"use strict";
var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value)=>key in obj ? __defProp(obj, key, {
        enumerable: true,
        configurable: true,
        writable: true,
        value
    }) : obj[key] = value;
var __publicField = (obj, key, value)=>__defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
;
;
class RequestManager {
    constructor(logger, markConnectionStateDirty){
        this.logger = logger;
        this.markConnectionStateDirty = markConnectionStateDirty;
        __publicField(this, "inflightRequests");
        __publicField(this, "requestsOlderThanRestart");
        __publicField(this, "inflightMutationsCount", 0);
        __publicField(this, "inflightActionsCount", 0);
        this.inflightRequests = /* @__PURE__ */ new Map();
        this.requestsOlderThanRestart = /* @__PURE__ */ new Set();
    }
    request(message, sent) {
        const result = new Promise((resolve)=>{
            const status = sent ? "Requested" : "NotSent";
            this.inflightRequests.set(message.requestId, {
                message,
                status: {
                    status,
                    requestedAt: /* @__PURE__ */ new Date(),
                    onResult: resolve
                }
            });
            if (message.type === "Mutation") {
                this.inflightMutationsCount++;
            } else if (message.type === "Action") {
                this.inflightActionsCount++;
            }
        });
        this.markConnectionStateDirty();
        return result;
    }
    /**
   * Update the state after receiving a response.
   *
   * @returns A RequestId if the request is complete and its optimistic update
   * can be dropped, null otherwise.
   */ onResponse(response) {
        const requestInfo = this.inflightRequests.get(response.requestId);
        if (requestInfo === void 0) {
            return null;
        }
        if (requestInfo.status.status === "Completed") {
            return null;
        }
        const udfType = requestInfo.message.type === "Mutation" ? "mutation" : "action";
        const udfPath = requestInfo.message.udfPath;
        for (const line of response.logLines){
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$logging$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["logForFunction"])(this.logger, "info", udfType, udfPath, line);
        }
        const status = requestInfo.status;
        let result;
        let onResolve;
        if (response.success) {
            result = {
                success: true,
                logLines: response.logLines,
                value: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$value$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsonToConvex"])(response.result)
            };
            onResolve = ()=>status.onResult(result);
        } else {
            const errorMessage = response.result;
            const { errorData } = response;
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$logging$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["logForFunction"])(this.logger, "error", udfType, udfPath, errorMessage);
            result = {
                success: false,
                errorMessage,
                errorData: errorData !== void 0 ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$value$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsonToConvex"])(errorData) : void 0,
                logLines: response.logLines
            };
            onResolve = ()=>status.onResult(result);
        }
        if (response.type === "ActionResponse" || !response.success) {
            onResolve();
            this.inflightRequests.delete(response.requestId);
            this.requestsOlderThanRestart.delete(response.requestId);
            if (requestInfo.message.type === "Action") {
                this.inflightActionsCount--;
            } else if (requestInfo.message.type === "Mutation") {
                this.inflightMutationsCount--;
            }
            this.markConnectionStateDirty();
            return {
                requestId: response.requestId,
                result
            };
        }
        requestInfo.status = {
            status: "Completed",
            result,
            ts: response.ts,
            onResolve
        };
        return null;
    }
    // Remove and returns completed requests.
    removeCompleted(ts) {
        const completeRequests = /* @__PURE__ */ new Map();
        for (const [requestId, requestInfo] of this.inflightRequests.entries()){
            const status = requestInfo.status;
            if (status.status === "Completed" && status.ts.lessThanOrEqual(ts)) {
                status.onResolve();
                completeRequests.set(requestId, status.result);
                if (requestInfo.message.type === "Mutation") {
                    this.inflightMutationsCount--;
                } else if (requestInfo.message.type === "Action") {
                    this.inflightActionsCount--;
                }
                this.inflightRequests.delete(requestId);
                this.requestsOlderThanRestart.delete(requestId);
            }
        }
        if (completeRequests.size > 0) {
            this.markConnectionStateDirty();
        }
        return completeRequests;
    }
    restart() {
        this.requestsOlderThanRestart = new Set(this.inflightRequests.keys());
        const allMessages = [];
        for (const [requestId, value] of this.inflightRequests){
            if (value.status.status === "NotSent") {
                value.status.status = "Requested";
                allMessages.push(value.message);
                continue;
            }
            if (value.message.type === "Mutation") {
                allMessages.push(value.message);
            } else if (value.message.type === "Action") {
                this.inflightRequests.delete(requestId);
                this.requestsOlderThanRestart.delete(requestId);
                this.inflightActionsCount--;
                if (value.status.status === "Completed") {
                    throw new Error("Action should never be in 'Completed' state");
                }
                value.status.onResult({
                    success: false,
                    errorMessage: "Connection lost while action was in flight",
                    logLines: []
                });
            }
        }
        this.markConnectionStateDirty();
        return allMessages;
    }
    resume() {
        const allMessages = [];
        for (const [, value] of this.inflightRequests){
            if (value.status.status === "NotSent") {
                value.status.status = "Requested";
                allMessages.push(value.message);
                continue;
            }
        }
        return allMessages;
    }
    /**
   * @returns true if there are any requests that have been requested but have
   * not be completed yet.
   */ hasIncompleteRequests() {
        for (const requestInfo of this.inflightRequests.values()){
            if (requestInfo.status.status === "Requested") {
                return true;
            }
        }
        return false;
    }
    /**
   * @returns true if there are any inflight requests, including ones that have
   * completed on the server, but have not been applied.
   */ hasInflightRequests() {
        return this.inflightRequests.size > 0;
    }
    /**
   * @returns true if there are any inflight requests, that have been hanging around
   * since prior to the most recent restart.
   */ hasSyncedPastLastReconnect() {
        return this.requestsOlderThanRestart.size === 0;
    }
    timeOfOldestInflightRequest() {
        if (this.inflightRequests.size === 0) {
            return null;
        }
        let oldestInflightRequest = Date.now();
        for (const request of this.inflightRequests.values()){
            if (request.status.status !== "Completed") {
                if (request.status.requestedAt.getTime() < oldestInflightRequest) {
                    oldestInflightRequest = request.status.requestedAt.getTime();
                }
            }
        }
        return new Date(oldestInflightRequest);
    }
    /**
   * @returns The number of mutations currently in flight.
   */ inflightMutations() {
        return this.inflightMutationsCount;
    }
    /**
   * @returns The number of actions currently in flight.
   */ inflightActions() {
        return this.inflightActionsCount;
    }
} //# sourceMappingURL=request_manager.js.map
}),
"[project]/node_modules/convex/dist/esm/server/functionName.js [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "functionName",
    ()=>functionName
]);
"use strict";
const functionName = Symbol.for("functionName"); //# sourceMappingURL=functionName.js.map
}),
"[project]/node_modules/convex/dist/esm/server/components/paths.js [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "extractReferencePath",
    ()=>extractReferencePath,
    "getFunctionAddress",
    ()=>getFunctionAddress,
    "isFunctionHandle",
    ()=>isFunctionHandle,
    "setReferencePath",
    ()=>setReferencePath,
    "toReferencePath",
    ()=>toReferencePath
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$server$2f$functionName$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/server/functionName.js [app-ssr] (ecmascript)");
"use strict";
;
const toReferencePath = Symbol.for("toReferencePath");
function setReferencePath(obj, value) {
    obj[toReferencePath] = value;
}
function extractReferencePath(reference) {
    return reference[toReferencePath] ?? null;
}
function isFunctionHandle(s) {
    return s.startsWith("function://");
}
function getFunctionAddress(functionReference) {
    let functionAddress;
    if (typeof functionReference === "string") {
        if (isFunctionHandle(functionReference)) {
            functionAddress = {
                functionHandle: functionReference
            };
        } else {
            functionAddress = {
                name: functionReference
            };
        }
    } else if (functionReference[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$server$2f$functionName$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["functionName"]]) {
        functionAddress = {
            name: functionReference[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$server$2f$functionName$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["functionName"]]
        };
    } else {
        const referencePath = extractReferencePath(functionReference);
        if (!referencePath) {
            throw new Error(`${functionReference} is not a functionReference`);
        }
        functionAddress = {
            reference: referencePath
        };
    }
    return functionAddress;
} //# sourceMappingURL=paths.js.map
}),
"[project]/node_modules/convex/dist/esm/server/api.js [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "anyApi",
    ()=>anyApi,
    "filterApi",
    ()=>filterApi,
    "getFunctionName",
    ()=>getFunctionName,
    "justActions",
    ()=>justActions,
    "justInternal",
    ()=>justInternal,
    "justMutations",
    ()=>justMutations,
    "justPaginatedQueries",
    ()=>justPaginatedQueries,
    "justPublic",
    ()=>justPublic,
    "justQueries",
    ()=>justQueries,
    "justSchedulable",
    ()=>justSchedulable,
    "makeFunctionReference",
    ()=>makeFunctionReference
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$server$2f$functionName$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/server/functionName.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$server$2f$components$2f$paths$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/server/components/paths.js [app-ssr] (ecmascript)");
"use strict";
;
;
function getFunctionName(functionReference) {
    const address = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$server$2f$components$2f$paths$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getFunctionAddress"])(functionReference);
    if (address.name === void 0) {
        if (address.functionHandle !== void 0) {
            throw new Error(`Expected function reference like "api.file.func" or "internal.file.func", but received function handle ${address.functionHandle}`);
        } else if (address.reference !== void 0) {
            throw new Error(`Expected function reference in the current component like "api.file.func" or "internal.file.func", but received reference ${address.reference}`);
        }
        throw new Error(`Expected function reference like "api.file.func" or "internal.file.func", but received ${JSON.stringify(address)}`);
    }
    if (typeof functionReference === "string") return functionReference;
    const name = functionReference[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$server$2f$functionName$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["functionName"]];
    if (!name) {
        throw new Error(`${functionReference} is not a functionReference`);
    }
    return name;
}
function makeFunctionReference(name) {
    return {
        [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$server$2f$functionName$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["functionName"]]: name
    };
}
function createApi(pathParts = []) {
    const handler = {
        get (_, prop) {
            if (typeof prop === "string") {
                const newParts = [
                    ...pathParts,
                    prop
                ];
                return createApi(newParts);
            } else if (prop === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$server$2f$functionName$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["functionName"]) {
                if (pathParts.length < 2) {
                    const found = [
                        "api",
                        ...pathParts
                    ].join(".");
                    throw new Error(`API path is expected to be of the form \`api.moduleName.functionName\`. Found: \`${found}\``);
                }
                const path = pathParts.slice(0, -1).join("/");
                const exportName = pathParts[pathParts.length - 1];
                if (exportName === "default") {
                    return path;
                } else {
                    return path + ":" + exportName;
                }
            } else if (prop === Symbol.toStringTag) {
                return "FunctionReference";
            } else {
                return void 0;
            }
        }
    };
    return new Proxy({}, handler);
}
function filterApi(api) {
    return api;
}
function justInternal(api) {
    return api;
}
function justPublic(api) {
    return api;
}
function justQueries(api) {
    return api;
}
function justMutations(api) {
    return api;
}
function justActions(api) {
    return api;
}
function justPaginatedQueries(api) {
    return api;
}
function justSchedulable(api) {
    return api;
}
const anyApi = createApi(); //# sourceMappingURL=api.js.map
}),
"[project]/node_modules/convex/dist/esm/browser/sync/optimistic_updates_impl.js [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "OptimisticQueryResults",
    ()=>OptimisticQueryResults
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$server$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/server/api.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$common$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/common/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$logging$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/browser/logging.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$udf_path_utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/browser/sync/udf_path_utils.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$errors$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/values/errors.js [app-ssr] (ecmascript)");
"use strict";
var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value)=>key in obj ? __defProp(obj, key, {
        enumerable: true,
        configurable: true,
        writable: true,
        value
    }) : obj[key] = value;
var __publicField = (obj, key, value)=>__defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
;
;
;
;
;
class OptimisticLocalStoreImpl {
    constructor(queryResults){
        // A references of the query results in OptimisticQueryResults
        __publicField(this, "queryResults");
        // All of the queries modified by this class
        __publicField(this, "modifiedQueries");
        this.queryResults = queryResults;
        this.modifiedQueries = [];
    }
    getQuery(query, ...args) {
        const queryArgs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$common$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["parseArgs"])(args[0]);
        const name = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$server$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getFunctionName"])(query);
        const queryResult = this.queryResults.get((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$udf_path_utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["serializePathAndArgs"])(name, queryArgs));
        if (queryResult === void 0) {
            return void 0;
        }
        return OptimisticLocalStoreImpl.queryValue(queryResult.result);
    }
    getAllQueries(query) {
        const queriesWithName = [];
        const name = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$server$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getFunctionName"])(query);
        for (const queryResult of this.queryResults.values()){
            if (queryResult.udfPath === (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$udf_path_utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["canonicalizeUdfPath"])(name)) {
                queriesWithName.push({
                    args: queryResult.args,
                    value: OptimisticLocalStoreImpl.queryValue(queryResult.result)
                });
            }
        }
        return queriesWithName;
    }
    setQuery(queryReference, args, value) {
        const queryArgs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$common$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["parseArgs"])(args);
        const name = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$server$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getFunctionName"])(queryReference);
        const queryToken = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$udf_path_utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["serializePathAndArgs"])(name, queryArgs);
        let result;
        if (value === void 0) {
            result = void 0;
        } else {
            result = {
                success: true,
                value,
                // It's an optimistic update, so there are no function logs to show.
                logLines: []
            };
        }
        const query = {
            udfPath: name,
            args: queryArgs,
            result
        };
        this.queryResults.set(queryToken, query);
        this.modifiedQueries.push(queryToken);
    }
    static queryValue(result) {
        if (result === void 0) {
            return void 0;
        } else if (result.success) {
            return result.value;
        } else {
            return void 0;
        }
    }
}
class OptimisticQueryResults {
    constructor(){
        __publicField(this, "queryResults");
        __publicField(this, "optimisticUpdates");
        this.queryResults = /* @__PURE__ */ new Map();
        this.optimisticUpdates = [];
    }
    /**
   * Apply all optimistic updates on top of server query results
   */ ingestQueryResultsFromServer(serverQueryResults, optimisticUpdatesToDrop) {
        this.optimisticUpdates = this.optimisticUpdates.filter((updateAndId)=>{
            return !optimisticUpdatesToDrop.has(updateAndId.mutationId);
        });
        const oldQueryResults = this.queryResults;
        this.queryResults = new Map(serverQueryResults);
        const localStore = new OptimisticLocalStoreImpl(this.queryResults);
        for (const updateAndId of this.optimisticUpdates){
            updateAndId.update(localStore);
        }
        const changedQueries = [];
        for (const [queryToken, query] of this.queryResults){
            const oldQuery = oldQueryResults.get(queryToken);
            if (oldQuery === void 0 || oldQuery.result !== query.result) {
                changedQueries.push(queryToken);
            }
        }
        return changedQueries;
    }
    applyOptimisticUpdate(update, mutationId) {
        this.optimisticUpdates.push({
            update,
            mutationId
        });
        const localStore = new OptimisticLocalStoreImpl(this.queryResults);
        update(localStore);
        return localStore.modifiedQueries;
    }
    /**
   * @internal
   */ rawQueryResult(queryToken) {
        return this.queryResults.get(queryToken);
    }
    queryResult(queryToken) {
        const query = this.queryResults.get(queryToken);
        if (query === void 0) {
            return void 0;
        }
        const result = query.result;
        if (result === void 0) {
            return void 0;
        } else if (result.success) {
            return result.value;
        } else {
            if (result.errorData !== void 0) {
                throw (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$logging$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardData"])(result, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$errors$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ConvexError"]((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$logging$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createHybridErrorStacktrace"])("query", query.udfPath, result)));
            }
            throw new Error((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$logging$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createHybridErrorStacktrace"])("query", query.udfPath, result));
        }
    }
    hasQueryResult(queryToken) {
        return this.queryResults.get(queryToken) !== void 0;
    }
    /**
   * @internal
   */ queryLogs(queryToken) {
        const query = this.queryResults.get(queryToken);
        return query?.result?.logLines;
    }
} //# sourceMappingURL=optimistic_updates_impl.js.map
}),
"[project]/node_modules/convex/dist/esm/browser/long.js [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "Long",
    ()=>Long
]);
"use strict";
var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value)=>key in obj ? __defProp(obj, key, {
        enumerable: true,
        configurable: true,
        writable: true,
        value
    }) : obj[key] = value;
var __publicField = (obj, key, value)=>__defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
class Long {
    constructor(low, high){
        __publicField(this, "low");
        __publicField(this, "high");
        __publicField(this, "__isUnsignedLong__");
        this.low = low | 0;
        this.high = high | 0;
        this.__isUnsignedLong__ = true;
    }
    static isLong(obj) {
        return (obj && obj.__isUnsignedLong__) === true;
    }
    // prettier-ignore
    static fromBytesLE(bytes) {
        return new Long(bytes[0] | bytes[1] << 8 | bytes[2] << 16 | bytes[3] << 24, bytes[4] | bytes[5] << 8 | bytes[6] << 16 | bytes[7] << 24);
    }
    // prettier-ignore
    toBytesLE() {
        const hi = this.high;
        const lo = this.low;
        return [
            lo & 255,
            lo >>> 8 & 255,
            lo >>> 16 & 255,
            lo >>> 24,
            hi & 255,
            hi >>> 8 & 255,
            hi >>> 16 & 255,
            hi >>> 24
        ];
    }
    static fromNumber(value) {
        if (isNaN(value)) return UZERO;
        if (value < 0) return UZERO;
        if (value >= TWO_PWR_64_DBL) return MAX_UNSIGNED_VALUE;
        return new Long(value % TWO_PWR_32_DBL | 0, value / TWO_PWR_32_DBL | 0);
    }
    toString() {
        return (BigInt(this.high) * BigInt(TWO_PWR_32_DBL) + BigInt(this.low)).toString();
    }
    equals(other) {
        if (!Long.isLong(other)) other = Long.fromValue(other);
        if (this.high >>> 31 === 1 && other.high >>> 31 === 1) return false;
        return this.high === other.high && this.low === other.low;
    }
    notEquals(other) {
        return !this.equals(other);
    }
    comp(other) {
        if (!Long.isLong(other)) other = Long.fromValue(other);
        if (this.equals(other)) return 0;
        return other.high >>> 0 > this.high >>> 0 || other.high === this.high && other.low >>> 0 > this.low >>> 0 ? -1 : 1;
    }
    lessThanOrEqual(other) {
        return this.comp(/* validates */ other) <= 0;
    }
    static fromValue(val) {
        if (typeof val === "number") return Long.fromNumber(val);
        return new Long(val.low, val.high);
    }
}
const UZERO = new Long(0, 0);
const TWO_PWR_16_DBL = 1 << 16;
const TWO_PWR_32_DBL = TWO_PWR_16_DBL * TWO_PWR_16_DBL;
const TWO_PWR_64_DBL = TWO_PWR_32_DBL * TWO_PWR_32_DBL;
const MAX_UNSIGNED_VALUE = new Long(4294967295 | 0, 4294967295 | 0); //# sourceMappingURL=long.js.map
}),
"[project]/node_modules/convex/dist/esm/browser/sync/remote_query_set.js [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "RemoteQuerySet",
    ()=>RemoteQuerySet
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/values/index.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$value$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/values/value.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$long$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/browser/long.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$logging$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/browser/logging.js [app-ssr] (ecmascript)");
"use strict";
var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value)=>key in obj ? __defProp(obj, key, {
        enumerable: true,
        configurable: true,
        writable: true,
        value
    }) : obj[key] = value;
var __publicField = (obj, key, value)=>__defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
;
;
;
class RemoteQuerySet {
    constructor(queryPath, logger){
        __publicField(this, "version");
        __publicField(this, "remoteQuerySet");
        __publicField(this, "queryPath");
        __publicField(this, "logger");
        this.version = {
            querySet: 0,
            ts: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$long$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Long"].fromNumber(0),
            identity: 0
        };
        this.remoteQuerySet = /* @__PURE__ */ new Map();
        this.queryPath = queryPath;
        this.logger = logger;
    }
    transition(transition) {
        const start = transition.startVersion;
        if (this.version.querySet !== start.querySet || this.version.ts.notEquals(start.ts) || this.version.identity !== start.identity) {
            throw new Error(`Invalid start version: ${start.ts.toString()}:${start.querySet}`);
        }
        for (const modification of transition.modifications){
            switch(modification.type){
                case "QueryUpdated":
                    {
                        const queryPath = this.queryPath(modification.queryId);
                        if (queryPath) {
                            for (const line of modification.logLines){
                                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$logging$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["logForFunction"])(this.logger, "info", "query", queryPath, line);
                            }
                        }
                        const value = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$value$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsonToConvex"])(modification.value ?? null);
                        this.remoteQuerySet.set(modification.queryId, {
                            success: true,
                            value,
                            logLines: modification.logLines
                        });
                        break;
                    }
                case "QueryFailed":
                    {
                        const queryPath = this.queryPath(modification.queryId);
                        if (queryPath) {
                            for (const line of modification.logLines){
                                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$logging$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["logForFunction"])(this.logger, "info", "query", queryPath, line);
                            }
                        }
                        const { errorData } = modification;
                        this.remoteQuerySet.set(modification.queryId, {
                            success: false,
                            errorMessage: modification.errorMessage,
                            errorData: errorData !== void 0 ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$value$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsonToConvex"])(errorData) : void 0,
                            logLines: modification.logLines
                        });
                        break;
                    }
                case "QueryRemoved":
                    {
                        this.remoteQuerySet.delete(modification.queryId);
                        break;
                    }
                default:
                    {
                        modification;
                        throw new Error(`Invalid modification ${modification.type}`);
                    }
            }
        }
        this.version = transition.endVersion;
    }
    remoteQueryResults() {
        return this.remoteQuerySet;
    }
    timestamp() {
        return this.version.ts;
    }
} //# sourceMappingURL=remote_query_set.js.map
}),
"[project]/node_modules/convex/dist/esm/values/base64.js [app-ssr] (ecmascript) <export * as Base64>", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "Base64",
    ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$base64$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$base64$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/values/base64.js [app-ssr] (ecmascript)");
}),
"[project]/node_modules/convex/dist/esm/browser/sync/protocol.js [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "encodeClientMessage",
    ()=>encodeClientMessage,
    "longToU64",
    ()=>longToU64,
    "parseServerMessage",
    ()=>parseServerMessage,
    "u64ToLong",
    ()=>u64ToLong
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/values/index.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$base64$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__Base64$3e$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/values/base64.js [app-ssr] (ecmascript) <export * as Base64>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$long$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/browser/long.js [app-ssr] (ecmascript)");
"use strict";
;
;
function u64ToLong(encoded) {
    const integerBytes = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$base64$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__Base64$3e$__["Base64"].toByteArray(encoded);
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$long$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Long"].fromBytesLE(Array.from(integerBytes));
}
function longToU64(raw) {
    const integerBytes = new Uint8Array(raw.toBytesLE());
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$base64$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__Base64$3e$__["Base64"].fromByteArray(integerBytes);
}
function parseServerMessage(encoded) {
    switch(encoded.type){
        case "FatalError":
        case "AuthError":
        case "ActionResponse":
        case "Ping":
            {
                return {
                    ...encoded
                };
            }
        case "MutationResponse":
            {
                if (encoded.success) {
                    return {
                        ...encoded,
                        ts: u64ToLong(encoded.ts)
                    };
                } else {
                    return {
                        ...encoded
                    };
                }
            }
        case "Transition":
            {
                return {
                    ...encoded,
                    startVersion: {
                        ...encoded.startVersion,
                        ts: u64ToLong(encoded.startVersion.ts)
                    },
                    endVersion: {
                        ...encoded.endVersion,
                        ts: u64ToLong(encoded.endVersion.ts)
                    }
                };
            }
        default:
            {
                encoded;
            }
    }
    return void 0;
}
function encodeClientMessage(message) {
    switch(message.type){
        case "Authenticate":
        case "ModifyQuerySet":
        case "Mutation":
        case "Action":
        case "Event":
            {
                return {
                    ...message
                };
            }
        case "Connect":
            {
                if (message.maxObservedTimestamp !== void 0) {
                    return {
                        ...message,
                        maxObservedTimestamp: longToU64(message.maxObservedTimestamp)
                    };
                } else {
                    return {
                        ...message,
                        maxObservedTimestamp: void 0
                    };
                }
            }
        default:
            {
                message;
            }
    }
    return void 0;
} //# sourceMappingURL=protocol.js.map
}),
"[project]/node_modules/convex/dist/esm/browser/sync/web_socket_manager.js [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "WebSocketManager",
    ()=>WebSocketManager
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$protocol$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/browser/sync/protocol.js [app-ssr] (ecmascript)");
"use strict";
var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value)=>key in obj ? __defProp(obj, key, {
        enumerable: true,
        configurable: true,
        writable: true,
        value
    }) : obj[key] = value;
var __publicField = (obj, key, value)=>__defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
;
const CLOSE_NORMAL = 1e3;
const CLOSE_GOING_AWAY = 1001;
const CLOSE_NO_STATUS = 1005;
const CLOSE_NOT_FOUND = 4040;
const serverDisconnectErrors = {
    // A known error, e.g. during a restart or push
    InternalServerError: {
        timeout: 1e3
    },
    // ErrorMetadata::overloaded() messages that we realy should back off
    SubscriptionsWorkerFullError: {
        timeout: 3e3
    },
    TooManyConcurrentRequests: {
        timeout: 3e3
    },
    CommitterFullError: {
        timeout: 3e3
    },
    AwsTooManyRequestsException: {
        timeout: 3e3
    },
    ExecuteFullError: {
        timeout: 3e3
    },
    SystemTimeoutError: {
        timeout: 3e3
    },
    ExpiredInQueue: {
        timeout: 3e3
    },
    // ErrorMetadata::feature_temporarily_unavailable() that typically indicate a deploy just happened
    VectorIndexesUnavailable: {
        timeout: 1e3
    },
    SearchIndexesUnavailable: {
        timeout: 1e3
    },
    TableSummariesUnavailable: {
        timeout: 1e3
    },
    // More ErrorMeatadata::overloaded()
    VectorIndexTooLarge: {
        timeout: 3e3
    },
    SearchIndexTooLarge: {
        timeout: 3e3
    },
    TooManyWritesInTimePeriod: {
        timeout: 3e3
    }
};
function classifyDisconnectError(s) {
    if (s === void 0) return "Unknown";
    for (const prefix of Object.keys(serverDisconnectErrors)){
        if (s.startsWith(prefix)) {
            return prefix;
        }
    }
    return "Unknown";
}
class WebSocketManager {
    constructor(uri, callbacks, webSocketConstructor, logger, markConnectionStateDirty){
        this.markConnectionStateDirty = markConnectionStateDirty;
        __publicField(this, "socket");
        __publicField(this, "connectionCount");
        __publicField(this, "_hasEverConnected", false);
        __publicField(this, "lastCloseReason");
        /** Upon HTTPS/WSS failure, the first jittered backoff duration, in ms. */ __publicField(this, "defaultInitialBackoff");
        /** We backoff exponentially, but we need to cap that--this is the jittered max. */ __publicField(this, "maxBackoff");
        /** How many times have we failed consecutively? */ __publicField(this, "retries");
        /** How long before lack of server response causes us to initiate a reconnect,
     * in ms */ __publicField(this, "serverInactivityThreshold");
        __publicField(this, "reconnectDueToServerInactivityTimeout");
        __publicField(this, "uri");
        __publicField(this, "onOpen");
        __publicField(this, "onResume");
        __publicField(this, "onMessage");
        __publicField(this, "webSocketConstructor");
        __publicField(this, "logger");
        __publicField(this, "onServerDisconnectError");
        this.webSocketConstructor = webSocketConstructor;
        this.socket = {
            state: "disconnected"
        };
        this.connectionCount = 0;
        this.lastCloseReason = "InitialConnect";
        this.defaultInitialBackoff = 1e3;
        this.maxBackoff = 16e3;
        this.retries = 0;
        this.serverInactivityThreshold = 6e4;
        this.reconnectDueToServerInactivityTimeout = null;
        this.uri = uri;
        this.onOpen = callbacks.onOpen;
        this.onResume = callbacks.onResume;
        this.onMessage = callbacks.onMessage;
        this.onServerDisconnectError = callbacks.onServerDisconnectError;
        this.logger = logger;
        this.connect();
    }
    setSocketState(state) {
        this.socket = state;
        this._logVerbose(`socket state changed: ${this.socket.state}, paused: ${"paused" in this.socket ? this.socket.paused : void 0}`);
        this.markConnectionStateDirty();
    }
    connect() {
        if (this.socket.state === "terminated") {
            return;
        }
        if (this.socket.state !== "disconnected" && this.socket.state !== "stopped") {
            throw new Error("Didn't start connection from disconnected state: " + this.socket.state);
        }
        const ws = new this.webSocketConstructor(this.uri);
        this._logVerbose("constructed WebSocket");
        this.setSocketState({
            state: "connecting",
            ws,
            paused: "no"
        });
        this.resetServerInactivityTimeout();
        ws.onopen = ()=>{
            this.logger.logVerbose("begin ws.onopen");
            if (this.socket.state !== "connecting") {
                throw new Error("onopen called with socket not in connecting state");
            }
            this.setSocketState({
                state: "ready",
                ws,
                paused: this.socket.paused === "yes" ? "uninitialized" : "no"
            });
            this.resetServerInactivityTimeout();
            if (this.socket.paused === "no") {
                this._hasEverConnected = true;
                this.onOpen({
                    connectionCount: this.connectionCount,
                    lastCloseReason: this.lastCloseReason
                });
            }
            if (this.lastCloseReason !== "InitialConnect") {
                this.logger.log("WebSocket reconnected");
            }
            this.connectionCount += 1;
            this.lastCloseReason = null;
        };
        ws.onerror = (error)=>{
            const message = error.message;
            this.logger.log(`WebSocket error: ${message}`);
        };
        ws.onmessage = (message)=>{
            this.resetServerInactivityTimeout();
            const serverMessage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$protocol$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["parseServerMessage"])(JSON.parse(message.data));
            this._logVerbose(`received ws message with type ${serverMessage.type}`);
            const response = this.onMessage(serverMessage);
            if (response.hasSyncedPastLastReconnect) {
                this.retries = 0;
                this.markConnectionStateDirty();
            }
        };
        ws.onclose = (event)=>{
            this._logVerbose("begin ws.onclose");
            if (this.lastCloseReason === null) {
                this.lastCloseReason = event.reason ?? "OnCloseInvoked";
            }
            if (event.code !== CLOSE_NORMAL && event.code !== CLOSE_GOING_AWAY && // This commonly gets fired on mobile apps when the app is backgrounded
            event.code !== CLOSE_NO_STATUS && event.code !== CLOSE_NOT_FOUND) {
                let msg = `WebSocket closed with code ${event.code}`;
                if (event.reason) {
                    msg += `: ${event.reason}`;
                }
                this.logger.log(msg);
                if (this.onServerDisconnectError && event.reason) {
                    this.onServerDisconnectError(msg);
                }
            }
            const reason = classifyDisconnectError(event.reason);
            this.scheduleReconnect(reason);
            return;
        };
    }
    /**
   * @returns The state of the {@link Socket}.
   */ socketState() {
        return this.socket.state;
    }
    /**
   * @param message - A ClientMessage to send.
   * @returns Whether the message (might have been) sent.
   */ sendMessage(message) {
        const messageForLog = {
            type: message.type,
            ...message.type === "Authenticate" && message.tokenType === "User" ? {
                value: `...${message.value.slice(-7)}`
            } : {}
        };
        if (this.socket.state === "ready" && this.socket.paused === "no") {
            const encodedMessage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$protocol$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["encodeClientMessage"])(message);
            const request = JSON.stringify(encodedMessage);
            try {
                this.socket.ws.send(request);
            } catch (error) {
                this.logger.log(`Failed to send message on WebSocket, reconnecting: ${error}`);
                this.closeAndReconnect("FailedToSendMessage");
            }
            this._logVerbose(`sent message with type ${message.type}: ${JSON.stringify(messageForLog)}`);
            return true;
        }
        this._logVerbose(`message not sent (socket state: ${this.socket.state}, paused: ${"paused" in this.socket ? this.socket.paused : void 0}): ${JSON.stringify(messageForLog)}`);
        return false;
    }
    resetServerInactivityTimeout() {
        if (this.socket.state === "terminated") {
            return;
        }
        if (this.reconnectDueToServerInactivityTimeout !== null) {
            clearTimeout(this.reconnectDueToServerInactivityTimeout);
            this.reconnectDueToServerInactivityTimeout = null;
        }
        this.reconnectDueToServerInactivityTimeout = setTimeout(()=>{
            this.closeAndReconnect("InactiveServer");
        }, this.serverInactivityThreshold);
    }
    scheduleReconnect(reason) {
        this.socket = {
            state: "disconnected"
        };
        const backoff = this.nextBackoff(reason);
        this.markConnectionStateDirty();
        this.logger.log(`Attempting reconnect in ${backoff}ms`);
        setTimeout(()=>this.connect(), backoff);
    }
    /**
   * Close the WebSocket and schedule a reconnect.
   *
   * This should be used when we hit an error and would like to restart the session.
   */ closeAndReconnect(closeReason) {
        this._logVerbose(`begin closeAndReconnect with reason ${closeReason}`);
        switch(this.socket.state){
            case "disconnected":
            case "terminated":
            case "stopped":
                return;
            case "connecting":
            case "ready":
                {
                    this.lastCloseReason = closeReason;
                    void this.close();
                    this.scheduleReconnect("client");
                    return;
                }
            default:
                {
                    this.socket;
                }
        }
    }
    /**
   * Close the WebSocket, being careful to clear the onclose handler to avoid re-entrant
   * calls. Use this instead of directly calling `ws.close()`
   *
   * It is the callers responsibility to update the state after this method is called so that the
   * closed socket is not accessible or used again after this method is called
   */ close() {
        switch(this.socket.state){
            case "disconnected":
            case "terminated":
            case "stopped":
                return Promise.resolve();
            case "connecting":
                {
                    const ws = this.socket.ws;
                    return new Promise((r)=>{
                        ws.onclose = ()=>{
                            this._logVerbose("Closed after connecting");
                            r();
                        };
                        ws.onopen = ()=>{
                            this._logVerbose("Opened after connecting");
                            ws.close();
                        };
                    });
                }
            case "ready":
                {
                    this._logVerbose("ws.close called");
                    const ws = this.socket.ws;
                    const result = new Promise((r)=>{
                        ws.onclose = ()=>{
                            r();
                        };
                    });
                    ws.close();
                    return result;
                }
            default:
                {
                    this.socket;
                    return Promise.resolve();
                }
        }
    }
    /**
   * Close the WebSocket and do not reconnect.
   * @returns A Promise that resolves when the WebSocket `onClose` callback is called.
   */ terminate() {
        if (this.reconnectDueToServerInactivityTimeout) {
            clearTimeout(this.reconnectDueToServerInactivityTimeout);
        }
        switch(this.socket.state){
            case "terminated":
            case "stopped":
            case "disconnected":
            case "connecting":
            case "ready":
                {
                    const result = this.close();
                    this.setSocketState({
                        state: "terminated"
                    });
                    return result;
                }
            default:
                {
                    this.socket;
                    throw new Error(`Invalid websocket state: ${this.socket.state}`);
                }
        }
    }
    stop() {
        switch(this.socket.state){
            case "terminated":
                return Promise.resolve();
            case "connecting":
            case "stopped":
            case "disconnected":
            case "ready":
                {
                    const result = this.close();
                    this.socket = {
                        state: "stopped"
                    };
                    return result;
                }
            default:
                {
                    this.socket;
                    return Promise.resolve();
                }
        }
    }
    /**
   * Create a new WebSocket after a previous `stop()`, unless `terminate()` was
   * called before.
   */ tryRestart() {
        switch(this.socket.state){
            case "stopped":
                break;
            case "terminated":
            case "connecting":
            case "ready":
            case "disconnected":
                this.logger.logVerbose("Restart called without stopping first");
                return;
            default:
                {
                    this.socket;
                }
        }
        this.connect();
    }
    pause() {
        switch(this.socket.state){
            case "disconnected":
            case "stopped":
            case "terminated":
                return;
            case "connecting":
            case "ready":
                {
                    this.socket = {
                        ...this.socket,
                        paused: "yes"
                    };
                    return;
                }
            default:
                {
                    this.socket;
                    return;
                }
        }
    }
    /**
   * Resume the state machine if previously paused.
   */ resume() {
        switch(this.socket.state){
            case "connecting":
                this.socket = {
                    ...this.socket,
                    paused: "no"
                };
                return;
            case "ready":
                if (this.socket.paused === "uninitialized") {
                    this.socket = {
                        ...this.socket,
                        paused: "no"
                    };
                    this.onOpen({
                        connectionCount: this.connectionCount,
                        lastCloseReason: this.lastCloseReason
                    });
                } else if (this.socket.paused === "yes") {
                    this.socket = {
                        ...this.socket,
                        paused: "no"
                    };
                    this.onResume();
                }
                return;
            case "terminated":
            case "stopped":
            case "disconnected":
                return;
            default:
                {
                    this.socket;
                }
        }
        this.connect();
    }
    connectionState() {
        return {
            isConnected: this.socket.state === "ready",
            hasEverConnected: this._hasEverConnected,
            connectionCount: this.connectionCount,
            connectionRetries: this.retries
        };
    }
    _logVerbose(message) {
        this.logger.logVerbose(message);
    }
    nextBackoff(reason) {
        const initialBackoff = reason === "client" ? 100 : reason === "Unknown" ? this.defaultInitialBackoff : serverDisconnectErrors[reason].timeout;
        const baseBackoff = initialBackoff * Math.pow(2, this.retries);
        this.retries += 1;
        const actualBackoff = Math.min(baseBackoff, this.maxBackoff);
        const jitter = actualBackoff * (Math.random() - 0.5);
        return actualBackoff + jitter;
    }
} //# sourceMappingURL=web_socket_manager.js.map
}),
"[project]/node_modules/convex/dist/esm/browser/sync/session.js [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "newSessionId",
    ()=>newSessionId
]);
"use strict";
function newSessionId() {
    return uuidv4();
}
function uuidv4() {
    return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, (c)=>{
        const r = Math.random() * 16 | 0, v = c === "x" ? r : r & 3 | 8;
        return v.toString(16);
    });
} //# sourceMappingURL=session.js.map
}),
"[project]/node_modules/convex/dist/esm/browser/sync/authentication_manager.js [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "AuthenticationManager",
    ()=>AuthenticationManager
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jwt$2d$decode$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/jwt-decode/build/esm/index.js [app-ssr] (ecmascript)");
"use strict";
var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value)=>key in obj ? __defProp(obj, key, {
        enumerable: true,
        configurable: true,
        writable: true,
        value
    }) : obj[key] = value;
var __publicField = (obj, key, value)=>__defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
;
const MAXIMUM_REFRESH_DELAY = 20 * 24 * 60 * 60 * 1e3;
const MAX_TOKEN_CONFIRMATION_ATTEMPTS = 2;
class AuthenticationManager {
    constructor(syncState, callbacks, config){
        __publicField(this, "authState", {
            state: "noAuth"
        });
        // Used to detect races involving `setConfig` calls
        // while a token is being fetched.
        __publicField(this, "configVersion", 0);
        // Shared by the BaseClient so that the auth manager can easily inspect it
        __publicField(this, "syncState");
        // Passed down by BaseClient, sends a message to the server
        __publicField(this, "authenticate");
        __publicField(this, "stopSocket");
        __publicField(this, "tryRestartSocket");
        __publicField(this, "pauseSocket");
        __publicField(this, "resumeSocket");
        // Passed down by BaseClient, sends a message to the server
        __publicField(this, "clearAuth");
        __publicField(this, "logger");
        __publicField(this, "refreshTokenLeewaySeconds");
        // Number of times we have attempted to confirm the latest token. We retry up
        // to `MAX_TOKEN_CONFIRMATION_ATTEMPTS` times.
        __publicField(this, "tokenConfirmationAttempts", 0);
        this.syncState = syncState;
        this.authenticate = callbacks.authenticate;
        this.stopSocket = callbacks.stopSocket;
        this.tryRestartSocket = callbacks.tryRestartSocket;
        this.pauseSocket = callbacks.pauseSocket;
        this.resumeSocket = callbacks.resumeSocket;
        this.clearAuth = callbacks.clearAuth;
        this.logger = config.logger;
        this.refreshTokenLeewaySeconds = config.refreshTokenLeewaySeconds;
    }
    async setConfig(fetchToken, onChange) {
        this.resetAuthState();
        this._logVerbose("pausing WS for auth token fetch");
        this.pauseSocket();
        const token = await this.fetchTokenAndGuardAgainstRace(fetchToken, {
            forceRefreshToken: false
        });
        if (token.isFromOutdatedConfig) {
            return;
        }
        if (token.value) {
            this.setAuthState({
                state: "waitingForServerConfirmationOfCachedToken",
                config: {
                    fetchToken,
                    onAuthChange: onChange
                },
                hasRetried: false
            });
            this.authenticate(token.value);
        } else {
            this.setAuthState({
                state: "initialRefetch",
                config: {
                    fetchToken,
                    onAuthChange: onChange
                }
            });
            await this.refetchToken();
        }
        this._logVerbose("resuming WS after auth token fetch");
        this.resumeSocket();
    }
    onTransition(serverMessage) {
        if (!this.syncState.isCurrentOrNewerAuthVersion(serverMessage.endVersion.identity)) {
            return;
        }
        if (serverMessage.endVersion.identity <= serverMessage.startVersion.identity) {
            return;
        }
        if (this.authState.state === "waitingForServerConfirmationOfCachedToken") {
            this._logVerbose("server confirmed auth token is valid");
            void this.refetchToken();
            this.authState.config.onAuthChange(true);
            return;
        }
        if (this.authState.state === "waitingForServerConfirmationOfFreshToken") {
            this._logVerbose("server confirmed new auth token is valid");
            this.scheduleTokenRefetch(this.authState.token);
            this.tokenConfirmationAttempts = 0;
            if (!this.authState.hadAuth) {
                this.authState.config.onAuthChange(true);
            }
        }
    }
    onAuthError(serverMessage) {
        if (serverMessage.authUpdateAttempted === false && (this.authState.state === "waitingForServerConfirmationOfFreshToken" || this.authState.state === "waitingForServerConfirmationOfCachedToken")) {
            this._logVerbose("ignoring non-auth token expired error");
            return;
        }
        const { baseVersion } = serverMessage;
        if (!this.syncState.isCurrentOrNewerAuthVersion(baseVersion + 1)) {
            this._logVerbose("ignoring auth error for previous auth attempt");
            return;
        }
        void this.tryToReauthenticate(serverMessage);
        return;
    }
    // This is similar to `refetchToken` defined below, in fact we
    // don't represent them as different states, but it is different
    // in that we pause the WebSocket so that mutations
    // don't retry with bad auth.
    async tryToReauthenticate(serverMessage) {
        this._logVerbose(`attempting to reauthenticate: ${serverMessage.error}`);
        if (// No way to fetch another token, kaboom
        this.authState.state === "noAuth" || // We failed on a fresh token. After a small number of retries, we give up
        // and clear the auth state to avoid infinite retries.
        this.authState.state === "waitingForServerConfirmationOfFreshToken" && this.tokenConfirmationAttempts >= MAX_TOKEN_CONFIRMATION_ATTEMPTS) {
            this.logger.error(`Failed to authenticate: "${serverMessage.error}", check your server auth config`);
            if (this.syncState.hasAuth()) {
                this.syncState.clearAuth();
            }
            if (this.authState.state !== "noAuth") {
                this.setAndReportAuthFailed(this.authState.config.onAuthChange);
            }
            return;
        }
        if (this.authState.state === "waitingForServerConfirmationOfFreshToken") {
            this.tokenConfirmationAttempts++;
            this._logVerbose(`retrying reauthentication, ${MAX_TOKEN_CONFIRMATION_ATTEMPTS - this.tokenConfirmationAttempts} attempts remaining`);
        }
        await this.stopSocket();
        const token = await this.fetchTokenAndGuardAgainstRace(this.authState.config.fetchToken, {
            forceRefreshToken: true
        });
        if (token.isFromOutdatedConfig) {
            return;
        }
        if (token.value && this.syncState.isNewAuth(token.value)) {
            this.authenticate(token.value);
            this.setAuthState({
                state: "waitingForServerConfirmationOfFreshToken",
                config: this.authState.config,
                token: token.value,
                hadAuth: this.authState.state === "notRefetching" || this.authState.state === "waitingForScheduledRefetch"
            });
        } else {
            this._logVerbose("reauthentication failed, could not fetch a new token");
            if (this.syncState.hasAuth()) {
                this.syncState.clearAuth();
            }
            this.setAndReportAuthFailed(this.authState.config.onAuthChange);
        }
        this.tryRestartSocket();
    }
    // Force refetch the token and schedule another refetch
    // before the token expires - an active client should never
    // need to reauthenticate.
    async refetchToken() {
        if (this.authState.state === "noAuth") {
            return;
        }
        this._logVerbose("refetching auth token");
        const token = await this.fetchTokenAndGuardAgainstRace(this.authState.config.fetchToken, {
            forceRefreshToken: true
        });
        if (token.isFromOutdatedConfig) {
            return;
        }
        if (token.value) {
            if (this.syncState.isNewAuth(token.value)) {
                this.setAuthState({
                    state: "waitingForServerConfirmationOfFreshToken",
                    hadAuth: this.syncState.hasAuth(),
                    token: token.value,
                    config: this.authState.config
                });
                this.authenticate(token.value);
            } else {
                this.setAuthState({
                    state: "notRefetching",
                    config: this.authState.config
                });
            }
        } else {
            this._logVerbose("refetching token failed");
            if (this.syncState.hasAuth()) {
                this.clearAuth();
            }
            this.setAndReportAuthFailed(this.authState.config.onAuthChange);
        }
        this._logVerbose("restarting WS after auth token fetch (if currently stopped)");
        this.tryRestartSocket();
    }
    scheduleTokenRefetch(token) {
        if (this.authState.state === "noAuth") {
            return;
        }
        const decodedToken = this.decodeToken(token);
        if (!decodedToken) {
            this.logger.error("Auth token is not a valid JWT, cannot refetch the token");
            return;
        }
        const { iat, exp } = decodedToken;
        if (!iat || !exp) {
            this.logger.error("Auth token does not have required fields, cannot refetch the token");
            return;
        }
        const tokenValiditySeconds = exp - iat;
        if (tokenValiditySeconds <= 2) {
            this.logger.error("Auth token does not live long enough, cannot refetch the token");
            return;
        }
        let delay = Math.min(MAXIMUM_REFRESH_DELAY, (tokenValiditySeconds - this.refreshTokenLeewaySeconds) * 1e3);
        if (delay <= 0) {
            this.logger.warn(`Refetching auth token immediately, configured leeway ${this.refreshTokenLeewaySeconds}s is larger than the token's lifetime ${tokenValiditySeconds}s`);
            delay = 0;
        }
        const refetchTokenTimeoutId = setTimeout(()=>{
            this._logVerbose("running scheduled token refetch");
            void this.refetchToken();
        }, delay);
        this.setAuthState({
            state: "waitingForScheduledRefetch",
            refetchTokenTimeoutId,
            config: this.authState.config
        });
        this._logVerbose(`scheduled preemptive auth token refetching in ${delay}ms`);
    }
    // Protects against simultaneous calls to `setConfig`
    // while we're fetching a token
    async fetchTokenAndGuardAgainstRace(fetchToken, fetchArgs) {
        const originalConfigVersion = ++this.configVersion;
        this._logVerbose(`fetching token with config version ${originalConfigVersion}`);
        const token = await fetchToken(fetchArgs);
        if (this.configVersion !== originalConfigVersion) {
            this._logVerbose(`stale config version, expected ${originalConfigVersion}, got ${this.configVersion}`);
            return {
                isFromOutdatedConfig: true
            };
        }
        return {
            isFromOutdatedConfig: false,
            value: token
        };
    }
    stop() {
        this.resetAuthState();
        this.configVersion++;
        this._logVerbose(`config version bumped to ${this.configVersion}`);
    }
    setAndReportAuthFailed(onAuthChange) {
        onAuthChange(false);
        this.resetAuthState();
    }
    resetAuthState() {
        this.setAuthState({
            state: "noAuth"
        });
    }
    setAuthState(newAuth) {
        const authStateForLog = newAuth.state === "waitingForServerConfirmationOfFreshToken" ? {
            hadAuth: newAuth.hadAuth,
            state: newAuth.state,
            token: `...${newAuth.token.slice(-7)}`
        } : {
            state: newAuth.state
        };
        this._logVerbose(`setting auth state to ${JSON.stringify(authStateForLog)}`);
        switch(newAuth.state){
            case "waitingForScheduledRefetch":
            case "notRefetching":
            case "noAuth":
                this.tokenConfirmationAttempts = 0;
                break;
            case "waitingForServerConfirmationOfFreshToken":
            case "waitingForServerConfirmationOfCachedToken":
            case "initialRefetch":
                break;
            default:
                {
                    newAuth;
                }
        }
        if (this.authState.state === "waitingForScheduledRefetch") {
            clearTimeout(this.authState.refetchTokenTimeoutId);
            this.syncState.markAuthCompletion();
        }
        this.authState = newAuth;
    }
    decodeToken(token) {
        try {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jwt$2d$decode$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jwtDecode"])(token);
        } catch (e) {
            this._logVerbose(`Error decoding token: ${e instanceof Error ? e.message : "Unknown error"}`);
            return null;
        }
    }
    _logVerbose(message) {
        this.logger.logVerbose(`${message} [v${this.configVersion}]`);
    }
} //# sourceMappingURL=authentication_manager.js.map
}),
"[project]/node_modules/convex/dist/esm/browser/sync/metrics.js [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "getMarksReport",
    ()=>getMarksReport,
    "mark",
    ()=>mark
]);
"use strict";
const markNames = [
    "convexClientConstructed",
    "convexWebSocketOpen",
    "convexFirstMessageReceived"
];
function mark(name, sessionId) {
    const detail = {
        sessionId
    };
    if (typeof performance === "undefined" || !performance.mark) return;
    performance.mark(name, {
        detail
    });
}
function performanceMarkToJson(mark2) {
    let name = mark2.name.slice("convex".length);
    name = name.charAt(0).toLowerCase() + name.slice(1);
    return {
        name,
        startTime: mark2.startTime
    };
}
function getMarksReport(sessionId) {
    if (typeof performance === "undefined" || !performance.getEntriesByName) {
        return [];
    }
    const allMarks = [];
    for (const name of markNames){
        const marks = performance.getEntriesByName(name).filter((entry)=>entry.entryType === "mark").filter((mark2)=>mark2.detail.sessionId === sessionId);
        allMarks.push(...marks);
    }
    return allMarks.map(performanceMarkToJson);
} //# sourceMappingURL=metrics.js.map
}),
"[project]/node_modules/convex/dist/esm/browser/sync/client.js [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "BaseConvexClient",
    ()=>BaseConvexClient
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/values/index.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$value$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/values/value.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$logging$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/browser/logging.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$local_state$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/browser/sync/local_state.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$request_manager$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/browser/sync/request_manager.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$optimistic_updates_impl$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/browser/sync/optimistic_updates_impl.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$remote_query_set$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/browser/sync/remote_query_set.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$udf_path_utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/browser/sync/udf_path_utils.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$web_socket_manager$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/browser/sync/web_socket_manager.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$session$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/browser/sync/session.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$authentication_manager$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/browser/sync/authentication_manager.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$metrics$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/browser/sync/metrics.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$common$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/common/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$errors$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/values/errors.js [app-ssr] (ecmascript)");
"use strict";
var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value)=>key in obj ? __defProp(obj, key, {
        enumerable: true,
        configurable: true,
        writable: true,
        value
    }) : obj[key] = value;
var __publicField = (obj, key, value)=>__defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
;
;
;
;
;
;
;
;
;
;
;
;
;
;
class BaseConvexClient {
    /**
   * @param address - The url of your Convex deployment, often provided
   * by an environment variable. E.g. `https://small-mouse-123.convex.cloud`.
   * @param onTransition - A callback receiving an array of query tokens
   * corresponding to query results that have changed -- additional handlers
   * can be added via `addOnTransitionHandler`.
   * @param options - See {@link BaseConvexClientOptions} for a full description.
   */ constructor(address, onTransition, options){
        __publicField(this, "address");
        __publicField(this, "state");
        __publicField(this, "requestManager");
        __publicField(this, "webSocketManager");
        __publicField(this, "authenticationManager");
        __publicField(this, "remoteQuerySet");
        __publicField(this, "optimisticQueryResults");
        __publicField(this, "_transitionHandlerCounter", 0);
        __publicField(this, "_nextRequestId");
        __publicField(this, "_onTransitionFns", /* @__PURE__ */ new Map());
        __publicField(this, "_sessionId");
        __publicField(this, "firstMessageReceived", false);
        __publicField(this, "debug");
        __publicField(this, "logger");
        __publicField(this, "maxObservedTimestamp");
        __publicField(this, "connectionStateSubscribers", /* @__PURE__ */ new Map());
        __publicField(this, "nextConnectionStateSubscriberId", 0);
        __publicField(this, "_lastPublishedConnectionState");
        /**
     * Call this whenever the connection state may have changed in a way that could
     * require publishing it. Schedules a possibly update.
     */ __publicField(this, "markConnectionStateDirty", ()=>{
            void Promise.resolve().then(()=>{
                const curConnectionState = this.connectionState();
                if (JSON.stringify(curConnectionState) !== JSON.stringify(this._lastPublishedConnectionState)) {
                    this._lastPublishedConnectionState = curConnectionState;
                    for (const cb of this.connectionStateSubscribers.values()){
                        cb(curConnectionState);
                    }
                }
            });
        });
        // Instance property so that `mark()` doesn't need to be called as a method.
        __publicField(this, "mark", (name)=>{
            if (this.debug) {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$metrics$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["mark"])(name, this.sessionId);
            }
        });
        if (typeof address === "object") {
            throw new Error("Passing a ClientConfig object is no longer supported. Pass the URL of the Convex deployment as a string directly.");
        }
        if (options?.skipConvexDeploymentUrlCheck !== true) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$common$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["validateDeploymentUrl"])(address);
        }
        options = {
            ...options
        };
        const authRefreshTokenLeewaySeconds = options.authRefreshTokenLeewaySeconds ?? 2;
        let webSocketConstructor = options.webSocketConstructor;
        if (!webSocketConstructor && typeof WebSocket === "undefined") {
            throw new Error("No WebSocket global variable defined! To use Convex in an environment without WebSocket try the HTTP client: https://docs.convex.dev/api/classes/browser.ConvexHttpClient");
        }
        webSocketConstructor = webSocketConstructor || WebSocket;
        this.debug = options.reportDebugInfoToConvex ?? false;
        this.address = address;
        this.logger = options.logger === false ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$logging$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["instantiateNoopLogger"])({
            verbose: options.verbose ?? false
        }) : options.logger !== true && options.logger ? options.logger : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$logging$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["instantiateDefaultLogger"])({
            verbose: options.verbose ?? false
        });
        const i = address.search("://");
        if (i === -1) {
            throw new Error("Provided address was not an absolute URL.");
        }
        const origin = address.substring(i + 3);
        const protocol = address.substring(0, i);
        let wsProtocol;
        if (protocol === "http") {
            wsProtocol = "ws";
        } else if (protocol === "https") {
            wsProtocol = "wss";
        } else {
            throw new Error(`Unknown parent protocol ${protocol}`);
        }
        const wsUri = `${wsProtocol}://${origin}/api/${__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["version"]}/sync`;
        this.state = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$local_state$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["LocalSyncState"]();
        this.remoteQuerySet = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$remote_query_set$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RemoteQuerySet"]((queryId)=>this.state.queryPath(queryId), this.logger);
        this.requestManager = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$request_manager$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RequestManager"](this.logger, this.markConnectionStateDirty);
        const pauseSocket = ()=>{
            this.webSocketManager.pause();
            this.state.pause();
        };
        this.authenticationManager = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$authentication_manager$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AuthenticationManager"](this.state, {
            authenticate: (token)=>{
                const message = this.state.setAuth(token);
                this.webSocketManager.sendMessage(message);
                return message.baseVersion;
            },
            stopSocket: ()=>this.webSocketManager.stop(),
            tryRestartSocket: ()=>this.webSocketManager.tryRestart(),
            pauseSocket,
            resumeSocket: ()=>this.webSocketManager.resume(),
            clearAuth: ()=>{
                this.clearAuth();
            }
        }, {
            logger: this.logger,
            refreshTokenLeewaySeconds: authRefreshTokenLeewaySeconds
        });
        this.optimisticQueryResults = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$optimistic_updates_impl$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["OptimisticQueryResults"]();
        this.addOnTransitionHandler((transition)=>{
            onTransition(transition.queries.map((q)=>q.token));
        });
        this._nextRequestId = 0;
        this._sessionId = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$session$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["newSessionId"])();
        const { unsavedChangesWarning } = options;
        if ("TURBOPACK compile-time truthy", 1) {
            if (unsavedChangesWarning === true) {
                throw new Error("unsavedChangesWarning requested, but window.addEventListener not found! Remove {unsavedChangesWarning: true} from Convex client options.");
            }
        } else //TURBOPACK unreachable
        ;
        this.webSocketManager = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$web_socket_manager$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WebSocketManager"](wsUri, {
            onOpen: (reconnectMetadata)=>{
                this.mark("convexWebSocketOpen");
                this.webSocketManager.sendMessage({
                    ...reconnectMetadata,
                    type: "Connect",
                    sessionId: this._sessionId,
                    maxObservedTimestamp: this.maxObservedTimestamp
                });
                const oldRemoteQueryResults = new Set(this.remoteQuerySet.remoteQueryResults().keys());
                this.remoteQuerySet = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$remote_query_set$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RemoteQuerySet"]((queryId)=>this.state.queryPath(queryId), this.logger);
                const [querySetModification, authModification] = this.state.restart(oldRemoteQueryResults);
                if (authModification) {
                    this.webSocketManager.sendMessage(authModification);
                }
                this.webSocketManager.sendMessage(querySetModification);
                for (const message of this.requestManager.restart()){
                    this.webSocketManager.sendMessage(message);
                }
            },
            onResume: ()=>{
                const [querySetModification, authModification] = this.state.resume();
                if (authModification) {
                    this.webSocketManager.sendMessage(authModification);
                }
                if (querySetModification) {
                    this.webSocketManager.sendMessage(querySetModification);
                }
                for (const message of this.requestManager.resume()){
                    this.webSocketManager.sendMessage(message);
                }
            },
            onMessage: (serverMessage)=>{
                if (!this.firstMessageReceived) {
                    this.firstMessageReceived = true;
                    this.mark("convexFirstMessageReceived");
                    this.reportMarks();
                }
                switch(serverMessage.type){
                    case "Transition":
                        {
                            this.observedTimestamp(serverMessage.endVersion.ts);
                            this.authenticationManager.onTransition(serverMessage);
                            this.remoteQuerySet.transition(serverMessage);
                            this.state.transition(serverMessage);
                            const completedRequests = this.requestManager.removeCompleted(this.remoteQuerySet.timestamp());
                            this.notifyOnQueryResultChanges(completedRequests);
                            break;
                        }
                    case "MutationResponse":
                        {
                            if (serverMessage.success) {
                                this.observedTimestamp(serverMessage.ts);
                            }
                            const completedMutationInfo = this.requestManager.onResponse(serverMessage);
                            if (completedMutationInfo !== null) {
                                this.notifyOnQueryResultChanges(/* @__PURE__ */ new Map([
                                    [
                                        completedMutationInfo.requestId,
                                        completedMutationInfo.result
                                    ]
                                ]));
                            }
                            break;
                        }
                    case "ActionResponse":
                        {
                            this.requestManager.onResponse(serverMessage);
                            break;
                        }
                    case "AuthError":
                        {
                            this.authenticationManager.onAuthError(serverMessage);
                            break;
                        }
                    case "FatalError":
                        {
                            const error = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$logging$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["logFatalError"])(this.logger, serverMessage.error);
                            void this.webSocketManager.terminate();
                            throw error;
                        }
                    case "Ping":
                        break;
                    // do nothing
                    default:
                        {
                            serverMessage;
                        }
                }
                return {
                    hasSyncedPastLastReconnect: this.hasSyncedPastLastReconnect()
                };
            },
            onServerDisconnectError: options.onServerDisconnectError
        }, webSocketConstructor, this.logger, this.markConnectionStateDirty);
        this.mark("convexClientConstructed");
        if (options.expectAuth) {
            pauseSocket();
        }
    }
    /**
   * Return true if there is outstanding work from prior to the time of the most recent restart.
   * This indicates that the client has not proven itself to have gotten past the issue that
   * potentially led to the restart. Use this to influence when to reset backoff after a failure.
   */ hasSyncedPastLastReconnect() {
        const hasSyncedPastLastReconnect = this.requestManager.hasSyncedPastLastReconnect() || this.state.hasSyncedPastLastReconnect();
        return hasSyncedPastLastReconnect;
    }
    observedTimestamp(observedTs) {
        if (this.maxObservedTimestamp === void 0 || this.maxObservedTimestamp.lessThanOrEqual(observedTs)) {
            this.maxObservedTimestamp = observedTs;
        }
    }
    getMaxObservedTimestamp() {
        return this.maxObservedTimestamp;
    }
    /**
   * Compute the current query results based on the remoteQuerySet and the
   * current optimistic updates and call `onTransition` for all the changed
   * queries.
   *
   * @param completedMutations - A set of mutation IDs whose optimistic updates
   * are no longer needed.
   */ notifyOnQueryResultChanges(completedRequests) {
        const remoteQueryResults = this.remoteQuerySet.remoteQueryResults();
        const queryTokenToValue = /* @__PURE__ */ new Map();
        for (const [queryId, result] of remoteQueryResults){
            const queryToken = this.state.queryToken(queryId);
            if (queryToken !== null) {
                const query = {
                    result,
                    udfPath: this.state.queryPath(queryId),
                    args: this.state.queryArgs(queryId)
                };
                queryTokenToValue.set(queryToken, query);
            }
        }
        const changedQueryTokens = this.optimisticQueryResults.ingestQueryResultsFromServer(queryTokenToValue, new Set(completedRequests.keys()));
        this.handleTransition({
            queries: changedQueryTokens.map((token)=>{
                const optimisticResult = this.optimisticQueryResults.rawQueryResult(token);
                return {
                    token,
                    modification: {
                        kind: "Updated",
                        result: optimisticResult.result
                    }
                };
            }),
            reflectedMutations: Array.from(completedRequests).map(([requestId, result])=>({
                    requestId,
                    result
                })),
            timestamp: this.remoteQuerySet.timestamp()
        });
    }
    handleTransition(transition) {
        for (const fn of this._onTransitionFns.values()){
            fn(transition);
        }
    }
    /**
   * Add a handler that will be called on a transition.
   *
   * Any external side effects (e.g. setting React state) should be handled here.
   *
   * @param fn
   *
   * @returns
   */ addOnTransitionHandler(fn) {
        const id = this._transitionHandlerCounter++;
        this._onTransitionFns.set(id, fn);
        return ()=>this._onTransitionFns.delete(id);
    }
    /**
   * Set the authentication token to be used for subsequent queries and mutations.
   * `fetchToken` will be called automatically again if a token expires.
   * `fetchToken` should return `null` if the token cannot be retrieved, for example
   * when the user's rights were permanently revoked.
   * @param fetchToken - an async function returning the JWT-encoded OpenID Connect Identity Token
   * @param onChange - a callback that will be called when the authentication status changes
   */ setAuth(fetchToken, onChange) {
        void this.authenticationManager.setConfig(fetchToken, onChange);
    }
    hasAuth() {
        return this.state.hasAuth();
    }
    /** @internal */ setAdminAuth(value, fakeUserIdentity) {
        const message = this.state.setAdminAuth(value, fakeUserIdentity);
        this.webSocketManager.sendMessage(message);
    }
    clearAuth() {
        const message = this.state.clearAuth();
        this.webSocketManager.sendMessage(message);
    }
    /**
     * Subscribe to a query function.
     *
     * Whenever this query's result changes, the `onTransition` callback
     * passed into the constructor will be called.
     *
     * @param name - The name of the query.
     * @param args - An arguments object for the query. If this is omitted, the
     * arguments will be `{}`.
     * @param options - A {@link SubscribeOptions} options object for this query.
  
     * @returns An object containing a {@link QueryToken} corresponding to this
     * query and an `unsubscribe` callback.
     */ subscribe(name, args, options) {
        const argsObject = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$common$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["parseArgs"])(args);
        const { modification, queryToken, unsubscribe } = this.state.subscribe(name, argsObject, options?.journal, options?.componentPath);
        if (modification !== null) {
            this.webSocketManager.sendMessage(modification);
        }
        return {
            queryToken,
            unsubscribe: ()=>{
                const modification2 = unsubscribe();
                if (modification2) {
                    this.webSocketManager.sendMessage(modification2);
                }
            }
        };
    }
    /**
   * A query result based only on the current, local state.
   *
   * The only way this will return a value is if we're already subscribed to the
   * query or its value has been set optimistically.
   */ localQueryResult(udfPath, args) {
        const argsObject = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$common$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["parseArgs"])(args);
        const queryToken = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$udf_path_utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["serializePathAndArgs"])(udfPath, argsObject);
        return this.optimisticQueryResults.queryResult(queryToken);
    }
    /**
   * Get query result by query token based on current, local state
   *
   * The only way this will return a value is if we're already subscribed to the
   * query or its value has been set optimistically.
   *
   * @internal
   */ localQueryResultByToken(queryToken) {
        return this.optimisticQueryResults.queryResult(queryToken);
    }
    /**
   * Whether local query result is available for a toke.
   *
   * This method does not throw if the result is an error.
   *
   * @internal
   */ hasLocalQueryResultByToken(queryToken) {
        return this.optimisticQueryResults.hasQueryResult(queryToken);
    }
    /**
   * @internal
   */ localQueryLogs(udfPath, args) {
        const argsObject = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$common$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["parseArgs"])(args);
        const queryToken = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$udf_path_utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["serializePathAndArgs"])(udfPath, argsObject);
        return this.optimisticQueryResults.queryLogs(queryToken);
    }
    /**
   * Retrieve the current {@link QueryJournal} for this query function.
   *
   * If we have not yet received a result for this query, this will be `undefined`.
   *
   * @param name - The name of the query.
   * @param args - The arguments object for this query.
   * @returns The query's {@link QueryJournal} or `undefined`.
   */ queryJournal(name, args) {
        const argsObject = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$common$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["parseArgs"])(args);
        const queryToken = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$udf_path_utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["serializePathAndArgs"])(name, argsObject);
        return this.state.queryJournal(queryToken);
    }
    /**
   * Get the current {@link ConnectionState} between the client and the Convex
   * backend.
   *
   * @returns The {@link ConnectionState} with the Convex backend.
   */ connectionState() {
        const wsConnectionState = this.webSocketManager.connectionState();
        return {
            hasInflightRequests: this.requestManager.hasInflightRequests(),
            isWebSocketConnected: wsConnectionState.isConnected,
            hasEverConnected: wsConnectionState.hasEverConnected,
            connectionCount: wsConnectionState.connectionCount,
            connectionRetries: wsConnectionState.connectionRetries,
            timeOfOldestInflightRequest: this.requestManager.timeOfOldestInflightRequest(),
            inflightMutations: this.requestManager.inflightMutations(),
            inflightActions: this.requestManager.inflightActions()
        };
    }
    /**
   * Subscribe to the {@link ConnectionState} between the client and the Convex
   * backend, calling a callback each time it changes.
   *
   * Subscribed callbacks will be called when any part of ConnectionState changes.
   * ConnectionState may grow in future versions (e.g. to provide a array of
   * inflight requests) in which case callbacks would be called more frequently.
   *
   * @returns An unsubscribe function to stop listening.
   */ subscribeToConnectionState(cb) {
        const id = this.nextConnectionStateSubscriberId++;
        this.connectionStateSubscribers.set(id, cb);
        return ()=>{
            this.connectionStateSubscribers.delete(id);
        };
    }
    /**
     * Execute a mutation function.
     *
     * @param name - The name of the mutation.
     * @param args - An arguments object for the mutation. If this is omitted,
     * the arguments will be `{}`.
     * @param options - A {@link MutationOptions} options object for this mutation.
  
     * @returns - A promise of the mutation's result.
     */ async mutation(name, args, options) {
        const result = await this.mutationInternal(name, args, options);
        if (!result.success) {
            if (result.errorData !== void 0) {
                throw (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$logging$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardData"])(result, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$errors$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ConvexError"]((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$logging$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createHybridErrorStacktrace"])("mutation", name, result)));
            }
            throw new Error((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$logging$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createHybridErrorStacktrace"])("mutation", name, result));
        }
        return result.value;
    }
    /**
   * @internal
   */ async mutationInternal(udfPath, args, options, componentPath) {
        const { mutationPromise } = this.enqueueMutation(udfPath, args, options, componentPath);
        return mutationPromise;
    }
    /**
   * @internal
   */ enqueueMutation(udfPath, args, options, componentPath) {
        const mutationArgs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$common$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["parseArgs"])(args);
        this.tryReportLongDisconnect();
        const requestId = this.nextRequestId;
        this._nextRequestId++;
        if (options !== void 0) {
            const optimisticUpdate = options.optimisticUpdate;
            if (optimisticUpdate !== void 0) {
                const wrappedUpdate = (localQueryStore)=>{
                    const result = optimisticUpdate(localQueryStore, mutationArgs);
                    if (result instanceof Promise) {
                        this.logger.warn("Optimistic update handler returned a Promise. Optimistic updates should be synchronous.");
                    }
                };
                const changedQueryTokens = this.optimisticQueryResults.applyOptimisticUpdate(wrappedUpdate, requestId);
                const changedQueries = changedQueryTokens.map((token)=>{
                    const localResult = this.localQueryResultByToken(token);
                    return {
                        token,
                        modification: {
                            kind: "Updated",
                            result: localResult === void 0 ? void 0 : {
                                success: true,
                                value: localResult,
                                logLines: []
                            }
                        }
                    };
                });
                this.handleTransition({
                    queries: changedQueries,
                    reflectedMutations: [],
                    timestamp: this.remoteQuerySet.timestamp()
                });
            }
        }
        const message = {
            type: "Mutation",
            requestId,
            udfPath,
            componentPath,
            args: [
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$value$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["convexToJson"])(mutationArgs)
            ]
        };
        const mightBeSent = this.webSocketManager.sendMessage(message);
        const mutationPromise = this.requestManager.request(message, mightBeSent);
        return {
            requestId,
            mutationPromise
        };
    }
    /**
   * Execute an action function.
   *
   * @param name - The name of the action.
   * @param args - An arguments object for the action. If this is omitted,
   * the arguments will be `{}`.
   * @returns A promise of the action's result.
   */ async action(name, args) {
        const result = await this.actionInternal(name, args);
        if (!result.success) {
            if (result.errorData !== void 0) {
                throw (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$logging$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardData"])(result, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$errors$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ConvexError"]((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$logging$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createHybridErrorStacktrace"])("action", name, result)));
            }
            throw new Error((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$logging$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createHybridErrorStacktrace"])("action", name, result));
        }
        return result.value;
    }
    /**
   * @internal
   */ async actionInternal(udfPath, args, componentPath) {
        const actionArgs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$common$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["parseArgs"])(args);
        const requestId = this.nextRequestId;
        this._nextRequestId++;
        this.tryReportLongDisconnect();
        const message = {
            type: "Action",
            requestId,
            udfPath,
            componentPath,
            args: [
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$value$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["convexToJson"])(actionArgs)
            ]
        };
        const mightBeSent = this.webSocketManager.sendMessage(message);
        return this.requestManager.request(message, mightBeSent);
    }
    /**
   * Close any network handles associated with this client and stop all subscriptions.
   *
   * Call this method when you're done with an {@link BaseConvexClient} to
   * dispose of its sockets and resources.
   *
   * @returns A `Promise` fulfilled when the connection has been completely closed.
   */ async close() {
        this.authenticationManager.stop();
        return this.webSocketManager.terminate();
    }
    /**
   * Return the address for this client, useful for creating a new client.
   *
   * Not guaranteed to match the address with which this client was constructed:
   * it may be canonicalized.
   */ get url() {
        return this.address;
    }
    /**
   * @internal
   */ get nextRequestId() {
        return this._nextRequestId;
    }
    /**
   * @internal
   */ get sessionId() {
        return this._sessionId;
    }
    /**
   * Reports performance marks to the server. This should only be called when
   * we have a functional websocket.
   */ reportMarks() {
        if (this.debug) {
            const report = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$metrics$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getMarksReport"])(this.sessionId);
            this.webSocketManager.sendMessage({
                type: "Event",
                eventType: "ClientConnect",
                event: report
            });
        }
    }
    tryReportLongDisconnect() {
        if (!this.debug) {
            return;
        }
        const timeOfOldestRequest = this.connectionState().timeOfOldestInflightRequest;
        if (timeOfOldestRequest === null || Date.now() - timeOfOldestRequest.getTime() <= 60 * 1e3) {
            return;
        }
        const endpoint = `${this.address}/api/debug_event`;
        fetch(endpoint, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                "Convex-Client": `npm-${__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["version"]}`
            },
            body: JSON.stringify({
                event: "LongWebsocketDisconnect"
            })
        }).then((response)=>{
            if (!response.ok) {
                this.logger.warn("Analytics request failed with response:", response.body);
            }
        }).catch((error)=>{
            this.logger.warn("Analytics response failed with error:", error);
        });
    }
} //# sourceMappingURL=client.js.map
}),
"[project]/node_modules/convex/dist/esm/browser/http_client.js [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "ConvexHttpClient",
    ()=>ConvexHttpClient,
    "STATUS_CODE_BAD_REQUEST",
    ()=>STATUS_CODE_BAD_REQUEST,
    "STATUS_CODE_OK",
    ()=>STATUS_CODE_OK,
    "STATUS_CODE_UDF_FAILED",
    ()=>STATUS_CODE_UDF_FAILED,
    "setFetch",
    ()=>setFetch
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$server$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/server/api.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$common$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/common/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/values/index.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$errors$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/values/errors.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$value$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/values/value.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$logging$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/browser/logging.js [app-ssr] (ecmascript)");
"use strict";
var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value)=>key in obj ? __defProp(obj, key, {
        enumerable: true,
        configurable: true,
        writable: true,
        value
    }) : obj[key] = value;
var __publicField = (obj, key, value)=>__defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
;
;
;
;
;
const STATUS_CODE_OK = 200;
const STATUS_CODE_BAD_REQUEST = 400;
const STATUS_CODE_UDF_FAILED = 560;
let specifiedFetch = void 0;
function setFetch(f) {
    specifiedFetch = f;
}
class ConvexHttpClient {
    /**
   * Create a new {@link ConvexHttpClient}.
   *
   * @param address - The url of your Convex deployment, often provided
   * by an environment variable. E.g. `https://small-mouse-123.convex.cloud`.
   * @param options - An object of options.
   * - `skipConvexDeploymentUrlCheck` - Skip validating that the Convex deployment URL looks like
   * `https://happy-animal-123.convex.cloud` or localhost. This can be useful if running a self-hosted
   * Convex backend that uses a different URL.
   * - `logger` - A logger or a boolean. If not provided, logs to the console.
   * You can construct your own logger to customize logging to log elsewhere
   * or not log at all, or use `false` as a shorthand for a no-op logger.
   * A logger is an object with 4 methods: log(), warn(), error(), and logVerbose().
   * These methods can receive multiple arguments of any types, like console.log().
   * - `auth` - A JWT containing identity claims accessible in Convex functions.
   * This identity may expire so it may be necessary to call `setAuth()` later,
   * but for short-lived clients it's convenient to specify this value here.
   */ constructor(address, options){
        __publicField(this, "address");
        __publicField(this, "auth");
        __publicField(this, "adminAuth");
        __publicField(this, "encodedTsPromise");
        __publicField(this, "debug");
        __publicField(this, "fetchOptions");
        __publicField(this, "logger");
        __publicField(this, "mutationQueue", []);
        __publicField(this, "isProcessingQueue", false);
        if (typeof options === "boolean") {
            throw new Error("skipConvexDeploymentUrlCheck as the second argument is no longer supported. Please pass an options object, `{ skipConvexDeploymentUrlCheck: true }`.");
        }
        const opts = options ?? {};
        if (opts.skipConvexDeploymentUrlCheck !== true) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$common$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["validateDeploymentUrl"])(address);
        }
        this.logger = options?.logger === false ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$logging$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["instantiateNoopLogger"])({
            verbose: false
        }) : options?.logger !== true && options?.logger ? options.logger : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$logging$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["instantiateDefaultLogger"])({
            verbose: false
        });
        this.address = address;
        this.debug = true;
        if (options?.auth) {
            this.setAuth(options.auth);
        }
    }
    /**
   * Obtain the {@link ConvexHttpClient}'s URL to its backend.
   * @deprecated Use url, which returns the url without /api at the end.
   *
   * @returns The URL to the Convex backend, including the client's API version.
   */ backendUrl() {
        return `${this.address}/api`;
    }
    /**
   * Return the address for this client, useful for creating a new client.
   *
   * Not guaranteed to match the address with which this client was constructed:
   * it may be canonicalized.
   */ get url() {
        return this.address;
    }
    /**
   * Set the authentication token to be used for subsequent queries and mutations.
   *
   * Should be called whenever the token changes (i.e. due to expiration and refresh).
   *
   * @param value - JWT-encoded OpenID Connect identity token.
   */ setAuth(value) {
        this.clearAuth();
        this.auth = value;
    }
    /**
   * Set admin auth token to allow calling internal queries, mutations, and actions
   * and acting as an identity.
   *
   * @internal
   */ setAdminAuth(token, actingAsIdentity) {
        this.clearAuth();
        if (actingAsIdentity !== void 0) {
            const bytes = new TextEncoder().encode(JSON.stringify(actingAsIdentity));
            const actingAsIdentityEncoded = btoa(String.fromCodePoint(...bytes));
            this.adminAuth = `${token}:${actingAsIdentityEncoded}`;
        } else {
            this.adminAuth = token;
        }
    }
    /**
   * Clear the current authentication token if set.
   */ clearAuth() {
        this.auth = void 0;
        this.adminAuth = void 0;
    }
    /**
   * Sets whether the result log lines should be printed on the console or not.
   *
   * @internal
   */ setDebug(debug) {
        this.debug = debug;
    }
    /**
   * Used to customize the fetch behavior in some runtimes.
   *
   * @internal
   */ setFetchOptions(fetchOptions) {
        this.fetchOptions = fetchOptions;
    }
    /**
   * This API is experimental: it may change or disappear.
   *
   * Execute a Convex query function at the same timestamp as every other
   * consistent query execution run by this HTTP client.
   *
   * This doesn't make sense for long-lived ConvexHttpClients as Convex
   * backends can read a limited amount into the past: beyond 30 seconds
   * in the past may not be available.
   *
   * Create a new client to use a consistent time.
   *
   * @param name - The name of the query.
   * @param args - The arguments object for the query. If this is omitted,
   * the arguments will be `{}`.
   * @returns A promise of the query's result.
   *
   * @deprecated This API is experimental: it may change or disappear.
   */ async consistentQuery(query, ...args) {
        const queryArgs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$common$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["parseArgs"])(args[0]);
        const timestampPromise = this.getTimestamp();
        return await this.queryInner(query, queryArgs, {
            timestampPromise
        });
    }
    async getTimestamp() {
        if (this.encodedTsPromise) {
            return this.encodedTsPromise;
        }
        return this.encodedTsPromise = this.getTimestampInner();
    }
    async getTimestampInner() {
        const localFetch = specifiedFetch || fetch;
        const headers = {
            "Content-Type": "application/json",
            "Convex-Client": `npm-${__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["version"]}`
        };
        const response = await localFetch(`${this.address}/api/query_ts`, {
            ...this.fetchOptions,
            method: "POST",
            headers
        });
        if (!response.ok) {
            throw new Error(await response.text());
        }
        const { ts } = await response.json();
        return ts;
    }
    /**
   * Execute a Convex query function.
   *
   * @param name - The name of the query.
   * @param args - The arguments object for the query. If this is omitted,
   * the arguments will be `{}`.
   * @returns A promise of the query's result.
   */ async query(query, ...args) {
        const queryArgs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$common$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["parseArgs"])(args[0]);
        return await this.queryInner(query, queryArgs, {});
    }
    async queryInner(query, queryArgs, options) {
        const name = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$server$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getFunctionName"])(query);
        const args = [
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$value$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["convexToJson"])(queryArgs)
        ];
        const headers = {
            "Content-Type": "application/json",
            "Convex-Client": `npm-${__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["version"]}`
        };
        if (this.adminAuth) {
            headers["Authorization"] = `Convex ${this.adminAuth}`;
        } else if (this.auth) {
            headers["Authorization"] = `Bearer ${this.auth}`;
        }
        const localFetch = specifiedFetch || fetch;
        const timestamp = options.timestampPromise ? await options.timestampPromise : void 0;
        const body = JSON.stringify({
            path: name,
            format: "convex_encoded_json",
            args,
            ...timestamp ? {
                ts: timestamp
            } : {}
        });
        const endpoint = timestamp ? `${this.address}/api/query_at_ts` : `${this.address}/api/query`;
        const response = await localFetch(endpoint, {
            ...this.fetchOptions,
            body,
            method: "POST",
            headers
        });
        if (!response.ok && response.status !== STATUS_CODE_UDF_FAILED) {
            throw new Error(await response.text());
        }
        const respJSON = await response.json();
        if (this.debug) {
            for (const line of respJSON.logLines ?? []){
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$logging$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["logForFunction"])(this.logger, "info", "query", name, line);
            }
        }
        switch(respJSON.status){
            case "success":
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$value$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsonToConvex"])(respJSON.value);
            case "error":
                if (respJSON.errorData !== void 0) {
                    throw forwardErrorData(respJSON.errorData, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$errors$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ConvexError"](respJSON.errorMessage));
                }
                throw new Error(respJSON.errorMessage);
            default:
                throw new Error(`Invalid response: ${JSON.stringify(respJSON)}`);
        }
    }
    async mutationInner(mutation, mutationArgs) {
        const name = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$server$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getFunctionName"])(mutation);
        const body = JSON.stringify({
            path: name,
            format: "convex_encoded_json",
            args: [
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$value$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["convexToJson"])(mutationArgs)
            ]
        });
        const headers = {
            "Content-Type": "application/json",
            "Convex-Client": `npm-${__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["version"]}`
        };
        if (this.adminAuth) {
            headers["Authorization"] = `Convex ${this.adminAuth}`;
        } else if (this.auth) {
            headers["Authorization"] = `Bearer ${this.auth}`;
        }
        const localFetch = specifiedFetch || fetch;
        const response = await localFetch(`${this.address}/api/mutation`, {
            ...this.fetchOptions,
            body,
            method: "POST",
            headers
        });
        if (!response.ok && response.status !== STATUS_CODE_UDF_FAILED) {
            throw new Error(await response.text());
        }
        const respJSON = await response.json();
        if (this.debug) {
            for (const line of respJSON.logLines ?? []){
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$logging$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["logForFunction"])(this.logger, "info", "mutation", name, line);
            }
        }
        switch(respJSON.status){
            case "success":
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$value$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsonToConvex"])(respJSON.value);
            case "error":
                if (respJSON.errorData !== void 0) {
                    throw forwardErrorData(respJSON.errorData, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$errors$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ConvexError"](respJSON.errorMessage));
                }
                throw new Error(respJSON.errorMessage);
            default:
                throw new Error(`Invalid response: ${JSON.stringify(respJSON)}`);
        }
    }
    async processMutationQueue() {
        if (this.isProcessingQueue) {
            return;
        }
        this.isProcessingQueue = true;
        while(this.mutationQueue.length > 0){
            const { mutation, args, resolve, reject } = this.mutationQueue.shift();
            try {
                const result = await this.mutationInner(mutation, args);
                resolve(result);
            } catch (error) {
                reject(error);
            }
        }
        this.isProcessingQueue = false;
    }
    enqueueMutation(mutation, args) {
        return new Promise((resolve, reject)=>{
            this.mutationQueue.push({
                mutation,
                args,
                resolve,
                reject
            });
            void this.processMutationQueue();
        });
    }
    /**
   * Execute a Convex mutation function. Mutations are queued by default.
   *
   * @param name - The name of the mutation.
   * @param args - The arguments object for the mutation. If this is omitted,
   * the arguments will be `{}`.
   * @param options - An optional object containing
   * @returns A promise of the mutation's result.
   */ async mutation(mutation, ...args) {
        const [fnArgs, options] = args;
        const mutationArgs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$common$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["parseArgs"])(fnArgs);
        const queued = !options?.skipQueue;
        if (queued) {
            return await this.enqueueMutation(mutation, mutationArgs);
        } else {
            return await this.mutationInner(mutation, mutationArgs);
        }
    }
    /**
   * Execute a Convex action function. Actions are not queued.
   *
   * @param name - The name of the action.
   * @param args - The arguments object for the action. If this is omitted,
   * the arguments will be `{}`.
   * @returns A promise of the action's result.
   */ async action(action, ...args) {
        const actionArgs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$common$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["parseArgs"])(args[0]);
        const name = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$server$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getFunctionName"])(action);
        const body = JSON.stringify({
            path: name,
            format: "convex_encoded_json",
            args: [
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$value$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["convexToJson"])(actionArgs)
            ]
        });
        const headers = {
            "Content-Type": "application/json",
            "Convex-Client": `npm-${__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["version"]}`
        };
        if (this.adminAuth) {
            headers["Authorization"] = `Convex ${this.adminAuth}`;
        } else if (this.auth) {
            headers["Authorization"] = `Bearer ${this.auth}`;
        }
        const localFetch = specifiedFetch || fetch;
        const response = await localFetch(`${this.address}/api/action`, {
            ...this.fetchOptions,
            body,
            method: "POST",
            headers
        });
        if (!response.ok && response.status !== STATUS_CODE_UDF_FAILED) {
            throw new Error(await response.text());
        }
        const respJSON = await response.json();
        if (this.debug) {
            for (const line of respJSON.logLines ?? []){
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$logging$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["logForFunction"])(this.logger, "info", "action", name, line);
            }
        }
        switch(respJSON.status){
            case "success":
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$value$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsonToConvex"])(respJSON.value);
            case "error":
                if (respJSON.errorData !== void 0) {
                    throw forwardErrorData(respJSON.errorData, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$errors$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ConvexError"](respJSON.errorMessage));
                }
                throw new Error(respJSON.errorMessage);
            default:
                throw new Error(`Invalid response: ${JSON.stringify(respJSON)}`);
        }
    }
    /**
   * Execute a Convex function of an unknown type. These function calls are not queued.
   *
   * @param name - The name of the function.
   * @param args - The arguments object for the function. If this is omitted,
   * the arguments will be `{}`.
   * @returns A promise of the function's result.
   *
   * @internal
   */ async function(anyFunction, componentPath, ...args) {
        const functionArgs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$common$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["parseArgs"])(args[0]);
        const name = typeof anyFunction === "string" ? anyFunction : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$server$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getFunctionName"])(anyFunction);
        const body = JSON.stringify({
            componentPath,
            path: name,
            format: "convex_encoded_json",
            args: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$value$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["convexToJson"])(functionArgs)
        });
        const headers = {
            "Content-Type": "application/json",
            "Convex-Client": `npm-${__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["version"]}`
        };
        if (this.adminAuth) {
            headers["Authorization"] = `Convex ${this.adminAuth}`;
        } else if (this.auth) {
            headers["Authorization"] = `Bearer ${this.auth}`;
        }
        const localFetch = specifiedFetch || fetch;
        const response = await localFetch(`${this.address}/api/function`, {
            ...this.fetchOptions,
            body,
            method: "POST",
            headers
        });
        if (!response.ok && response.status !== STATUS_CODE_UDF_FAILED) {
            throw new Error(await response.text());
        }
        const respJSON = await response.json();
        if (this.debug) {
            for (const line of respJSON.logLines ?? []){
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$logging$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["logForFunction"])(this.logger, "info", "any", name, line);
            }
        }
        switch(respJSON.status){
            case "success":
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$value$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsonToConvex"])(respJSON.value);
            case "error":
                if (respJSON.errorData !== void 0) {
                    throw forwardErrorData(respJSON.errorData, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$errors$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ConvexError"](respJSON.errorMessage));
                }
                throw new Error(respJSON.errorMessage);
            default:
                throw new Error(`Invalid response: ${JSON.stringify(respJSON)}`);
        }
    }
}
function forwardErrorData(errorData, error) {
    error.data = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$value$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsonToConvex"])(errorData);
    return error;
} //# sourceMappingURL=http_client.js.map
}),
"[project]/node_modules/convex/dist/esm/browser/index.js [app-ssr] (ecmascript) <locals>", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$client$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/browser/sync/client.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$simple_client$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/browser/simple_client.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$http_client$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/browser/http_client.js [app-ssr] (ecmascript)"); //# sourceMappingURL=index.js.map
"use strict";
;
;
;
}),
"[project]/node_modules/convex/dist/esm/browser/simple_client.js [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "ConvexClient",
    ()=>ConvexClient,
    "setDefaultWebSocketConstructor",
    ()=>setDefaultWebSocketConstructor
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$common$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/common/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/browser/index.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$client$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/browser/sync/client.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$server$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/server/api.js [app-ssr] (ecmascript)");
"use strict";
var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value)=>key in obj ? __defProp(obj, key, {
        enumerable: true,
        configurable: true,
        writable: true,
        value
    }) : obj[key] = value;
var __publicField = (obj, key, value)=>__defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
;
;
;
let defaultWebSocketConstructor;
function setDefaultWebSocketConstructor(ws) {
    defaultWebSocketConstructor = ws;
}
class ConvexClient {
    /**
   * Construct a client and immediately initiate a WebSocket connection to the passed address.
   *
   * @public
   */ constructor(address, options = {}){
        __publicField(this, "listeners");
        __publicField(this, "_client");
        // A synthetic server event to run callbacks the first time
        __publicField(this, "callNewListenersWithCurrentValuesTimer");
        __publicField(this, "_closed");
        __publicField(this, "_disabled");
        if (options.skipConvexDeploymentUrlCheck !== true) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$common$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["validateDeploymentUrl"])(address);
        }
        const { disabled, ...baseOptions } = options;
        this._closed = false;
        this._disabled = !!disabled;
        if (defaultWebSocketConstructor && !("webSocketConstructor" in baseOptions) && typeof WebSocket === "undefined") {
            baseOptions.webSocketConstructor = defaultWebSocketConstructor;
        }
        if ("undefined" === "undefined" && !("unsavedChangesWarning" in baseOptions)) {
            baseOptions.unsavedChangesWarning = false;
        }
        if (!this.disabled) {
            this._client = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$client$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseConvexClient"](address, (updatedQueries)=>this._transition(updatedQueries), baseOptions);
        }
        this.listeners = /* @__PURE__ */ new Set();
    }
    /**
   * Once closed no registered callbacks will fire again.
   */ get closed() {
        return this._closed;
    }
    get client() {
        if (this._client) return this._client;
        throw new Error("ConvexClient is disabled");
    }
    get disabled() {
        return this._disabled;
    }
    /**
   * Call a callback whenever a new result for a query is received. The callback
   * will run soon after being registered if a result for the query is already
   * in memory.
   *
   * The return value is an {@link Unsubscribe} object which is both a function
   * an an object with properties. Both of the patterns below work with this object:
   *
   *```ts
   * // call the return value as a function
   * const unsubscribe = client.onUpdate(api.messages.list, {}, (messages) => {
   *   console.log(messages);
   * });
   * unsubscribe();
   *
   * // unpack the return value into its properties
   * const {
   *   getCurrentValue,
   *   unsubscribe,
   * } = client.onUpdate(api.messages.list, {}, (messages) => {
   *   console.log(messages);
   * });
   *```
   *
   * @param query - A {@link server.FunctionReference} for the public query to run.
   * @param args - The arguments to run the query with.
   * @param callback - Function to call when the query result updates.
   * @param onError - Function to call when the query result updates with an error.
   * If not provided, errors will be thrown instead of calling the callback.
   *
   * @return an {@link Unsubscribe} function to stop calling the onUpdate function.
   */ onUpdate(query, args, callback, onError) {
        if (this.disabled) {
            const disabledUnsubscribe = ()=>{};
            const unsubscribeProps2 = {
                unsubscribe: disabledUnsubscribe,
                getCurrentValue: ()=>void 0,
                getQueryLogs: ()=>void 0
            };
            Object.assign(disabledUnsubscribe, unsubscribeProps2);
            return disabledUnsubscribe;
        }
        const { queryToken, unsubscribe } = this.client.subscribe((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$server$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getFunctionName"])(query), args);
        const queryInfo = {
            queryToken,
            callback,
            onError,
            unsubscribe,
            hasEverRun: false,
            query,
            args
        };
        this.listeners.add(queryInfo);
        if (this.queryResultReady(queryToken) && this.callNewListenersWithCurrentValuesTimer === void 0) {
            this.callNewListenersWithCurrentValuesTimer = setTimeout(()=>this.callNewListenersWithCurrentValues(), 0);
        }
        const unsubscribeProps = {
            unsubscribe: ()=>{
                if (this.closed) {
                    return;
                }
                this.listeners.delete(queryInfo);
                unsubscribe();
            },
            getCurrentValue: ()=>this.client.localQueryResultByToken(queryToken),
            getQueryLogs: ()=>this.client.localQueryLogs(queryToken)
        };
        const ret = unsubscribeProps.unsubscribe;
        Object.assign(ret, unsubscribeProps);
        return ret;
    }
    // Run all callbacks that have never been run before if they have a query
    // result available now.
    callNewListenersWithCurrentValues() {
        this.callNewListenersWithCurrentValuesTimer = void 0;
        this._transition([], true);
    }
    queryResultReady(queryToken) {
        return this.client.hasLocalQueryResultByToken(queryToken);
    }
    async close() {
        if (this.disabled) return;
        this.listeners.clear();
        this._closed = true;
        return this.client.close();
    }
    /**
   * Set the authentication token to be used for subsequent queries and mutations.
   * `fetchToken` will be called automatically again if a token expires.
   * `fetchToken` should return `null` if the token cannot be retrieved, for example
   * when the user's rights were permanently revoked.
   * @param fetchToken - an async function returning the JWT (typically an OpenID Connect Identity Token)
   * @param onChange - a callback that will be called when the authentication status changes
   */ setAuth(fetchToken, onChange) {
        if (this.disabled) return;
        this.client.setAuth(fetchToken, onChange ?? (()=>{}));
    }
    /**
   * @internal
   */ setAdminAuth(token, identity) {
        if (this.closed) {
            throw new Error("ConvexClient has already been closed.");
        }
        if (this.disabled) return;
        this.client.setAdminAuth(token, identity);
    }
    /**
   * @internal
   */ _transition(updatedQueries, callNewListeners = false) {
        for (const queryInfo of this.listeners){
            const { callback, queryToken, onError, hasEverRun } = queryInfo;
            if (updatedQueries.includes(queryToken) || callNewListeners && !hasEverRun && this.client.hasLocalQueryResultByToken(queryToken)) {
                queryInfo.hasEverRun = true;
                let newValue;
                try {
                    newValue = this.client.localQueryResultByToken(queryToken);
                } catch (error) {
                    if (!(error instanceof Error)) throw error;
                    if (onError) {
                        onError(error, "Second argument to onUpdate onError is reserved for later use");
                    } else {
                        void Promise.reject(error);
                    }
                    continue;
                }
                callback(newValue, "Second argument to onUpdate callback is reserved for later use");
            }
        }
    }
    /**
   * Execute a mutation function.
   *
   * @param mutation - A {@link server.FunctionReference} for the public mutation
   * to run.
   * @param args - An arguments object for the mutation.
   * @param options - A {@link MutationOptions} options object for the mutation.
   * @returns A promise of the mutation's result.
   */ async mutation(mutation, args, options) {
        if (this.disabled) throw new Error("ConvexClient is disabled");
        return await this.client.mutation((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$server$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getFunctionName"])(mutation), args, options);
    }
    /**
   * Execute an action function.
   *
   * @param action - A {@link server.FunctionReference} for the public action
   * to run.
   * @param args - An arguments object for the action.
   * @returns A promise of the action's result.
   */ async action(action, args) {
        if (this.disabled) throw new Error("ConvexClient is disabled");
        return await this.client.action((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$server$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getFunctionName"])(action), args);
    }
    /**
   * Fetch a query result once.
   *
   * @param query - A {@link server.FunctionReference} for the public query
   * to run.
   * @param args - An arguments object for the query.
   * @returns A promise of the query's result.
   */ async query(query, args) {
        if (this.disabled) throw new Error("ConvexClient is disabled");
        const value = this.client.localQueryResult((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$server$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getFunctionName"])(query), args);
        if (value !== void 0) return Promise.resolve(value);
        return new Promise((resolve, reject)=>{
            const { unsubscribe } = this.onUpdate(query, args, (value2)=>{
                unsubscribe();
                resolve(value2);
            }, (e)=>{
                unsubscribe();
                reject(e);
            });
        });
    }
    /**
   * Get the current {@link ConnectionState} between the client and the Convex
   * backend.
   *
   * @returns The {@link ConnectionState} with the Convex backend.
   */ connectionState() {
        if (this.disabled) throw new Error("ConvexClient is disabled");
        return this.client.connectionState();
    }
    /**
   * Subscribe to the {@link ConnectionState} between the client and the Convex
   * backend, calling a callback each time it changes.
   *
   * Subscribed callbacks will be called when any part of ConnectionState changes.
   * ConnectionState may grow in future versions (e.g. to provide a array of
   * inflight requests) in which case callbacks would be called more frequently.
   *
   * @returns An unsubscribe function to stop listening.
   */ subscribeToConnectionState(cb) {
        if (this.disabled) return ()=>{};
        return this.client.subscribeToConnectionState(cb);
    }
} //# sourceMappingURL=simple_client.js.map
}),
"[project]/node_modules/convex/dist/esm/browser/simple_client-node.js [app-ssr] (ecmascript) <locals>", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([]);
var __TURBOPACK__imported__module__$5b$externals$5d2f$module__$5b$external$5d$__$28$module$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/module [external] (module, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/path [external] (path, cjs)");
// src/browser/simple_client-node.ts
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$simple_client$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/browser/simple_client.js [app-ssr] (ecmascript)");
;
;
const require = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$module__$5b$external$5d$__$28$module$2c$__cjs$29$__["createRequire"])((0, __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["resolve"])('.'));
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __require = /* @__PURE__ */ ((x)=>typeof require !== "undefined" ? require : typeof Proxy !== "undefined" ? new Proxy(x, {
        get: (a, b)=>(typeof require !== "undefined" ? require : a)[b]
    }) : x)(function(x) {
    if (typeof require !== "undefined") return require.apply(this, arguments);
    throw Error('Dynamic require of "' + x + '" is not supported');
});
var __commonJS = (cb, mod)=>function __require2() {
        return mod || (0, cb[__getOwnPropNames(cb)[0]])((mod = {
            exports: {}
        }).exports, mod), mod.exports;
    };
var __copyProps = (to, from, except, desc)=>{
    if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
            get: ()=>from[key],
            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
    }
    return to;
};
var __toESM = (mod, isNodeMode, target)=>(target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(// If the importer is in node compatibility mode or this is not an ESM
    // file that has been converted to a CommonJS file using a Babel-
    // compatible transform (i.e. "__esModule" has not been set), then set
    // "default" to the CommonJS "module.exports" for node compatibility.
    isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", {
        value: mod,
        enumerable: true
    }) : target, mod));
// ../common/temp/node_modules/.pnpm/ws@8.18.0_bufferutil@4.0.9_utf-8-validate@5.0.10/node_modules/ws/lib/stream.js
var require_stream = __commonJS({
    "../common/temp/node_modules/.pnpm/ws@8.18.0_bufferutil@4.0.9_utf-8-validate@5.0.10/node_modules/ws/lib/stream.js" (exports, module) {
        "use strict";
        var { Duplex } = __require("stream");
        function emitClose(stream) {
            stream.emit("close");
        }
        function duplexOnEnd() {
            if (!this.destroyed && this._writableState.finished) {
                this.destroy();
            }
        }
        function duplexOnError(err) {
            this.removeListener("error", duplexOnError);
            this.destroy();
            if (this.listenerCount("error") === 0) {
                this.emit("error", err);
            }
        }
        function createWebSocketStream2(ws, options) {
            let terminateOnDestroy = true;
            const duplex = new Duplex({
                ...options,
                autoDestroy: false,
                emitClose: false,
                objectMode: false,
                writableObjectMode: false
            });
            ws.on("message", function message(msg, isBinary) {
                const data = !isBinary && duplex._readableState.objectMode ? msg.toString() : msg;
                if (!duplex.push(data)) ws.pause();
            });
            ws.once("error", function error(err) {
                if (duplex.destroyed) return;
                terminateOnDestroy = false;
                duplex.destroy(err);
            });
            ws.once("close", function close() {
                if (duplex.destroyed) return;
                duplex.push(null);
            });
            duplex._destroy = function(err, callback) {
                if (ws.readyState === ws.CLOSED) {
                    callback(err);
                    process.nextTick(emitClose, duplex);
                    return;
                }
                let called = false;
                ws.once("error", function error(err2) {
                    called = true;
                    callback(err2);
                });
                ws.once("close", function close() {
                    if (!called) callback(err);
                    process.nextTick(emitClose, duplex);
                });
                if (terminateOnDestroy) ws.terminate();
            };
            duplex._final = function(callback) {
                if (ws.readyState === ws.CONNECTING) {
                    ws.once("open", function open() {
                        duplex._final(callback);
                    });
                    return;
                }
                if (ws._socket === null) return;
                if (ws._socket._writableState.finished) {
                    callback();
                    if (duplex._readableState.endEmitted) duplex.destroy();
                } else {
                    ws._socket.once("finish", function finish() {
                        callback();
                    });
                    ws.close();
                }
            };
            duplex._read = function() {
                if (ws.isPaused) ws.resume();
            };
            duplex._write = function(chunk, encoding, callback) {
                if (ws.readyState === ws.CONNECTING) {
                    ws.once("open", function open() {
                        duplex._write(chunk, encoding, callback);
                    });
                    return;
                }
                ws.send(chunk, callback);
            };
            duplex.on("end", duplexOnEnd);
            duplex.on("error", duplexOnError);
            return duplex;
        }
        module.exports = createWebSocketStream2;
    }
});
// ../common/temp/node_modules/.pnpm/ws@8.18.0_bufferutil@4.0.9_utf-8-validate@5.0.10/node_modules/ws/lib/constants.js
var require_constants = __commonJS({
    "../common/temp/node_modules/.pnpm/ws@8.18.0_bufferutil@4.0.9_utf-8-validate@5.0.10/node_modules/ws/lib/constants.js" (exports, module) {
        "use strict";
        var BINARY_TYPES = [
            "nodebuffer",
            "arraybuffer",
            "fragments"
        ];
        var hasBlob = typeof Blob !== "undefined";
        if (hasBlob) BINARY_TYPES.push("blob");
        module.exports = {
            BINARY_TYPES,
            EMPTY_BUFFER: Buffer.alloc(0),
            GUID: "258EAFA5-E914-47DA-95CA-C5AB0DC85B11",
            hasBlob,
            kForOnEventAttribute: Symbol("kIsForOnEventAttribute"),
            kListener: Symbol("kListener"),
            kStatusCode: Symbol("status-code"),
            kWebSocket: Symbol("websocket"),
            NOOP: ()=>{}
        };
    }
});
// ../common/temp/node_modules/.pnpm/node-gyp-build@4.8.4/node_modules/node-gyp-build/node-gyp-build.js
var require_node_gyp_build = __commonJS({
    "../common/temp/node_modules/.pnpm/node-gyp-build@4.8.4/node_modules/node-gyp-build/node-gyp-build.js" (exports, module) {
        var fs = __require("fs");
        var path = __require("path");
        var os = __require("os");
        var runtimeRequire = typeof __webpack_require__ === "function" ? __non_webpack_require__ : __require;
        var vars = process.config && process.config.variables || {};
        var prebuildsOnly = !!process.env.PREBUILDS_ONLY;
        var abi = process.versions.modules;
        var runtime = isElectron() ? "electron" : isNwjs() ? "node-webkit" : "node";
        var arch = process.env.npm_config_arch || os.arch();
        var platform = process.env.npm_config_platform || os.platform();
        var libc = process.env.LIBC || (isAlpine(platform) ? "musl" : "glibc");
        var armv = process.env.ARM_VERSION || (arch === "arm64" ? "8" : vars.arm_version) || "";
        var uv = (process.versions.uv || "").split(".")[0];
        module.exports = load;
        function load(dir) {
            return runtimeRequire(load.resolve(dir));
        }
        load.resolve = load.path = function(dir) {
            dir = path.resolve(dir || ".");
            try {
                var name = runtimeRequire(path.join(dir, "package.json")).name.toUpperCase().replace(/-/g, "_");
                if (process.env[name + "_PREBUILD"]) dir = process.env[name + "_PREBUILD"];
            } catch (err) {}
            if (!prebuildsOnly) {
                var release = getFirst(path.join(dir, "build/Release"), matchBuild);
                if (release) return release;
                var debug = getFirst(path.join(dir, "build/Debug"), matchBuild);
                if (debug) return debug;
            }
            var prebuild = resolve(dir);
            if (prebuild) return prebuild;
            var nearby = resolve(path.dirname(process.execPath));
            if (nearby) return nearby;
            var target = [
                "platform=" + platform,
                "arch=" + arch,
                "runtime=" + runtime,
                "abi=" + abi,
                "uv=" + uv,
                armv ? "armv=" + armv : "",
                "libc=" + libc,
                "node=" + process.versions.node,
                process.versions.electron ? "electron=" + process.versions.electron : "",
                typeof __webpack_require__ === "function" ? "webpack=true" : ""
            ].filter(Boolean).join(" ");
            throw new Error("No native build was found for " + target + "\n    loaded from: " + dir + "\n");
            function resolve(dir2) {
                var tuples = readdirSync(path.join(dir2, "prebuilds")).map(parseTuple);
                var tuple = tuples.filter(matchTuple(platform, arch)).sort(compareTuples)[0];
                if (!tuple) return;
                var prebuilds = path.join(dir2, "prebuilds", tuple.name);
                var parsed = readdirSync(prebuilds).map(parseTags);
                var candidates = parsed.filter(matchTags(runtime, abi));
                var winner = candidates.sort(compareTags(runtime))[0];
                if (winner) return path.join(prebuilds, winner.file);
            }
        };
        function readdirSync(dir) {
            try {
                return fs.readdirSync(dir);
            } catch (err) {
                return [];
            }
        }
        function getFirst(dir, filter) {
            var files = readdirSync(dir).filter(filter);
            return files[0] && path.join(dir, files[0]);
        }
        function matchBuild(name) {
            return /\.node$/.test(name);
        }
        function parseTuple(name) {
            var arr = name.split("-");
            if (arr.length !== 2) return;
            var platform2 = arr[0];
            var architectures = arr[1].split("+");
            if (!platform2) return;
            if (!architectures.length) return;
            if (!architectures.every(Boolean)) return;
            return {
                name,
                platform: platform2,
                architectures
            };
        }
        function matchTuple(platform2, arch2) {
            return function(tuple) {
                if (tuple == null) return false;
                if (tuple.platform !== platform2) return false;
                return tuple.architectures.includes(arch2);
            };
        }
        function compareTuples(a, b) {
            return a.architectures.length - b.architectures.length;
        }
        function parseTags(file) {
            var arr = file.split(".");
            var extension = arr.pop();
            var tags = {
                file,
                specificity: 0
            };
            if (extension !== "node") return;
            for(var i = 0; i < arr.length; i++){
                var tag = arr[i];
                if (tag === "node" || tag === "electron" || tag === "node-webkit") {
                    tags.runtime = tag;
                } else if (tag === "napi") {
                    tags.napi = true;
                } else if (tag.slice(0, 3) === "abi") {
                    tags.abi = tag.slice(3);
                } else if (tag.slice(0, 2) === "uv") {
                    tags.uv = tag.slice(2);
                } else if (tag.slice(0, 4) === "armv") {
                    tags.armv = tag.slice(4);
                } else if (tag === "glibc" || tag === "musl") {
                    tags.libc = tag;
                } else {
                    continue;
                }
                tags.specificity++;
            }
            return tags;
        }
        function matchTags(runtime2, abi2) {
            return function(tags) {
                if (tags == null) return false;
                if (tags.runtime && tags.runtime !== runtime2 && !runtimeAgnostic(tags)) return false;
                if (tags.abi && tags.abi !== abi2 && !tags.napi) return false;
                if (tags.uv && tags.uv !== uv) return false;
                if (tags.armv && tags.armv !== armv) return false;
                if (tags.libc && tags.libc !== libc) return false;
                return true;
            };
        }
        function runtimeAgnostic(tags) {
            return tags.runtime === "node" && tags.napi;
        }
        function compareTags(runtime2) {
            return function(a, b) {
                if (a.runtime !== b.runtime) {
                    return a.runtime === runtime2 ? -1 : 1;
                } else if (a.abi !== b.abi) {
                    return a.abi ? -1 : 1;
                } else if (a.specificity !== b.specificity) {
                    return a.specificity > b.specificity ? -1 : 1;
                } else {
                    return 0;
                }
            };
        }
        function isNwjs() {
            return !!(process.versions && process.versions.nw);
        }
        function isElectron() {
            if (process.versions && process.versions.electron) return true;
            if (process.env.ELECTRON_RUN_AS_NODE) return true;
            return "undefined" !== "undefined" && window.process && window.process.type === "renderer";
        }
        function isAlpine(platform2) {
            return platform2 === "linux" && fs.existsSync("/etc/alpine-release");
        }
        load.parseTags = parseTags;
        load.matchTags = matchTags;
        load.compareTags = compareTags;
        load.parseTuple = parseTuple;
        load.matchTuple = matchTuple;
        load.compareTuples = compareTuples;
    }
});
// ../common/temp/node_modules/.pnpm/node-gyp-build@4.8.4/node_modules/node-gyp-build/index.js
var require_node_gyp_build2 = __commonJS({
    "../common/temp/node_modules/.pnpm/node-gyp-build@4.8.4/node_modules/node-gyp-build/index.js" (exports, module) {
        var runtimeRequire = typeof __webpack_require__ === "function" ? __non_webpack_require__ : __require;
        if (typeof runtimeRequire.addon === "function") {
            module.exports = runtimeRequire.addon.bind(runtimeRequire);
        } else {
            module.exports = require_node_gyp_build();
        }
    }
});
// ../common/temp/node_modules/.pnpm/bufferutil@4.0.9/node_modules/bufferutil/fallback.js
var require_fallback = __commonJS({
    "../common/temp/node_modules/.pnpm/bufferutil@4.0.9/node_modules/bufferutil/fallback.js" (exports, module) {
        "use strict";
        var mask = (source, mask2, output, offset, length)=>{
            for(var i = 0; i < length; i++){
                output[offset + i] = source[i] ^ mask2[i & 3];
            }
        };
        var unmask = (buffer, mask2)=>{
            const length = buffer.length;
            for(var i = 0; i < length; i++){
                buffer[i] ^= mask2[i & 3];
            }
        };
        module.exports = {
            mask,
            unmask
        };
    }
});
// ../common/temp/node_modules/.pnpm/bufferutil@4.0.9/node_modules/bufferutil/index.js
var require_bufferutil = __commonJS({
    "../common/temp/node_modules/.pnpm/bufferutil@4.0.9/node_modules/bufferutil/index.js" (exports, module) {
        "use strict";
        try {
            module.exports = require_node_gyp_build2()(("TURBOPACK compile-time value", "/ROOT/node_modules/convex/dist/esm/browser"));
        } catch (e) {
            module.exports = require_fallback();
        }
    }
});
// ../common/temp/node_modules/.pnpm/ws@8.18.0_bufferutil@4.0.9_utf-8-validate@5.0.10/node_modules/ws/lib/buffer-util.js
var require_buffer_util = __commonJS({
    "../common/temp/node_modules/.pnpm/ws@8.18.0_bufferutil@4.0.9_utf-8-validate@5.0.10/node_modules/ws/lib/buffer-util.js" (exports, module) {
        "use strict";
        var { EMPTY_BUFFER } = require_constants();
        var FastBuffer = Buffer[Symbol.species];
        function concat(list, totalLength) {
            if (list.length === 0) return EMPTY_BUFFER;
            if (list.length === 1) return list[0];
            const target = Buffer.allocUnsafe(totalLength);
            let offset = 0;
            for(let i = 0; i < list.length; i++){
                const buf = list[i];
                target.set(buf, offset);
                offset += buf.length;
            }
            if (offset < totalLength) {
                return new FastBuffer(target.buffer, target.byteOffset, offset);
            }
            return target;
        }
        function _mask(source, mask, output, offset, length) {
            for(let i = 0; i < length; i++){
                output[offset + i] = source[i] ^ mask[i & 3];
            }
        }
        function _unmask(buffer, mask) {
            for(let i = 0; i < buffer.length; i++){
                buffer[i] ^= mask[i & 3];
            }
        }
        function toArrayBuffer(buf) {
            if (buf.length === buf.buffer.byteLength) {
                return buf.buffer;
            }
            return buf.buffer.slice(buf.byteOffset, buf.byteOffset + buf.length);
        }
        function toBuffer(data) {
            toBuffer.readOnly = true;
            if (Buffer.isBuffer(data)) return data;
            let buf;
            if (data instanceof ArrayBuffer) {
                buf = new FastBuffer(data);
            } else if (ArrayBuffer.isView(data)) {
                buf = new FastBuffer(data.buffer, data.byteOffset, data.byteLength);
            } else {
                buf = Buffer.from(data);
                toBuffer.readOnly = false;
            }
            return buf;
        }
        module.exports = {
            concat,
            mask: _mask,
            toArrayBuffer,
            toBuffer,
            unmask: _unmask
        };
        if (!process.env.WS_NO_BUFFER_UTIL) {
            try {
                const bufferUtil = require_bufferutil();
                module.exports.mask = function(source, mask, output, offset, length) {
                    if (length < 48) _mask(source, mask, output, offset, length);
                    else bufferUtil.mask(source, mask, output, offset, length);
                };
                module.exports.unmask = function(buffer, mask) {
                    if (buffer.length < 32) _unmask(buffer, mask);
                    else bufferUtil.unmask(buffer, mask);
                };
            } catch (e) {}
        }
    }
});
// ../common/temp/node_modules/.pnpm/ws@8.18.0_bufferutil@4.0.9_utf-8-validate@5.0.10/node_modules/ws/lib/limiter.js
var require_limiter = __commonJS({
    "../common/temp/node_modules/.pnpm/ws@8.18.0_bufferutil@4.0.9_utf-8-validate@5.0.10/node_modules/ws/lib/limiter.js" (exports, module) {
        "use strict";
        var kDone = Symbol("kDone");
        var kRun = Symbol("kRun");
        var Limiter = class {
            /**
       * Creates a new `Limiter`.
       *
       * @param {Number} [concurrency=Infinity] The maximum number of jobs allowed
       *     to run concurrently
       */ constructor(concurrency){
                this[kDone] = ()=>{
                    this.pending--;
                    this[kRun]();
                };
                this.concurrency = concurrency || Infinity;
                this.jobs = [];
                this.pending = 0;
            }
            /**
       * Adds a job to the queue.
       *
       * @param {Function} job The job to run
       * @public
       */ add(job) {
                this.jobs.push(job);
                this[kRun]();
            }
            /**
       * Removes a job from the queue and runs it if possible.
       *
       * @private
       */ [kRun]() {
                if (this.pending === this.concurrency) return;
                if (this.jobs.length) {
                    const job = this.jobs.shift();
                    this.pending++;
                    job(this[kDone]);
                }
            }
        };
        module.exports = Limiter;
    }
});
// ../common/temp/node_modules/.pnpm/ws@8.18.0_bufferutil@4.0.9_utf-8-validate@5.0.10/node_modules/ws/lib/permessage-deflate.js
var require_permessage_deflate = __commonJS({
    "../common/temp/node_modules/.pnpm/ws@8.18.0_bufferutil@4.0.9_utf-8-validate@5.0.10/node_modules/ws/lib/permessage-deflate.js" (exports, module) {
        "use strict";
        var zlib = __require("zlib");
        var bufferUtil = require_buffer_util();
        var Limiter = require_limiter();
        var { kStatusCode } = require_constants();
        var FastBuffer = Buffer[Symbol.species];
        var TRAILER = Buffer.from([
            0,
            0,
            255,
            255
        ]);
        var kPerMessageDeflate = Symbol("permessage-deflate");
        var kTotalLength = Symbol("total-length");
        var kCallback = Symbol("callback");
        var kBuffers = Symbol("buffers");
        var kError = Symbol("error");
        var zlibLimiter;
        var PerMessageDeflate = class {
            /**
       * Creates a PerMessageDeflate instance.
       *
       * @param {Object} [options] Configuration options
       * @param {(Boolean|Number)} [options.clientMaxWindowBits] Advertise support
       *     for, or request, a custom client window size
       * @param {Boolean} [options.clientNoContextTakeover=false] Advertise/
       *     acknowledge disabling of client context takeover
       * @param {Number} [options.concurrencyLimit=10] The number of concurrent
       *     calls to zlib
       * @param {(Boolean|Number)} [options.serverMaxWindowBits] Request/confirm the
       *     use of a custom server window size
       * @param {Boolean} [options.serverNoContextTakeover=false] Request/accept
       *     disabling of server context takeover
       * @param {Number} [options.threshold=1024] Size (in bytes) below which
       *     messages should not be compressed if context takeover is disabled
       * @param {Object} [options.zlibDeflateOptions] Options to pass to zlib on
       *     deflate
       * @param {Object} [options.zlibInflateOptions] Options to pass to zlib on
       *     inflate
       * @param {Boolean} [isServer=false] Create the instance in either server or
       *     client mode
       * @param {Number} [maxPayload=0] The maximum allowed message length
       */ constructor(options, isServer, maxPayload){
                this._maxPayload = maxPayload | 0;
                this._options = options || {};
                this._threshold = this._options.threshold !== void 0 ? this._options.threshold : 1024;
                this._isServer = !!isServer;
                this._deflate = null;
                this._inflate = null;
                this.params = null;
                if (!zlibLimiter) {
                    const concurrency = this._options.concurrencyLimit !== void 0 ? this._options.concurrencyLimit : 10;
                    zlibLimiter = new Limiter(concurrency);
                }
            }
            /**
       * @type {String}
       */ static get extensionName() {
                return "permessage-deflate";
            }
            /**
       * Create an extension negotiation offer.
       *
       * @return {Object} Extension parameters
       * @public
       */ offer() {
                const params = {};
                if (this._options.serverNoContextTakeover) {
                    params.server_no_context_takeover = true;
                }
                if (this._options.clientNoContextTakeover) {
                    params.client_no_context_takeover = true;
                }
                if (this._options.serverMaxWindowBits) {
                    params.server_max_window_bits = this._options.serverMaxWindowBits;
                }
                if (this._options.clientMaxWindowBits) {
                    params.client_max_window_bits = this._options.clientMaxWindowBits;
                } else if (this._options.clientMaxWindowBits == null) {
                    params.client_max_window_bits = true;
                }
                return params;
            }
            /**
       * Accept an extension negotiation offer/response.
       *
       * @param {Array} configurations The extension negotiation offers/reponse
       * @return {Object} Accepted configuration
       * @public
       */ accept(configurations) {
                configurations = this.normalizeParams(configurations);
                this.params = this._isServer ? this.acceptAsServer(configurations) : this.acceptAsClient(configurations);
                return this.params;
            }
            /**
       * Releases all resources used by the extension.
       *
       * @public
       */ cleanup() {
                if (this._inflate) {
                    this._inflate.close();
                    this._inflate = null;
                }
                if (this._deflate) {
                    const callback = this._deflate[kCallback];
                    this._deflate.close();
                    this._deflate = null;
                    if (callback) {
                        callback(new Error("The deflate stream was closed while data was being processed"));
                    }
                }
            }
            /**
       *  Accept an extension negotiation offer.
       *
       * @param {Array} offers The extension negotiation offers
       * @return {Object} Accepted configuration
       * @private
       */ acceptAsServer(offers) {
                const opts = this._options;
                const accepted = offers.find((params)=>{
                    if (opts.serverNoContextTakeover === false && params.server_no_context_takeover || params.server_max_window_bits && (opts.serverMaxWindowBits === false || typeof opts.serverMaxWindowBits === "number" && opts.serverMaxWindowBits > params.server_max_window_bits) || typeof opts.clientMaxWindowBits === "number" && !params.client_max_window_bits) {
                        return false;
                    }
                    return true;
                });
                if (!accepted) {
                    throw new Error("None of the extension offers can be accepted");
                }
                if (opts.serverNoContextTakeover) {
                    accepted.server_no_context_takeover = true;
                }
                if (opts.clientNoContextTakeover) {
                    accepted.client_no_context_takeover = true;
                }
                if (typeof opts.serverMaxWindowBits === "number") {
                    accepted.server_max_window_bits = opts.serverMaxWindowBits;
                }
                if (typeof opts.clientMaxWindowBits === "number") {
                    accepted.client_max_window_bits = opts.clientMaxWindowBits;
                } else if (accepted.client_max_window_bits === true || opts.clientMaxWindowBits === false) {
                    delete accepted.client_max_window_bits;
                }
                return accepted;
            }
            /**
       * Accept the extension negotiation response.
       *
       * @param {Array} response The extension negotiation response
       * @return {Object} Accepted configuration
       * @private
       */ acceptAsClient(response) {
                const params = response[0];
                if (this._options.clientNoContextTakeover === false && params.client_no_context_takeover) {
                    throw new Error('Unexpected parameter "client_no_context_takeover"');
                }
                if (!params.client_max_window_bits) {
                    if (typeof this._options.clientMaxWindowBits === "number") {
                        params.client_max_window_bits = this._options.clientMaxWindowBits;
                    }
                } else if (this._options.clientMaxWindowBits === false || typeof this._options.clientMaxWindowBits === "number" && params.client_max_window_bits > this._options.clientMaxWindowBits) {
                    throw new Error('Unexpected or invalid parameter "client_max_window_bits"');
                }
                return params;
            }
            /**
       * Normalize parameters.
       *
       * @param {Array} configurations The extension negotiation offers/reponse
       * @return {Array} The offers/response with normalized parameters
       * @private
       */ normalizeParams(configurations) {
                configurations.forEach((params)=>{
                    Object.keys(params).forEach((key)=>{
                        let value = params[key];
                        if (value.length > 1) {
                            throw new Error(`Parameter "${key}" must have only a single value`);
                        }
                        value = value[0];
                        if (key === "client_max_window_bits") {
                            if (value !== true) {
                                const num = +value;
                                if (!Number.isInteger(num) || num < 8 || num > 15) {
                                    throw new TypeError(`Invalid value for parameter "${key}": ${value}`);
                                }
                                value = num;
                            } else if (!this._isServer) {
                                throw new TypeError(`Invalid value for parameter "${key}": ${value}`);
                            }
                        } else if (key === "server_max_window_bits") {
                            const num = +value;
                            if (!Number.isInteger(num) || num < 8 || num > 15) {
                                throw new TypeError(`Invalid value for parameter "${key}": ${value}`);
                            }
                            value = num;
                        } else if (key === "client_no_context_takeover" || key === "server_no_context_takeover") {
                            if (value !== true) {
                                throw new TypeError(`Invalid value for parameter "${key}": ${value}`);
                            }
                        } else {
                            throw new Error(`Unknown parameter "${key}"`);
                        }
                        params[key] = value;
                    });
                });
                return configurations;
            }
            /**
       * Decompress data. Concurrency limited.
       *
       * @param {Buffer} data Compressed data
       * @param {Boolean} fin Specifies whether or not this is the last fragment
       * @param {Function} callback Callback
       * @public
       */ decompress(data, fin, callback) {
                zlibLimiter.add((done)=>{
                    this._decompress(data, fin, (err, result)=>{
                        done();
                        callback(err, result);
                    });
                });
            }
            /**
       * Compress data. Concurrency limited.
       *
       * @param {(Buffer|String)} data Data to compress
       * @param {Boolean} fin Specifies whether or not this is the last fragment
       * @param {Function} callback Callback
       * @public
       */ compress(data, fin, callback) {
                zlibLimiter.add((done)=>{
                    this._compress(data, fin, (err, result)=>{
                        done();
                        callback(err, result);
                    });
                });
            }
            /**
       * Decompress data.
       *
       * @param {Buffer} data Compressed data
       * @param {Boolean} fin Specifies whether or not this is the last fragment
       * @param {Function} callback Callback
       * @private
       */ _decompress(data, fin, callback) {
                const endpoint = this._isServer ? "client" : "server";
                if (!this._inflate) {
                    const key = `${endpoint}_max_window_bits`;
                    const windowBits = typeof this.params[key] !== "number" ? zlib.Z_DEFAULT_WINDOWBITS : this.params[key];
                    this._inflate = zlib.createInflateRaw({
                        ...this._options.zlibInflateOptions,
                        windowBits
                    });
                    this._inflate[kPerMessageDeflate] = this;
                    this._inflate[kTotalLength] = 0;
                    this._inflate[kBuffers] = [];
                    this._inflate.on("error", inflateOnError);
                    this._inflate.on("data", inflateOnData);
                }
                this._inflate[kCallback] = callback;
                this._inflate.write(data);
                if (fin) this._inflate.write(TRAILER);
                this._inflate.flush(()=>{
                    const err = this._inflate[kError];
                    if (err) {
                        this._inflate.close();
                        this._inflate = null;
                        callback(err);
                        return;
                    }
                    const data2 = bufferUtil.concat(this._inflate[kBuffers], this._inflate[kTotalLength]);
                    if (this._inflate._readableState.endEmitted) {
                        this._inflate.close();
                        this._inflate = null;
                    } else {
                        this._inflate[kTotalLength] = 0;
                        this._inflate[kBuffers] = [];
                        if (fin && this.params[`${endpoint}_no_context_takeover`]) {
                            this._inflate.reset();
                        }
                    }
                    callback(null, data2);
                });
            }
            /**
       * Compress data.
       *
       * @param {(Buffer|String)} data Data to compress
       * @param {Boolean} fin Specifies whether or not this is the last fragment
       * @param {Function} callback Callback
       * @private
       */ _compress(data, fin, callback) {
                const endpoint = this._isServer ? "server" : "client";
                if (!this._deflate) {
                    const key = `${endpoint}_max_window_bits`;
                    const windowBits = typeof this.params[key] !== "number" ? zlib.Z_DEFAULT_WINDOWBITS : this.params[key];
                    this._deflate = zlib.createDeflateRaw({
                        ...this._options.zlibDeflateOptions,
                        windowBits
                    });
                    this._deflate[kTotalLength] = 0;
                    this._deflate[kBuffers] = [];
                    this._deflate.on("data", deflateOnData);
                }
                this._deflate[kCallback] = callback;
                this._deflate.write(data);
                this._deflate.flush(zlib.Z_SYNC_FLUSH, ()=>{
                    if (!this._deflate) {
                        return;
                    }
                    let data2 = bufferUtil.concat(this._deflate[kBuffers], this._deflate[kTotalLength]);
                    if (fin) {
                        data2 = new FastBuffer(data2.buffer, data2.byteOffset, data2.length - 4);
                    }
                    this._deflate[kCallback] = null;
                    this._deflate[kTotalLength] = 0;
                    this._deflate[kBuffers] = [];
                    if (fin && this.params[`${endpoint}_no_context_takeover`]) {
                        this._deflate.reset();
                    }
                    callback(null, data2);
                });
            }
        };
        module.exports = PerMessageDeflate;
        function deflateOnData(chunk) {
            this[kBuffers].push(chunk);
            this[kTotalLength] += chunk.length;
        }
        function inflateOnData(chunk) {
            this[kTotalLength] += chunk.length;
            if (this[kPerMessageDeflate]._maxPayload < 1 || this[kTotalLength] <= this[kPerMessageDeflate]._maxPayload) {
                this[kBuffers].push(chunk);
                return;
            }
            this[kError] = new RangeError("Max payload size exceeded");
            this[kError].code = "WS_ERR_UNSUPPORTED_MESSAGE_LENGTH";
            this[kError][kStatusCode] = 1009;
            this.removeListener("data", inflateOnData);
            this.reset();
        }
        function inflateOnError(err) {
            this[kPerMessageDeflate]._inflate = null;
            err[kStatusCode] = 1007;
            this[kCallback](err);
        }
    }
});
// ../common/temp/node_modules/.pnpm/utf-8-validate@5.0.10/node_modules/utf-8-validate/fallback.js
var require_fallback2 = __commonJS({
    "../common/temp/node_modules/.pnpm/utf-8-validate@5.0.10/node_modules/utf-8-validate/fallback.js" (exports, module) {
        "use strict";
        function isValidUTF8(buf) {
            const len = buf.length;
            let i = 0;
            while(i < len){
                if ((buf[i] & 128) === 0) {
                    i++;
                } else if ((buf[i] & 224) === 192) {
                    if (i + 1 === len || (buf[i + 1] & 192) !== 128 || (buf[i] & 254) === 192) {
                        return false;
                    }
                    i += 2;
                } else if ((buf[i] & 240) === 224) {
                    if (i + 2 >= len || (buf[i + 1] & 192) !== 128 || (buf[i + 2] & 192) !== 128 || buf[i] === 224 && (buf[i + 1] & 224) === 128 || // overlong
                    buf[i] === 237 && (buf[i + 1] & 224) === 160) {
                        return false;
                    }
                    i += 3;
                } else if ((buf[i] & 248) === 240) {
                    if (i + 3 >= len || (buf[i + 1] & 192) !== 128 || (buf[i + 2] & 192) !== 128 || (buf[i + 3] & 192) !== 128 || buf[i] === 240 && (buf[i + 1] & 240) === 128 || // overlong
                    buf[i] === 244 && buf[i + 1] > 143 || buf[i] > 244) {
                        return false;
                    }
                    i += 4;
                } else {
                    return false;
                }
            }
            return true;
        }
        module.exports = isValidUTF8;
    }
});
// ../common/temp/node_modules/.pnpm/utf-8-validate@5.0.10/node_modules/utf-8-validate/index.js
var require_utf_8_validate = __commonJS({
    "../common/temp/node_modules/.pnpm/utf-8-validate@5.0.10/node_modules/utf-8-validate/index.js" (exports, module) {
        "use strict";
        try {
            module.exports = require_node_gyp_build2()(("TURBOPACK compile-time value", "/ROOT/node_modules/convex/dist/esm/browser"));
        } catch (e) {
            module.exports = require_fallback2();
        }
    }
});
// ../common/temp/node_modules/.pnpm/ws@8.18.0_bufferutil@4.0.9_utf-8-validate@5.0.10/node_modules/ws/lib/validation.js
var require_validation = __commonJS({
    "../common/temp/node_modules/.pnpm/ws@8.18.0_bufferutil@4.0.9_utf-8-validate@5.0.10/node_modules/ws/lib/validation.js" (exports, module) {
        "use strict";
        var { isUtf8 } = __require("buffer");
        var { hasBlob } = require_constants();
        var tokenChars = [
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            // 0 - 15
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            // 16 - 31
            0,
            1,
            0,
            1,
            1,
            1,
            1,
            1,
            0,
            0,
            1,
            1,
            0,
            1,
            1,
            0,
            // 32 - 47
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            0,
            0,
            0,
            0,
            0,
            0,
            // 48 - 63
            0,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            // 64 - 79
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            0,
            0,
            0,
            1,
            1,
            // 80 - 95
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            // 96 - 111
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            0,
            1,
            0,
            1,
            0
        ];
        function isValidStatusCode(code) {
            return code >= 1e3 && code <= 1014 && code !== 1004 && code !== 1005 && code !== 1006 || code >= 3e3 && code <= 4999;
        }
        function _isValidUTF8(buf) {
            const len = buf.length;
            let i = 0;
            while(i < len){
                if ((buf[i] & 128) === 0) {
                    i++;
                } else if ((buf[i] & 224) === 192) {
                    if (i + 1 === len || (buf[i + 1] & 192) !== 128 || (buf[i] & 254) === 192) {
                        return false;
                    }
                    i += 2;
                } else if ((buf[i] & 240) === 224) {
                    if (i + 2 >= len || (buf[i + 1] & 192) !== 128 || (buf[i + 2] & 192) !== 128 || buf[i] === 224 && (buf[i + 1] & 224) === 128 || // Overlong
                    buf[i] === 237 && (buf[i + 1] & 224) === 160) {
                        return false;
                    }
                    i += 3;
                } else if ((buf[i] & 248) === 240) {
                    if (i + 3 >= len || (buf[i + 1] & 192) !== 128 || (buf[i + 2] & 192) !== 128 || (buf[i + 3] & 192) !== 128 || buf[i] === 240 && (buf[i + 1] & 240) === 128 || // Overlong
                    buf[i] === 244 && buf[i + 1] > 143 || buf[i] > 244) {
                        return false;
                    }
                    i += 4;
                } else {
                    return false;
                }
            }
            return true;
        }
        function isBlob(value) {
            return hasBlob && typeof value === "object" && typeof value.arrayBuffer === "function" && typeof value.type === "string" && typeof value.stream === "function" && (value[Symbol.toStringTag] === "Blob" || value[Symbol.toStringTag] === "File");
        }
        module.exports = {
            isBlob,
            isValidStatusCode,
            isValidUTF8: _isValidUTF8,
            tokenChars
        };
        if (isUtf8) {
            module.exports.isValidUTF8 = function(buf) {
                return buf.length < 24 ? _isValidUTF8(buf) : isUtf8(buf);
            };
        } else if (!process.env.WS_NO_UTF_8_VALIDATE) {
            try {
                const isValidUTF8 = require_utf_8_validate();
                module.exports.isValidUTF8 = function(buf) {
                    return buf.length < 32 ? _isValidUTF8(buf) : isValidUTF8(buf);
                };
            } catch (e) {}
        }
    }
});
// ../common/temp/node_modules/.pnpm/ws@8.18.0_bufferutil@4.0.9_utf-8-validate@5.0.10/node_modules/ws/lib/receiver.js
var require_receiver = __commonJS({
    "../common/temp/node_modules/.pnpm/ws@8.18.0_bufferutil@4.0.9_utf-8-validate@5.0.10/node_modules/ws/lib/receiver.js" (exports, module) {
        "use strict";
        var { Writable } = __require("stream");
        var PerMessageDeflate = require_permessage_deflate();
        var { BINARY_TYPES, EMPTY_BUFFER, kStatusCode, kWebSocket } = require_constants();
        var { concat, toArrayBuffer, unmask } = require_buffer_util();
        var { isValidStatusCode, isValidUTF8 } = require_validation();
        var FastBuffer = Buffer[Symbol.species];
        var GET_INFO = 0;
        var GET_PAYLOAD_LENGTH_16 = 1;
        var GET_PAYLOAD_LENGTH_64 = 2;
        var GET_MASK = 3;
        var GET_DATA = 4;
        var INFLATING = 5;
        var DEFER_EVENT = 6;
        var Receiver2 = class extends Writable {
            /**
       * Creates a Receiver instance.
       *
       * @param {Object} [options] Options object
       * @param {Boolean} [options.allowSynchronousEvents=true] Specifies whether
       *     any of the `'message'`, `'ping'`, and `'pong'` events can be emitted
       *     multiple times in the same tick
       * @param {String} [options.binaryType=nodebuffer] The type for binary data
       * @param {Object} [options.extensions] An object containing the negotiated
       *     extensions
       * @param {Boolean} [options.isServer=false] Specifies whether to operate in
       *     client or server mode
       * @param {Number} [options.maxPayload=0] The maximum allowed message length
       * @param {Boolean} [options.skipUTF8Validation=false] Specifies whether or
       *     not to skip UTF-8 validation for text and close messages
       */ constructor(options = {}){
                super();
                this._allowSynchronousEvents = options.allowSynchronousEvents !== void 0 ? options.allowSynchronousEvents : true;
                this._binaryType = options.binaryType || BINARY_TYPES[0];
                this._extensions = options.extensions || {};
                this._isServer = !!options.isServer;
                this._maxPayload = options.maxPayload | 0;
                this._skipUTF8Validation = !!options.skipUTF8Validation;
                this[kWebSocket] = void 0;
                this._bufferedBytes = 0;
                this._buffers = [];
                this._compressed = false;
                this._payloadLength = 0;
                this._mask = void 0;
                this._fragmented = 0;
                this._masked = false;
                this._fin = false;
                this._opcode = 0;
                this._totalPayloadLength = 0;
                this._messageLength = 0;
                this._fragments = [];
                this._errored = false;
                this._loop = false;
                this._state = GET_INFO;
            }
            /**
       * Implements `Writable.prototype._write()`.
       *
       * @param {Buffer} chunk The chunk of data to write
       * @param {String} encoding The character encoding of `chunk`
       * @param {Function} cb Callback
       * @private
       */ _write(chunk, encoding, cb) {
                if (this._opcode === 8 && this._state == GET_INFO) return cb();
                this._bufferedBytes += chunk.length;
                this._buffers.push(chunk);
                this.startLoop(cb);
            }
            /**
       * Consumes `n` bytes from the buffered data.
       *
       * @param {Number} n The number of bytes to consume
       * @return {Buffer} The consumed bytes
       * @private
       */ consume(n) {
                this._bufferedBytes -= n;
                if (n === this._buffers[0].length) return this._buffers.shift();
                if (n < this._buffers[0].length) {
                    const buf = this._buffers[0];
                    this._buffers[0] = new FastBuffer(buf.buffer, buf.byteOffset + n, buf.length - n);
                    return new FastBuffer(buf.buffer, buf.byteOffset, n);
                }
                const dst = Buffer.allocUnsafe(n);
                do {
                    const buf = this._buffers[0];
                    const offset = dst.length - n;
                    if (n >= buf.length) {
                        dst.set(this._buffers.shift(), offset);
                    } else {
                        dst.set(new Uint8Array(buf.buffer, buf.byteOffset, n), offset);
                        this._buffers[0] = new FastBuffer(buf.buffer, buf.byteOffset + n, buf.length - n);
                    }
                    n -= buf.length;
                }while (n > 0)
                return dst;
            }
            /**
       * Starts the parsing loop.
       *
       * @param {Function} cb Callback
       * @private
       */ startLoop(cb) {
                this._loop = true;
                do {
                    switch(this._state){
                        case GET_INFO:
                            this.getInfo(cb);
                            break;
                        case GET_PAYLOAD_LENGTH_16:
                            this.getPayloadLength16(cb);
                            break;
                        case GET_PAYLOAD_LENGTH_64:
                            this.getPayloadLength64(cb);
                            break;
                        case GET_MASK:
                            this.getMask();
                            break;
                        case GET_DATA:
                            this.getData(cb);
                            break;
                        case INFLATING:
                        case DEFER_EVENT:
                            this._loop = false;
                            return;
                    }
                }while (this._loop)
                if (!this._errored) cb();
            }
            /**
       * Reads the first two bytes of a frame.
       *
       * @param {Function} cb Callback
       * @private
       */ getInfo(cb) {
                if (this._bufferedBytes < 2) {
                    this._loop = false;
                    return;
                }
                const buf = this.consume(2);
                if ((buf[0] & 48) !== 0) {
                    const error = this.createError(RangeError, "RSV2 and RSV3 must be clear", true, 1002, "WS_ERR_UNEXPECTED_RSV_2_3");
                    cb(error);
                    return;
                }
                const compressed = (buf[0] & 64) === 64;
                if (compressed && !this._extensions[PerMessageDeflate.extensionName]) {
                    const error = this.createError(RangeError, "RSV1 must be clear", true, 1002, "WS_ERR_UNEXPECTED_RSV_1");
                    cb(error);
                    return;
                }
                this._fin = (buf[0] & 128) === 128;
                this._opcode = buf[0] & 15;
                this._payloadLength = buf[1] & 127;
                if (this._opcode === 0) {
                    if (compressed) {
                        const error = this.createError(RangeError, "RSV1 must be clear", true, 1002, "WS_ERR_UNEXPECTED_RSV_1");
                        cb(error);
                        return;
                    }
                    if (!this._fragmented) {
                        const error = this.createError(RangeError, "invalid opcode 0", true, 1002, "WS_ERR_INVALID_OPCODE");
                        cb(error);
                        return;
                    }
                    this._opcode = this._fragmented;
                } else if (this._opcode === 1 || this._opcode === 2) {
                    if (this._fragmented) {
                        const error = this.createError(RangeError, `invalid opcode ${this._opcode}`, true, 1002, "WS_ERR_INVALID_OPCODE");
                        cb(error);
                        return;
                    }
                    this._compressed = compressed;
                } else if (this._opcode > 7 && this._opcode < 11) {
                    if (!this._fin) {
                        const error = this.createError(RangeError, "FIN must be set", true, 1002, "WS_ERR_EXPECTED_FIN");
                        cb(error);
                        return;
                    }
                    if (compressed) {
                        const error = this.createError(RangeError, "RSV1 must be clear", true, 1002, "WS_ERR_UNEXPECTED_RSV_1");
                        cb(error);
                        return;
                    }
                    if (this._payloadLength > 125 || this._opcode === 8 && this._payloadLength === 1) {
                        const error = this.createError(RangeError, `invalid payload length ${this._payloadLength}`, true, 1002, "WS_ERR_INVALID_CONTROL_PAYLOAD_LENGTH");
                        cb(error);
                        return;
                    }
                } else {
                    const error = this.createError(RangeError, `invalid opcode ${this._opcode}`, true, 1002, "WS_ERR_INVALID_OPCODE");
                    cb(error);
                    return;
                }
                if (!this._fin && !this._fragmented) this._fragmented = this._opcode;
                this._masked = (buf[1] & 128) === 128;
                if (this._isServer) {
                    if (!this._masked) {
                        const error = this.createError(RangeError, "MASK must be set", true, 1002, "WS_ERR_EXPECTED_MASK");
                        cb(error);
                        return;
                    }
                } else if (this._masked) {
                    const error = this.createError(RangeError, "MASK must be clear", true, 1002, "WS_ERR_UNEXPECTED_MASK");
                    cb(error);
                    return;
                }
                if (this._payloadLength === 126) this._state = GET_PAYLOAD_LENGTH_16;
                else if (this._payloadLength === 127) this._state = GET_PAYLOAD_LENGTH_64;
                else this.haveLength(cb);
            }
            /**
       * Gets extended payload length (7+16).
       *
       * @param {Function} cb Callback
       * @private
       */ getPayloadLength16(cb) {
                if (this._bufferedBytes < 2) {
                    this._loop = false;
                    return;
                }
                this._payloadLength = this.consume(2).readUInt16BE(0);
                this.haveLength(cb);
            }
            /**
       * Gets extended payload length (7+64).
       *
       * @param {Function} cb Callback
       * @private
       */ getPayloadLength64(cb) {
                if (this._bufferedBytes < 8) {
                    this._loop = false;
                    return;
                }
                const buf = this.consume(8);
                const num = buf.readUInt32BE(0);
                if (num > Math.pow(2, 53 - 32) - 1) {
                    const error = this.createError(RangeError, "Unsupported WebSocket frame: payload length > 2^53 - 1", false, 1009, "WS_ERR_UNSUPPORTED_DATA_PAYLOAD_LENGTH");
                    cb(error);
                    return;
                }
                this._payloadLength = num * Math.pow(2, 32) + buf.readUInt32BE(4);
                this.haveLength(cb);
            }
            /**
       * Payload length has been read.
       *
       * @param {Function} cb Callback
       * @private
       */ haveLength(cb) {
                if (this._payloadLength && this._opcode < 8) {
                    this._totalPayloadLength += this._payloadLength;
                    if (this._totalPayloadLength > this._maxPayload && this._maxPayload > 0) {
                        const error = this.createError(RangeError, "Max payload size exceeded", false, 1009, "WS_ERR_UNSUPPORTED_MESSAGE_LENGTH");
                        cb(error);
                        return;
                    }
                }
                if (this._masked) this._state = GET_MASK;
                else this._state = GET_DATA;
            }
            /**
       * Reads mask bytes.
       *
       * @private
       */ getMask() {
                if (this._bufferedBytes < 4) {
                    this._loop = false;
                    return;
                }
                this._mask = this.consume(4);
                this._state = GET_DATA;
            }
            /**
       * Reads data bytes.
       *
       * @param {Function} cb Callback
       * @private
       */ getData(cb) {
                let data = EMPTY_BUFFER;
                if (this._payloadLength) {
                    if (this._bufferedBytes < this._payloadLength) {
                        this._loop = false;
                        return;
                    }
                    data = this.consume(this._payloadLength);
                    if (this._masked && (this._mask[0] | this._mask[1] | this._mask[2] | this._mask[3]) !== 0) {
                        unmask(data, this._mask);
                    }
                }
                if (this._opcode > 7) {
                    this.controlMessage(data, cb);
                    return;
                }
                if (this._compressed) {
                    this._state = INFLATING;
                    this.decompress(data, cb);
                    return;
                }
                if (data.length) {
                    this._messageLength = this._totalPayloadLength;
                    this._fragments.push(data);
                }
                this.dataMessage(cb);
            }
            /**
       * Decompresses data.
       *
       * @param {Buffer} data Compressed data
       * @param {Function} cb Callback
       * @private
       */ decompress(data, cb) {
                const perMessageDeflate = this._extensions[PerMessageDeflate.extensionName];
                perMessageDeflate.decompress(data, this._fin, (err, buf)=>{
                    if (err) return cb(err);
                    if (buf.length) {
                        this._messageLength += buf.length;
                        if (this._messageLength > this._maxPayload && this._maxPayload > 0) {
                            const error = this.createError(RangeError, "Max payload size exceeded", false, 1009, "WS_ERR_UNSUPPORTED_MESSAGE_LENGTH");
                            cb(error);
                            return;
                        }
                        this._fragments.push(buf);
                    }
                    this.dataMessage(cb);
                    if (this._state === GET_INFO) this.startLoop(cb);
                });
            }
            /**
       * Handles a data message.
       *
       * @param {Function} cb Callback
       * @private
       */ dataMessage(cb) {
                if (!this._fin) {
                    this._state = GET_INFO;
                    return;
                }
                const messageLength = this._messageLength;
                const fragments = this._fragments;
                this._totalPayloadLength = 0;
                this._messageLength = 0;
                this._fragmented = 0;
                this._fragments = [];
                if (this._opcode === 2) {
                    let data;
                    if (this._binaryType === "nodebuffer") {
                        data = concat(fragments, messageLength);
                    } else if (this._binaryType === "arraybuffer") {
                        data = toArrayBuffer(concat(fragments, messageLength));
                    } else if (this._binaryType === "blob") {
                        data = new Blob(fragments);
                    } else {
                        data = fragments;
                    }
                    if (this._allowSynchronousEvents) {
                        this.emit("message", data, true);
                        this._state = GET_INFO;
                    } else {
                        this._state = DEFER_EVENT;
                        setImmediate(()=>{
                            this.emit("message", data, true);
                            this._state = GET_INFO;
                            this.startLoop(cb);
                        });
                    }
                } else {
                    const buf = concat(fragments, messageLength);
                    if (!this._skipUTF8Validation && !isValidUTF8(buf)) {
                        const error = this.createError(Error, "invalid UTF-8 sequence", true, 1007, "WS_ERR_INVALID_UTF8");
                        cb(error);
                        return;
                    }
                    if (this._state === INFLATING || this._allowSynchronousEvents) {
                        this.emit("message", buf, false);
                        this._state = GET_INFO;
                    } else {
                        this._state = DEFER_EVENT;
                        setImmediate(()=>{
                            this.emit("message", buf, false);
                            this._state = GET_INFO;
                            this.startLoop(cb);
                        });
                    }
                }
            }
            /**
       * Handles a control message.
       *
       * @param {Buffer} data Data to handle
       * @return {(Error|RangeError|undefined)} A possible error
       * @private
       */ controlMessage(data, cb) {
                if (this._opcode === 8) {
                    if (data.length === 0) {
                        this._loop = false;
                        this.emit("conclude", 1005, EMPTY_BUFFER);
                        this.end();
                    } else {
                        const code = data.readUInt16BE(0);
                        if (!isValidStatusCode(code)) {
                            const error = this.createError(RangeError, `invalid status code ${code}`, true, 1002, "WS_ERR_INVALID_CLOSE_CODE");
                            cb(error);
                            return;
                        }
                        const buf = new FastBuffer(data.buffer, data.byteOffset + 2, data.length - 2);
                        if (!this._skipUTF8Validation && !isValidUTF8(buf)) {
                            const error = this.createError(Error, "invalid UTF-8 sequence", true, 1007, "WS_ERR_INVALID_UTF8");
                            cb(error);
                            return;
                        }
                        this._loop = false;
                        this.emit("conclude", code, buf);
                        this.end();
                    }
                    this._state = GET_INFO;
                    return;
                }
                if (this._allowSynchronousEvents) {
                    this.emit(this._opcode === 9 ? "ping" : "pong", data);
                    this._state = GET_INFO;
                } else {
                    this._state = DEFER_EVENT;
                    setImmediate(()=>{
                        this.emit(this._opcode === 9 ? "ping" : "pong", data);
                        this._state = GET_INFO;
                        this.startLoop(cb);
                    });
                }
            }
            /**
       * Builds an error object.
       *
       * @param {function(new:Error|RangeError)} ErrorCtor The error constructor
       * @param {String} message The error message
       * @param {Boolean} prefix Specifies whether or not to add a default prefix to
       *     `message`
       * @param {Number} statusCode The status code
       * @param {String} errorCode The exposed error code
       * @return {(Error|RangeError)} The error
       * @private
       */ createError(ErrorCtor, message, prefix, statusCode, errorCode) {
                this._loop = false;
                this._errored = true;
                const err = new ErrorCtor(prefix ? `Invalid WebSocket frame: ${message}` : message);
                Error.captureStackTrace(err, this.createError);
                err.code = errorCode;
                err[kStatusCode] = statusCode;
                return err;
            }
        };
        module.exports = Receiver2;
    }
});
// ../common/temp/node_modules/.pnpm/ws@8.18.0_bufferutil@4.0.9_utf-8-validate@5.0.10/node_modules/ws/lib/sender.js
var require_sender = __commonJS({
    "../common/temp/node_modules/.pnpm/ws@8.18.0_bufferutil@4.0.9_utf-8-validate@5.0.10/node_modules/ws/lib/sender.js" (exports, module) {
        "use strict";
        var { Duplex } = __require("stream");
        var { randomFillSync } = __require("crypto");
        var PerMessageDeflate = require_permessage_deflate();
        var { EMPTY_BUFFER, kWebSocket, NOOP } = require_constants();
        var { isBlob, isValidStatusCode } = require_validation();
        var { mask: applyMask, toBuffer } = require_buffer_util();
        var kByteLength = Symbol("kByteLength");
        var maskBuffer = Buffer.alloc(4);
        var RANDOM_POOL_SIZE = 8 * 1024;
        var randomPool;
        var randomPoolPointer = RANDOM_POOL_SIZE;
        var DEFAULT = 0;
        var DEFLATING = 1;
        var GET_BLOB_DATA = 2;
        var Sender2 = class _Sender {
            /**
       * Creates a Sender instance.
       *
       * @param {Duplex} socket The connection socket
       * @param {Object} [extensions] An object containing the negotiated extensions
       * @param {Function} [generateMask] The function used to generate the masking
       *     key
       */ constructor(socket, extensions, generateMask){
                this._extensions = extensions || {};
                if (generateMask) {
                    this._generateMask = generateMask;
                    this._maskBuffer = Buffer.alloc(4);
                }
                this._socket = socket;
                this._firstFragment = true;
                this._compress = false;
                this._bufferedBytes = 0;
                this._queue = [];
                this._state = DEFAULT;
                this.onerror = NOOP;
                this[kWebSocket] = void 0;
            }
            /**
       * Frames a piece of data according to the HyBi WebSocket protocol.
       *
       * @param {(Buffer|String)} data The data to frame
       * @param {Object} options Options object
       * @param {Boolean} [options.fin=false] Specifies whether or not to set the
       *     FIN bit
       * @param {Function} [options.generateMask] The function used to generate the
       *     masking key
       * @param {Boolean} [options.mask=false] Specifies whether or not to mask
       *     `data`
       * @param {Buffer} [options.maskBuffer] The buffer used to store the masking
       *     key
       * @param {Number} options.opcode The opcode
       * @param {Boolean} [options.readOnly=false] Specifies whether `data` can be
       *     modified
       * @param {Boolean} [options.rsv1=false] Specifies whether or not to set the
       *     RSV1 bit
       * @return {(Buffer|String)[]} The framed data
       * @public
       */ static frame(data, options) {
                let mask;
                let merge = false;
                let offset = 2;
                let skipMasking = false;
                if (options.mask) {
                    mask = options.maskBuffer || maskBuffer;
                    if (options.generateMask) {
                        options.generateMask(mask);
                    } else {
                        if (randomPoolPointer === RANDOM_POOL_SIZE) {
                            if (randomPool === void 0) {
                                randomPool = Buffer.alloc(RANDOM_POOL_SIZE);
                            }
                            randomFillSync(randomPool, 0, RANDOM_POOL_SIZE);
                            randomPoolPointer = 0;
                        }
                        mask[0] = randomPool[randomPoolPointer++];
                        mask[1] = randomPool[randomPoolPointer++];
                        mask[2] = randomPool[randomPoolPointer++];
                        mask[3] = randomPool[randomPoolPointer++];
                    }
                    skipMasking = (mask[0] | mask[1] | mask[2] | mask[3]) === 0;
                    offset = 6;
                }
                let dataLength;
                if (typeof data === "string") {
                    if ((!options.mask || skipMasking) && options[kByteLength] !== void 0) {
                        dataLength = options[kByteLength];
                    } else {
                        data = Buffer.from(data);
                        dataLength = data.length;
                    }
                } else {
                    dataLength = data.length;
                    merge = options.mask && options.readOnly && !skipMasking;
                }
                let payloadLength = dataLength;
                if (dataLength >= 65536) {
                    offset += 8;
                    payloadLength = 127;
                } else if (dataLength > 125) {
                    offset += 2;
                    payloadLength = 126;
                }
                const target = Buffer.allocUnsafe(merge ? dataLength + offset : offset);
                target[0] = options.fin ? options.opcode | 128 : options.opcode;
                if (options.rsv1) target[0] |= 64;
                target[1] = payloadLength;
                if (payloadLength === 126) {
                    target.writeUInt16BE(dataLength, 2);
                } else if (payloadLength === 127) {
                    target[2] = target[3] = 0;
                    target.writeUIntBE(dataLength, 4, 6);
                }
                if (!options.mask) return [
                    target,
                    data
                ];
                target[1] |= 128;
                target[offset - 4] = mask[0];
                target[offset - 3] = mask[1];
                target[offset - 2] = mask[2];
                target[offset - 1] = mask[3];
                if (skipMasking) return [
                    target,
                    data
                ];
                if (merge) {
                    applyMask(data, mask, target, offset, dataLength);
                    return [
                        target
                    ];
                }
                applyMask(data, mask, data, 0, dataLength);
                return [
                    target,
                    data
                ];
            }
            /**
       * Sends a close message to the other peer.
       *
       * @param {Number} [code] The status code component of the body
       * @param {(String|Buffer)} [data] The message component of the body
       * @param {Boolean} [mask=false] Specifies whether or not to mask the message
       * @param {Function} [cb] Callback
       * @public
       */ close(code, data, mask, cb) {
                let buf;
                if (code === void 0) {
                    buf = EMPTY_BUFFER;
                } else if (typeof code !== "number" || !isValidStatusCode(code)) {
                    throw new TypeError("First argument must be a valid error code number");
                } else if (data === void 0 || !data.length) {
                    buf = Buffer.allocUnsafe(2);
                    buf.writeUInt16BE(code, 0);
                } else {
                    const length = Buffer.byteLength(data);
                    if (length > 123) {
                        throw new RangeError("The message must not be greater than 123 bytes");
                    }
                    buf = Buffer.allocUnsafe(2 + length);
                    buf.writeUInt16BE(code, 0);
                    if (typeof data === "string") {
                        buf.write(data, 2);
                    } else {
                        buf.set(data, 2);
                    }
                }
                const options = {
                    [kByteLength]: buf.length,
                    fin: true,
                    generateMask: this._generateMask,
                    mask,
                    maskBuffer: this._maskBuffer,
                    opcode: 8,
                    readOnly: false,
                    rsv1: false
                };
                if (this._state !== DEFAULT) {
                    this.enqueue([
                        this.dispatch,
                        buf,
                        false,
                        options,
                        cb
                    ]);
                } else {
                    this.sendFrame(_Sender.frame(buf, options), cb);
                }
            }
            /**
       * Sends a ping message to the other peer.
       *
       * @param {*} data The message to send
       * @param {Boolean} [mask=false] Specifies whether or not to mask `data`
       * @param {Function} [cb] Callback
       * @public
       */ ping(data, mask, cb) {
                let byteLength;
                let readOnly;
                if (typeof data === "string") {
                    byteLength = Buffer.byteLength(data);
                    readOnly = false;
                } else if (isBlob(data)) {
                    byteLength = data.size;
                    readOnly = false;
                } else {
                    data = toBuffer(data);
                    byteLength = data.length;
                    readOnly = toBuffer.readOnly;
                }
                if (byteLength > 125) {
                    throw new RangeError("The data size must not be greater than 125 bytes");
                }
                const options = {
                    [kByteLength]: byteLength,
                    fin: true,
                    generateMask: this._generateMask,
                    mask,
                    maskBuffer: this._maskBuffer,
                    opcode: 9,
                    readOnly,
                    rsv1: false
                };
                if (isBlob(data)) {
                    if (this._state !== DEFAULT) {
                        this.enqueue([
                            this.getBlobData,
                            data,
                            false,
                            options,
                            cb
                        ]);
                    } else {
                        this.getBlobData(data, false, options, cb);
                    }
                } else if (this._state !== DEFAULT) {
                    this.enqueue([
                        this.dispatch,
                        data,
                        false,
                        options,
                        cb
                    ]);
                } else {
                    this.sendFrame(_Sender.frame(data, options), cb);
                }
            }
            /**
       * Sends a pong message to the other peer.
       *
       * @param {*} data The message to send
       * @param {Boolean} [mask=false] Specifies whether or not to mask `data`
       * @param {Function} [cb] Callback
       * @public
       */ pong(data, mask, cb) {
                let byteLength;
                let readOnly;
                if (typeof data === "string") {
                    byteLength = Buffer.byteLength(data);
                    readOnly = false;
                } else if (isBlob(data)) {
                    byteLength = data.size;
                    readOnly = false;
                } else {
                    data = toBuffer(data);
                    byteLength = data.length;
                    readOnly = toBuffer.readOnly;
                }
                if (byteLength > 125) {
                    throw new RangeError("The data size must not be greater than 125 bytes");
                }
                const options = {
                    [kByteLength]: byteLength,
                    fin: true,
                    generateMask: this._generateMask,
                    mask,
                    maskBuffer: this._maskBuffer,
                    opcode: 10,
                    readOnly,
                    rsv1: false
                };
                if (isBlob(data)) {
                    if (this._state !== DEFAULT) {
                        this.enqueue([
                            this.getBlobData,
                            data,
                            false,
                            options,
                            cb
                        ]);
                    } else {
                        this.getBlobData(data, false, options, cb);
                    }
                } else if (this._state !== DEFAULT) {
                    this.enqueue([
                        this.dispatch,
                        data,
                        false,
                        options,
                        cb
                    ]);
                } else {
                    this.sendFrame(_Sender.frame(data, options), cb);
                }
            }
            /**
       * Sends a data message to the other peer.
       *
       * @param {*} data The message to send
       * @param {Object} options Options object
       * @param {Boolean} [options.binary=false] Specifies whether `data` is binary
       *     or text
       * @param {Boolean} [options.compress=false] Specifies whether or not to
       *     compress `data`
       * @param {Boolean} [options.fin=false] Specifies whether the fragment is the
       *     last one
       * @param {Boolean} [options.mask=false] Specifies whether or not to mask
       *     `data`
       * @param {Function} [cb] Callback
       * @public
       */ send(data, options, cb) {
                const perMessageDeflate = this._extensions[PerMessageDeflate.extensionName];
                let opcode = options.binary ? 2 : 1;
                let rsv1 = options.compress;
                let byteLength;
                let readOnly;
                if (typeof data === "string") {
                    byteLength = Buffer.byteLength(data);
                    readOnly = false;
                } else if (isBlob(data)) {
                    byteLength = data.size;
                    readOnly = false;
                } else {
                    data = toBuffer(data);
                    byteLength = data.length;
                    readOnly = toBuffer.readOnly;
                }
                if (this._firstFragment) {
                    this._firstFragment = false;
                    if (rsv1 && perMessageDeflate && perMessageDeflate.params[perMessageDeflate._isServer ? "server_no_context_takeover" : "client_no_context_takeover"]) {
                        rsv1 = byteLength >= perMessageDeflate._threshold;
                    }
                    this._compress = rsv1;
                } else {
                    rsv1 = false;
                    opcode = 0;
                }
                if (options.fin) this._firstFragment = true;
                const opts = {
                    [kByteLength]: byteLength,
                    fin: options.fin,
                    generateMask: this._generateMask,
                    mask: options.mask,
                    maskBuffer: this._maskBuffer,
                    opcode,
                    readOnly,
                    rsv1
                };
                if (isBlob(data)) {
                    if (this._state !== DEFAULT) {
                        this.enqueue([
                            this.getBlobData,
                            data,
                            this._compress,
                            opts,
                            cb
                        ]);
                    } else {
                        this.getBlobData(data, this._compress, opts, cb);
                    }
                } else if (this._state !== DEFAULT) {
                    this.enqueue([
                        this.dispatch,
                        data,
                        this._compress,
                        opts,
                        cb
                    ]);
                } else {
                    this.dispatch(data, this._compress, opts, cb);
                }
            }
            /**
       * Gets the contents of a blob as binary data.
       *
       * @param {Blob} blob The blob
       * @param {Boolean} [compress=false] Specifies whether or not to compress
       *     the data
       * @param {Object} options Options object
       * @param {Boolean} [options.fin=false] Specifies whether or not to set the
       *     FIN bit
       * @param {Function} [options.generateMask] The function used to generate the
       *     masking key
       * @param {Boolean} [options.mask=false] Specifies whether or not to mask
       *     `data`
       * @param {Buffer} [options.maskBuffer] The buffer used to store the masking
       *     key
       * @param {Number} options.opcode The opcode
       * @param {Boolean} [options.readOnly=false] Specifies whether `data` can be
       *     modified
       * @param {Boolean} [options.rsv1=false] Specifies whether or not to set the
       *     RSV1 bit
       * @param {Function} [cb] Callback
       * @private
       */ getBlobData(blob, compress, options, cb) {
                this._bufferedBytes += options[kByteLength];
                this._state = GET_BLOB_DATA;
                blob.arrayBuffer().then((arrayBuffer)=>{
                    if (this._socket.destroyed) {
                        const err = new Error("The socket was closed while the blob was being read");
                        process.nextTick(callCallbacks, this, err, cb);
                        return;
                    }
                    this._bufferedBytes -= options[kByteLength];
                    const data = toBuffer(arrayBuffer);
                    if (!compress) {
                        this._state = DEFAULT;
                        this.sendFrame(_Sender.frame(data, options), cb);
                        this.dequeue();
                    } else {
                        this.dispatch(data, compress, options, cb);
                    }
                }).catch((err)=>{
                    process.nextTick(onError, this, err, cb);
                });
            }
            /**
       * Dispatches a message.
       *
       * @param {(Buffer|String)} data The message to send
       * @param {Boolean} [compress=false] Specifies whether or not to compress
       *     `data`
       * @param {Object} options Options object
       * @param {Boolean} [options.fin=false] Specifies whether or not to set the
       *     FIN bit
       * @param {Function} [options.generateMask] The function used to generate the
       *     masking key
       * @param {Boolean} [options.mask=false] Specifies whether or not to mask
       *     `data`
       * @param {Buffer} [options.maskBuffer] The buffer used to store the masking
       *     key
       * @param {Number} options.opcode The opcode
       * @param {Boolean} [options.readOnly=false] Specifies whether `data` can be
       *     modified
       * @param {Boolean} [options.rsv1=false] Specifies whether or not to set the
       *     RSV1 bit
       * @param {Function} [cb] Callback
       * @private
       */ dispatch(data, compress, options, cb) {
                if (!compress) {
                    this.sendFrame(_Sender.frame(data, options), cb);
                    return;
                }
                const perMessageDeflate = this._extensions[PerMessageDeflate.extensionName];
                this._bufferedBytes += options[kByteLength];
                this._state = DEFLATING;
                perMessageDeflate.compress(data, options.fin, (_, buf)=>{
                    if (this._socket.destroyed) {
                        const err = new Error("The socket was closed while data was being compressed");
                        callCallbacks(this, err, cb);
                        return;
                    }
                    this._bufferedBytes -= options[kByteLength];
                    this._state = DEFAULT;
                    options.readOnly = false;
                    this.sendFrame(_Sender.frame(buf, options), cb);
                    this.dequeue();
                });
            }
            /**
       * Executes queued send operations.
       *
       * @private
       */ dequeue() {
                while(this._state === DEFAULT && this._queue.length){
                    const params = this._queue.shift();
                    this._bufferedBytes -= params[3][kByteLength];
                    Reflect.apply(params[0], this, params.slice(1));
                }
            }
            /**
       * Enqueues a send operation.
       *
       * @param {Array} params Send operation parameters.
       * @private
       */ enqueue(params) {
                this._bufferedBytes += params[3][kByteLength];
                this._queue.push(params);
            }
            /**
       * Sends a frame.
       *
       * @param {Buffer[]} list The frame to send
       * @param {Function} [cb] Callback
       * @private
       */ sendFrame(list, cb) {
                if (list.length === 2) {
                    this._socket.cork();
                    this._socket.write(list[0]);
                    this._socket.write(list[1], cb);
                    this._socket.uncork();
                } else {
                    this._socket.write(list[0], cb);
                }
            }
        };
        module.exports = Sender2;
        function callCallbacks(sender, err, cb) {
            if (typeof cb === "function") cb(err);
            for(let i = 0; i < sender._queue.length; i++){
                const params = sender._queue[i];
                const callback = params[params.length - 1];
                if (typeof callback === "function") callback(err);
            }
        }
        function onError(sender, err, cb) {
            callCallbacks(sender, err, cb);
            sender.onerror(err);
        }
    }
});
// ../common/temp/node_modules/.pnpm/ws@8.18.0_bufferutil@4.0.9_utf-8-validate@5.0.10/node_modules/ws/lib/event-target.js
var require_event_target = __commonJS({
    "../common/temp/node_modules/.pnpm/ws@8.18.0_bufferutil@4.0.9_utf-8-validate@5.0.10/node_modules/ws/lib/event-target.js" (exports, module) {
        "use strict";
        var { kForOnEventAttribute, kListener } = require_constants();
        var kCode = Symbol("kCode");
        var kData = Symbol("kData");
        var kError = Symbol("kError");
        var kMessage = Symbol("kMessage");
        var kReason = Symbol("kReason");
        var kTarget = Symbol("kTarget");
        var kType = Symbol("kType");
        var kWasClean = Symbol("kWasClean");
        var Event = class {
            /**
       * Create a new `Event`.
       *
       * @param {String} type The name of the event
       * @throws {TypeError} If the `type` argument is not specified
       */ constructor(type){
                this[kTarget] = null;
                this[kType] = type;
            }
            /**
       * @type {*}
       */ get target() {
                return this[kTarget];
            }
            /**
       * @type {String}
       */ get type() {
                return this[kType];
            }
        };
        Object.defineProperty(Event.prototype, "target", {
            enumerable: true
        });
        Object.defineProperty(Event.prototype, "type", {
            enumerable: true
        });
        var CloseEvent = class extends Event {
            /**
       * Create a new `CloseEvent`.
       *
       * @param {String} type The name of the event
       * @param {Object} [options] A dictionary object that allows for setting
       *     attributes via object members of the same name
       * @param {Number} [options.code=0] The status code explaining why the
       *     connection was closed
       * @param {String} [options.reason=''] A human-readable string explaining why
       *     the connection was closed
       * @param {Boolean} [options.wasClean=false] Indicates whether or not the
       *     connection was cleanly closed
       */ constructor(type, options = {}){
                super(type);
                this[kCode] = options.code === void 0 ? 0 : options.code;
                this[kReason] = options.reason === void 0 ? "" : options.reason;
                this[kWasClean] = options.wasClean === void 0 ? false : options.wasClean;
            }
            /**
       * @type {Number}
       */ get code() {
                return this[kCode];
            }
            /**
       * @type {String}
       */ get reason() {
                return this[kReason];
            }
            /**
       * @type {Boolean}
       */ get wasClean() {
                return this[kWasClean];
            }
        };
        Object.defineProperty(CloseEvent.prototype, "code", {
            enumerable: true
        });
        Object.defineProperty(CloseEvent.prototype, "reason", {
            enumerable: true
        });
        Object.defineProperty(CloseEvent.prototype, "wasClean", {
            enumerable: true
        });
        var ErrorEvent = class extends Event {
            /**
       * Create a new `ErrorEvent`.
       *
       * @param {String} type The name of the event
       * @param {Object} [options] A dictionary object that allows for setting
       *     attributes via object members of the same name
       * @param {*} [options.error=null] The error that generated this event
       * @param {String} [options.message=''] The error message
       */ constructor(type, options = {}){
                super(type);
                this[kError] = options.error === void 0 ? null : options.error;
                this[kMessage] = options.message === void 0 ? "" : options.message;
            }
            /**
       * @type {*}
       */ get error() {
                return this[kError];
            }
            /**
       * @type {String}
       */ get message() {
                return this[kMessage];
            }
        };
        Object.defineProperty(ErrorEvent.prototype, "error", {
            enumerable: true
        });
        Object.defineProperty(ErrorEvent.prototype, "message", {
            enumerable: true
        });
        var MessageEvent = class extends Event {
            /**
       * Create a new `MessageEvent`.
       *
       * @param {String} type The name of the event
       * @param {Object} [options] A dictionary object that allows for setting
       *     attributes via object members of the same name
       * @param {*} [options.data=null] The message content
       */ constructor(type, options = {}){
                super(type);
                this[kData] = options.data === void 0 ? null : options.data;
            }
            /**
       * @type {*}
       */ get data() {
                return this[kData];
            }
        };
        Object.defineProperty(MessageEvent.prototype, "data", {
            enumerable: true
        });
        var EventTarget = {
            /**
       * Register an event listener.
       *
       * @param {String} type A string representing the event type to listen for
       * @param {(Function|Object)} handler The listener to add
       * @param {Object} [options] An options object specifies characteristics about
       *     the event listener
       * @param {Boolean} [options.once=false] A `Boolean` indicating that the
       *     listener should be invoked at most once after being added. If `true`,
       *     the listener would be automatically removed when invoked.
       * @public
       */ addEventListener (type, handler, options = {}) {
                for (const listener of this.listeners(type)){
                    if (!options[kForOnEventAttribute] && listener[kListener] === handler && !listener[kForOnEventAttribute]) {
                        return;
                    }
                }
                let wrapper;
                if (type === "message") {
                    wrapper = function onMessage(data, isBinary) {
                        const event = new MessageEvent("message", {
                            data: isBinary ? data : data.toString()
                        });
                        event[kTarget] = this;
                        callListener(handler, this, event);
                    };
                } else if (type === "close") {
                    wrapper = function onClose(code, message) {
                        const event = new CloseEvent("close", {
                            code,
                            reason: message.toString(),
                            wasClean: this._closeFrameReceived && this._closeFrameSent
                        });
                        event[kTarget] = this;
                        callListener(handler, this, event);
                    };
                } else if (type === "error") {
                    wrapper = function onError(error) {
                        const event = new ErrorEvent("error", {
                            error,
                            message: error.message
                        });
                        event[kTarget] = this;
                        callListener(handler, this, event);
                    };
                } else if (type === "open") {
                    wrapper = function onOpen() {
                        const event = new Event("open");
                        event[kTarget] = this;
                        callListener(handler, this, event);
                    };
                } else {
                    return;
                }
                wrapper[kForOnEventAttribute] = !!options[kForOnEventAttribute];
                wrapper[kListener] = handler;
                if (options.once) {
                    this.once(type, wrapper);
                } else {
                    this.on(type, wrapper);
                }
            },
            /**
       * Remove an event listener.
       *
       * @param {String} type A string representing the event type to remove
       * @param {(Function|Object)} handler The listener to remove
       * @public
       */ removeEventListener (type, handler) {
                for (const listener of this.listeners(type)){
                    if (listener[kListener] === handler && !listener[kForOnEventAttribute]) {
                        this.removeListener(type, listener);
                        break;
                    }
                }
            }
        };
        module.exports = {
            CloseEvent,
            ErrorEvent,
            Event,
            EventTarget,
            MessageEvent
        };
        function callListener(listener, thisArg, event) {
            if (typeof listener === "object" && listener.handleEvent) {
                listener.handleEvent.call(listener, event);
            } else {
                listener.call(thisArg, event);
            }
        }
    }
});
// ../common/temp/node_modules/.pnpm/ws@8.18.0_bufferutil@4.0.9_utf-8-validate@5.0.10/node_modules/ws/lib/extension.js
var require_extension = __commonJS({
    "../common/temp/node_modules/.pnpm/ws@8.18.0_bufferutil@4.0.9_utf-8-validate@5.0.10/node_modules/ws/lib/extension.js" (exports, module) {
        "use strict";
        var { tokenChars } = require_validation();
        function push(dest, name, elem) {
            if (dest[name] === void 0) dest[name] = [
                elem
            ];
            else dest[name].push(elem);
        }
        function parse(header) {
            const offers = /* @__PURE__ */ Object.create(null);
            let params = /* @__PURE__ */ Object.create(null);
            let mustUnescape = false;
            let isEscaping = false;
            let inQuotes = false;
            let extensionName;
            let paramName;
            let start = -1;
            let code = -1;
            let end = -1;
            let i = 0;
            for(; i < header.length; i++){
                code = header.charCodeAt(i);
                if (extensionName === void 0) {
                    if (end === -1 && tokenChars[code] === 1) {
                        if (start === -1) start = i;
                    } else if (i !== 0 && (code === 32 || code === 9)) {
                        if (end === -1 && start !== -1) end = i;
                    } else if (code === 59 || code === 44) {
                        if (start === -1) {
                            throw new SyntaxError(`Unexpected character at index ${i}`);
                        }
                        if (end === -1) end = i;
                        const name = header.slice(start, end);
                        if (code === 44) {
                            push(offers, name, params);
                            params = /* @__PURE__ */ Object.create(null);
                        } else {
                            extensionName = name;
                        }
                        start = end = -1;
                    } else {
                        throw new SyntaxError(`Unexpected character at index ${i}`);
                    }
                } else if (paramName === void 0) {
                    if (end === -1 && tokenChars[code] === 1) {
                        if (start === -1) start = i;
                    } else if (code === 32 || code === 9) {
                        if (end === -1 && start !== -1) end = i;
                    } else if (code === 59 || code === 44) {
                        if (start === -1) {
                            throw new SyntaxError(`Unexpected character at index ${i}`);
                        }
                        if (end === -1) end = i;
                        push(params, header.slice(start, end), true);
                        if (code === 44) {
                            push(offers, extensionName, params);
                            params = /* @__PURE__ */ Object.create(null);
                            extensionName = void 0;
                        }
                        start = end = -1;
                    } else if (code === 61 && start !== -1 && end === -1) {
                        paramName = header.slice(start, i);
                        start = end = -1;
                    } else {
                        throw new SyntaxError(`Unexpected character at index ${i}`);
                    }
                } else {
                    if (isEscaping) {
                        if (tokenChars[code] !== 1) {
                            throw new SyntaxError(`Unexpected character at index ${i}`);
                        }
                        if (start === -1) start = i;
                        else if (!mustUnescape) mustUnescape = true;
                        isEscaping = false;
                    } else if (inQuotes) {
                        if (tokenChars[code] === 1) {
                            if (start === -1) start = i;
                        } else if (code === 34 && start !== -1) {
                            inQuotes = false;
                            end = i;
                        } else if (code === 92) {
                            isEscaping = true;
                        } else {
                            throw new SyntaxError(`Unexpected character at index ${i}`);
                        }
                    } else if (code === 34 && header.charCodeAt(i - 1) === 61) {
                        inQuotes = true;
                    } else if (end === -1 && tokenChars[code] === 1) {
                        if (start === -1) start = i;
                    } else if (start !== -1 && (code === 32 || code === 9)) {
                        if (end === -1) end = i;
                    } else if (code === 59 || code === 44) {
                        if (start === -1) {
                            throw new SyntaxError(`Unexpected character at index ${i}`);
                        }
                        if (end === -1) end = i;
                        let value = header.slice(start, end);
                        if (mustUnescape) {
                            value = value.replace(/\\/g, "");
                            mustUnescape = false;
                        }
                        push(params, paramName, value);
                        if (code === 44) {
                            push(offers, extensionName, params);
                            params = /* @__PURE__ */ Object.create(null);
                            extensionName = void 0;
                        }
                        paramName = void 0;
                        start = end = -1;
                    } else {
                        throw new SyntaxError(`Unexpected character at index ${i}`);
                    }
                }
            }
            if (start === -1 || inQuotes || code === 32 || code === 9) {
                throw new SyntaxError("Unexpected end of input");
            }
            if (end === -1) end = i;
            const token = header.slice(start, end);
            if (extensionName === void 0) {
                push(offers, token, params);
            } else {
                if (paramName === void 0) {
                    push(params, token, true);
                } else if (mustUnescape) {
                    push(params, paramName, token.replace(/\\/g, ""));
                } else {
                    push(params, paramName, token);
                }
                push(offers, extensionName, params);
            }
            return offers;
        }
        function format(extensions) {
            return Object.keys(extensions).map((extension)=>{
                let configurations = extensions[extension];
                if (!Array.isArray(configurations)) configurations = [
                    configurations
                ];
                return configurations.map((params)=>{
                    return [
                        extension
                    ].concat(Object.keys(params).map((k)=>{
                        let values = params[k];
                        if (!Array.isArray(values)) values = [
                            values
                        ];
                        return values.map((v)=>v === true ? k : `${k}=${v}`).join("; ");
                    })).join("; ");
                }).join(", ");
            }).join(", ");
        }
        module.exports = {
            format,
            parse
        };
    }
});
// ../common/temp/node_modules/.pnpm/ws@8.18.0_bufferutil@4.0.9_utf-8-validate@5.0.10/node_modules/ws/lib/websocket.js
var require_websocket = __commonJS({
    "../common/temp/node_modules/.pnpm/ws@8.18.0_bufferutil@4.0.9_utf-8-validate@5.0.10/node_modules/ws/lib/websocket.js" (exports, module) {
        "use strict";
        var EventEmitter = __require("events");
        var https = __require("https");
        var http = __require("http");
        var net = __require("net");
        var tls = __require("tls");
        var { randomBytes, createHash } = __require("crypto");
        var { Duplex, Readable } = __require("stream");
        var { URL } = __require("url");
        var PerMessageDeflate = require_permessage_deflate();
        var Receiver2 = require_receiver();
        var Sender2 = require_sender();
        var { isBlob } = require_validation();
        var { BINARY_TYPES, EMPTY_BUFFER, GUID, kForOnEventAttribute, kListener, kStatusCode, kWebSocket, NOOP } = require_constants();
        var { EventTarget: { addEventListener, removeEventListener } } = require_event_target();
        var { format, parse } = require_extension();
        var { toBuffer } = require_buffer_util();
        var closeTimeout = 30 * 1e3;
        var kAborted = Symbol("kAborted");
        var protocolVersions = [
            8,
            13
        ];
        var readyStates = [
            "CONNECTING",
            "OPEN",
            "CLOSING",
            "CLOSED"
        ];
        var subprotocolRegex = /^[!#$%&'*+\-.0-9A-Z^_`|a-z~]+$/;
        var WebSocket2 = class _WebSocket extends EventEmitter {
            /**
       * Create a new `WebSocket`.
       *
       * @param {(String|URL)} address The URL to which to connect
       * @param {(String|String[])} [protocols] The subprotocols
       * @param {Object} [options] Connection options
       */ constructor(address, protocols, options){
                super();
                this._binaryType = BINARY_TYPES[0];
                this._closeCode = 1006;
                this._closeFrameReceived = false;
                this._closeFrameSent = false;
                this._closeMessage = EMPTY_BUFFER;
                this._closeTimer = null;
                this._errorEmitted = false;
                this._extensions = {};
                this._paused = false;
                this._protocol = "";
                this._readyState = _WebSocket.CONNECTING;
                this._receiver = null;
                this._sender = null;
                this._socket = null;
                if (address !== null) {
                    this._bufferedAmount = 0;
                    this._isServer = false;
                    this._redirects = 0;
                    if (protocols === void 0) {
                        protocols = [];
                    } else if (!Array.isArray(protocols)) {
                        if (typeof protocols === "object" && protocols !== null) {
                            options = protocols;
                            protocols = [];
                        } else {
                            protocols = [
                                protocols
                            ];
                        }
                    }
                    initAsClient(this, address, protocols, options);
                } else {
                    this._autoPong = options.autoPong;
                    this._isServer = true;
                }
            }
            /**
       * For historical reasons, the custom "nodebuffer" type is used by the default
       * instead of "blob".
       *
       * @type {String}
       */ get binaryType() {
                return this._binaryType;
            }
            set binaryType(type) {
                if (!BINARY_TYPES.includes(type)) return;
                this._binaryType = type;
                if (this._receiver) this._receiver._binaryType = type;
            }
            /**
       * @type {Number}
       */ get bufferedAmount() {
                if (!this._socket) return this._bufferedAmount;
                return this._socket._writableState.length + this._sender._bufferedBytes;
            }
            /**
       * @type {String}
       */ get extensions() {
                return Object.keys(this._extensions).join();
            }
            /**
       * @type {Boolean}
       */ get isPaused() {
                return this._paused;
            }
            /**
       * @type {Function}
       */ /* istanbul ignore next */ get onclose() {
                return null;
            }
            /**
       * @type {Function}
       */ /* istanbul ignore next */ get onerror() {
                return null;
            }
            /**
       * @type {Function}
       */ /* istanbul ignore next */ get onopen() {
                return null;
            }
            /**
       * @type {Function}
       */ /* istanbul ignore next */ get onmessage() {
                return null;
            }
            /**
       * @type {String}
       */ get protocol() {
                return this._protocol;
            }
            /**
       * @type {Number}
       */ get readyState() {
                return this._readyState;
            }
            /**
       * @type {String}
       */ get url() {
                return this._url;
            }
            /**
       * Set up the socket and the internal resources.
       *
       * @param {Duplex} socket The network socket between the server and client
       * @param {Buffer} head The first packet of the upgraded stream
       * @param {Object} options Options object
       * @param {Boolean} [options.allowSynchronousEvents=false] Specifies whether
       *     any of the `'message'`, `'ping'`, and `'pong'` events can be emitted
       *     multiple times in the same tick
       * @param {Function} [options.generateMask] The function used to generate the
       *     masking key
       * @param {Number} [options.maxPayload=0] The maximum allowed message size
       * @param {Boolean} [options.skipUTF8Validation=false] Specifies whether or
       *     not to skip UTF-8 validation for text and close messages
       * @private
       */ setSocket(socket, head, options) {
                const receiver = new Receiver2({
                    allowSynchronousEvents: options.allowSynchronousEvents,
                    binaryType: this.binaryType,
                    extensions: this._extensions,
                    isServer: this._isServer,
                    maxPayload: options.maxPayload,
                    skipUTF8Validation: options.skipUTF8Validation
                });
                const sender = new Sender2(socket, this._extensions, options.generateMask);
                this._receiver = receiver;
                this._sender = sender;
                this._socket = socket;
                receiver[kWebSocket] = this;
                sender[kWebSocket] = this;
                socket[kWebSocket] = this;
                receiver.on("conclude", receiverOnConclude);
                receiver.on("drain", receiverOnDrain);
                receiver.on("error", receiverOnError);
                receiver.on("message", receiverOnMessage);
                receiver.on("ping", receiverOnPing);
                receiver.on("pong", receiverOnPong);
                sender.onerror = senderOnError;
                if (socket.setTimeout) socket.setTimeout(0);
                if (socket.setNoDelay) socket.setNoDelay();
                if (head.length > 0) socket.unshift(head);
                socket.on("close", socketOnClose);
                socket.on("data", socketOnData);
                socket.on("end", socketOnEnd);
                socket.on("error", socketOnError);
                this._readyState = _WebSocket.OPEN;
                this.emit("open");
            }
            /**
       * Emit the `'close'` event.
       *
       * @private
       */ emitClose() {
                if (!this._socket) {
                    this._readyState = _WebSocket.CLOSED;
                    this.emit("close", this._closeCode, this._closeMessage);
                    return;
                }
                if (this._extensions[PerMessageDeflate.extensionName]) {
                    this._extensions[PerMessageDeflate.extensionName].cleanup();
                }
                this._receiver.removeAllListeners();
                this._readyState = _WebSocket.CLOSED;
                this.emit("close", this._closeCode, this._closeMessage);
            }
            /**
       * Start a closing handshake.
       *
       *          +----------+   +-----------+   +----------+
       *     - - -|ws.close()|-->|close frame|-->|ws.close()|- - -
       *    |     +----------+   +-----------+   +----------+     |
       *          +----------+   +-----------+         |
       * CLOSING  |ws.close()|<--|close frame|<--+-----+       CLOSING
       *          +----------+   +-----------+   |
       *    |           |                        |   +---+        |
       *                +------------------------+-->|fin| - - - -
       *    |         +---+                      |   +---+
       *     - - - - -|fin|<---------------------+
       *              +---+
       *
       * @param {Number} [code] Status code explaining why the connection is closing
       * @param {(String|Buffer)} [data] The reason why the connection is
       *     closing
       * @public
       */ close(code, data) {
                if (this.readyState === _WebSocket.CLOSED) return;
                if (this.readyState === _WebSocket.CONNECTING) {
                    const msg = "WebSocket was closed before the connection was established";
                    abortHandshake(this, this._req, msg);
                    return;
                }
                if (this.readyState === _WebSocket.CLOSING) {
                    if (this._closeFrameSent && (this._closeFrameReceived || this._receiver._writableState.errorEmitted)) {
                        this._socket.end();
                    }
                    return;
                }
                this._readyState = _WebSocket.CLOSING;
                this._sender.close(code, data, !this._isServer, (err)=>{
                    if (err) return;
                    this._closeFrameSent = true;
                    if (this._closeFrameReceived || this._receiver._writableState.errorEmitted) {
                        this._socket.end();
                    }
                });
                setCloseTimer(this);
            }
            /**
       * Pause the socket.
       *
       * @public
       */ pause() {
                if (this.readyState === _WebSocket.CONNECTING || this.readyState === _WebSocket.CLOSED) {
                    return;
                }
                this._paused = true;
                this._socket.pause();
            }
            /**
       * Send a ping.
       *
       * @param {*} [data] The data to send
       * @param {Boolean} [mask] Indicates whether or not to mask `data`
       * @param {Function} [cb] Callback which is executed when the ping is sent
       * @public
       */ ping(data, mask, cb) {
                if (this.readyState === _WebSocket.CONNECTING) {
                    throw new Error("WebSocket is not open: readyState 0 (CONNECTING)");
                }
                if (typeof data === "function") {
                    cb = data;
                    data = mask = void 0;
                } else if (typeof mask === "function") {
                    cb = mask;
                    mask = void 0;
                }
                if (typeof data === "number") data = data.toString();
                if (this.readyState !== _WebSocket.OPEN) {
                    sendAfterClose(this, data, cb);
                    return;
                }
                if (mask === void 0) mask = !this._isServer;
                this._sender.ping(data || EMPTY_BUFFER, mask, cb);
            }
            /**
       * Send a pong.
       *
       * @param {*} [data] The data to send
       * @param {Boolean} [mask] Indicates whether or not to mask `data`
       * @param {Function} [cb] Callback which is executed when the pong is sent
       * @public
       */ pong(data, mask, cb) {
                if (this.readyState === _WebSocket.CONNECTING) {
                    throw new Error("WebSocket is not open: readyState 0 (CONNECTING)");
                }
                if (typeof data === "function") {
                    cb = data;
                    data = mask = void 0;
                } else if (typeof mask === "function") {
                    cb = mask;
                    mask = void 0;
                }
                if (typeof data === "number") data = data.toString();
                if (this.readyState !== _WebSocket.OPEN) {
                    sendAfterClose(this, data, cb);
                    return;
                }
                if (mask === void 0) mask = !this._isServer;
                this._sender.pong(data || EMPTY_BUFFER, mask, cb);
            }
            /**
       * Resume the socket.
       *
       * @public
       */ resume() {
                if (this.readyState === _WebSocket.CONNECTING || this.readyState === _WebSocket.CLOSED) {
                    return;
                }
                this._paused = false;
                if (!this._receiver._writableState.needDrain) this._socket.resume();
            }
            /**
       * Send a data message.
       *
       * @param {*} data The message to send
       * @param {Object} [options] Options object
       * @param {Boolean} [options.binary] Specifies whether `data` is binary or
       *     text
       * @param {Boolean} [options.compress] Specifies whether or not to compress
       *     `data`
       * @param {Boolean} [options.fin=true] Specifies whether the fragment is the
       *     last one
       * @param {Boolean} [options.mask] Specifies whether or not to mask `data`
       * @param {Function} [cb] Callback which is executed when data is written out
       * @public
       */ send(data, options, cb) {
                if (this.readyState === _WebSocket.CONNECTING) {
                    throw new Error("WebSocket is not open: readyState 0 (CONNECTING)");
                }
                if (typeof options === "function") {
                    cb = options;
                    options = {};
                }
                if (typeof data === "number") data = data.toString();
                if (this.readyState !== _WebSocket.OPEN) {
                    sendAfterClose(this, data, cb);
                    return;
                }
                const opts = {
                    binary: typeof data !== "string",
                    mask: !this._isServer,
                    compress: true,
                    fin: true,
                    ...options
                };
                if (!this._extensions[PerMessageDeflate.extensionName]) {
                    opts.compress = false;
                }
                this._sender.send(data || EMPTY_BUFFER, opts, cb);
            }
            /**
       * Forcibly close the connection.
       *
       * @public
       */ terminate() {
                if (this.readyState === _WebSocket.CLOSED) return;
                if (this.readyState === _WebSocket.CONNECTING) {
                    const msg = "WebSocket was closed before the connection was established";
                    abortHandshake(this, this._req, msg);
                    return;
                }
                if (this._socket) {
                    this._readyState = _WebSocket.CLOSING;
                    this._socket.destroy();
                }
            }
        };
        Object.defineProperty(WebSocket2, "CONNECTING", {
            enumerable: true,
            value: readyStates.indexOf("CONNECTING")
        });
        Object.defineProperty(WebSocket2.prototype, "CONNECTING", {
            enumerable: true,
            value: readyStates.indexOf("CONNECTING")
        });
        Object.defineProperty(WebSocket2, "OPEN", {
            enumerable: true,
            value: readyStates.indexOf("OPEN")
        });
        Object.defineProperty(WebSocket2.prototype, "OPEN", {
            enumerable: true,
            value: readyStates.indexOf("OPEN")
        });
        Object.defineProperty(WebSocket2, "CLOSING", {
            enumerable: true,
            value: readyStates.indexOf("CLOSING")
        });
        Object.defineProperty(WebSocket2.prototype, "CLOSING", {
            enumerable: true,
            value: readyStates.indexOf("CLOSING")
        });
        Object.defineProperty(WebSocket2, "CLOSED", {
            enumerable: true,
            value: readyStates.indexOf("CLOSED")
        });
        Object.defineProperty(WebSocket2.prototype, "CLOSED", {
            enumerable: true,
            value: readyStates.indexOf("CLOSED")
        });
        [
            "binaryType",
            "bufferedAmount",
            "extensions",
            "isPaused",
            "protocol",
            "readyState",
            "url"
        ].forEach((property)=>{
            Object.defineProperty(WebSocket2.prototype, property, {
                enumerable: true
            });
        });
        [
            "open",
            "error",
            "close",
            "message"
        ].forEach((method)=>{
            Object.defineProperty(WebSocket2.prototype, `on${method}`, {
                enumerable: true,
                get () {
                    for (const listener of this.listeners(method)){
                        if (listener[kForOnEventAttribute]) return listener[kListener];
                    }
                    return null;
                },
                set (handler) {
                    for (const listener of this.listeners(method)){
                        if (listener[kForOnEventAttribute]) {
                            this.removeListener(method, listener);
                            break;
                        }
                    }
                    if (typeof handler !== "function") return;
                    this.addEventListener(method, handler, {
                        [kForOnEventAttribute]: true
                    });
                }
            });
        });
        WebSocket2.prototype.addEventListener = addEventListener;
        WebSocket2.prototype.removeEventListener = removeEventListener;
        module.exports = WebSocket2;
        function initAsClient(websocket, address, protocols, options) {
            const opts = {
                allowSynchronousEvents: true,
                autoPong: true,
                protocolVersion: protocolVersions[1],
                maxPayload: 100 * 1024 * 1024,
                skipUTF8Validation: false,
                perMessageDeflate: true,
                followRedirects: false,
                maxRedirects: 10,
                ...options,
                socketPath: void 0,
                hostname: void 0,
                protocol: void 0,
                timeout: void 0,
                method: "GET",
                host: void 0,
                path: void 0,
                port: void 0
            };
            websocket._autoPong = opts.autoPong;
            if (!protocolVersions.includes(opts.protocolVersion)) {
                throw new RangeError(`Unsupported protocol version: ${opts.protocolVersion} (supported versions: ${protocolVersions.join(", ")})`);
            }
            let parsedUrl;
            if (address instanceof URL) {
                parsedUrl = address;
            } else {
                try {
                    parsedUrl = new URL(address);
                } catch (e) {
                    throw new SyntaxError(`Invalid URL: ${address}`);
                }
            }
            if (parsedUrl.protocol === "http:") {
                parsedUrl.protocol = "ws:";
            } else if (parsedUrl.protocol === "https:") {
                parsedUrl.protocol = "wss:";
            }
            websocket._url = parsedUrl.href;
            const isSecure = parsedUrl.protocol === "wss:";
            const isIpcUrl = parsedUrl.protocol === "ws+unix:";
            let invalidUrlMessage;
            if (parsedUrl.protocol !== "ws:" && !isSecure && !isIpcUrl) {
                invalidUrlMessage = `The URL's protocol must be one of "ws:", "wss:", "http:", "https", or "ws+unix:"`;
            } else if (isIpcUrl && !parsedUrl.pathname) {
                invalidUrlMessage = "The URL's pathname is empty";
            } else if (parsedUrl.hash) {
                invalidUrlMessage = "The URL contains a fragment identifier";
            }
            if (invalidUrlMessage) {
                const err = new SyntaxError(invalidUrlMessage);
                if (websocket._redirects === 0) {
                    throw err;
                } else {
                    emitErrorAndClose(websocket, err);
                    return;
                }
            }
            const defaultPort = isSecure ? 443 : 80;
            const key = randomBytes(16).toString("base64");
            const request = isSecure ? https.request : http.request;
            const protocolSet = /* @__PURE__ */ new Set();
            let perMessageDeflate;
            opts.createConnection = opts.createConnection || (isSecure ? tlsConnect : netConnect);
            opts.defaultPort = opts.defaultPort || defaultPort;
            opts.port = parsedUrl.port || defaultPort;
            opts.host = parsedUrl.hostname.startsWith("[") ? parsedUrl.hostname.slice(1, -1) : parsedUrl.hostname;
            opts.headers = {
                ...opts.headers,
                "Sec-WebSocket-Version": opts.protocolVersion,
                "Sec-WebSocket-Key": key,
                Connection: "Upgrade",
                Upgrade: "websocket"
            };
            opts.path = parsedUrl.pathname + parsedUrl.search;
            opts.timeout = opts.handshakeTimeout;
            if (opts.perMessageDeflate) {
                perMessageDeflate = new PerMessageDeflate(opts.perMessageDeflate !== true ? opts.perMessageDeflate : {}, false, opts.maxPayload);
                opts.headers["Sec-WebSocket-Extensions"] = format({
                    [PerMessageDeflate.extensionName]: perMessageDeflate.offer()
                });
            }
            if (protocols.length) {
                for (const protocol of protocols){
                    if (typeof protocol !== "string" || !subprotocolRegex.test(protocol) || protocolSet.has(protocol)) {
                        throw new SyntaxError("An invalid or duplicated subprotocol was specified");
                    }
                    protocolSet.add(protocol);
                }
                opts.headers["Sec-WebSocket-Protocol"] = protocols.join(",");
            }
            if (opts.origin) {
                if (opts.protocolVersion < 13) {
                    opts.headers["Sec-WebSocket-Origin"] = opts.origin;
                } else {
                    opts.headers.Origin = opts.origin;
                }
            }
            if (parsedUrl.username || parsedUrl.password) {
                opts.auth = `${parsedUrl.username}:${parsedUrl.password}`;
            }
            if (isIpcUrl) {
                const parts = opts.path.split(":");
                opts.socketPath = parts[0];
                opts.path = parts[1];
            }
            let req;
            if (opts.followRedirects) {
                if (websocket._redirects === 0) {
                    websocket._originalIpc = isIpcUrl;
                    websocket._originalSecure = isSecure;
                    websocket._originalHostOrSocketPath = isIpcUrl ? opts.socketPath : parsedUrl.host;
                    const headers = options && options.headers;
                    options = {
                        ...options,
                        headers: {}
                    };
                    if (headers) {
                        for (const [key2, value] of Object.entries(headers)){
                            options.headers[key2.toLowerCase()] = value;
                        }
                    }
                } else if (websocket.listenerCount("redirect") === 0) {
                    const isSameHost = isIpcUrl ? websocket._originalIpc ? opts.socketPath === websocket._originalHostOrSocketPath : false : websocket._originalIpc ? false : parsedUrl.host === websocket._originalHostOrSocketPath;
                    if (!isSameHost || websocket._originalSecure && !isSecure) {
                        delete opts.headers.authorization;
                        delete opts.headers.cookie;
                        if (!isSameHost) delete opts.headers.host;
                        opts.auth = void 0;
                    }
                }
                if (opts.auth && !options.headers.authorization) {
                    options.headers.authorization = "Basic " + Buffer.from(opts.auth).toString("base64");
                }
                req = websocket._req = request(opts);
                if (websocket._redirects) {
                    websocket.emit("redirect", websocket.url, req);
                }
            } else {
                req = websocket._req = request(opts);
            }
            if (opts.timeout) {
                req.on("timeout", ()=>{
                    abortHandshake(websocket, req, "Opening handshake has timed out");
                });
            }
            req.on("error", (err)=>{
                if (req === null || req[kAborted]) return;
                req = websocket._req = null;
                emitErrorAndClose(websocket, err);
            });
            req.on("response", (res)=>{
                const location = res.headers.location;
                const statusCode = res.statusCode;
                if (location && opts.followRedirects && statusCode >= 300 && statusCode < 400) {
                    if (++websocket._redirects > opts.maxRedirects) {
                        abortHandshake(websocket, req, "Maximum redirects exceeded");
                        return;
                    }
                    req.abort();
                    let addr;
                    try {
                        addr = new URL(location, address);
                    } catch (e) {
                        const err = new SyntaxError(`Invalid URL: ${location}`);
                        emitErrorAndClose(websocket, err);
                        return;
                    }
                    initAsClient(websocket, addr, protocols, options);
                } else if (!websocket.emit("unexpected-response", req, res)) {
                    abortHandshake(websocket, req, `Unexpected server response: ${res.statusCode}`);
                }
            });
            req.on("upgrade", (res, socket, head)=>{
                websocket.emit("upgrade", res);
                if (websocket.readyState !== WebSocket2.CONNECTING) return;
                req = websocket._req = null;
                const upgrade = res.headers.upgrade;
                if (upgrade === void 0 || upgrade.toLowerCase() !== "websocket") {
                    abortHandshake(websocket, socket, "Invalid Upgrade header");
                    return;
                }
                const digest = createHash("sha1").update(key + GUID).digest("base64");
                if (res.headers["sec-websocket-accept"] !== digest) {
                    abortHandshake(websocket, socket, "Invalid Sec-WebSocket-Accept header");
                    return;
                }
                const serverProt = res.headers["sec-websocket-protocol"];
                let protError;
                if (serverProt !== void 0) {
                    if (!protocolSet.size) {
                        protError = "Server sent a subprotocol but none was requested";
                    } else if (!protocolSet.has(serverProt)) {
                        protError = "Server sent an invalid subprotocol";
                    }
                } else if (protocolSet.size) {
                    protError = "Server sent no subprotocol";
                }
                if (protError) {
                    abortHandshake(websocket, socket, protError);
                    return;
                }
                if (serverProt) websocket._protocol = serverProt;
                const secWebSocketExtensions = res.headers["sec-websocket-extensions"];
                if (secWebSocketExtensions !== void 0) {
                    if (!perMessageDeflate) {
                        const message = "Server sent a Sec-WebSocket-Extensions header but no extension was requested";
                        abortHandshake(websocket, socket, message);
                        return;
                    }
                    let extensions;
                    try {
                        extensions = parse(secWebSocketExtensions);
                    } catch (err) {
                        const message = "Invalid Sec-WebSocket-Extensions header";
                        abortHandshake(websocket, socket, message);
                        return;
                    }
                    const extensionNames = Object.keys(extensions);
                    if (extensionNames.length !== 1 || extensionNames[0] !== PerMessageDeflate.extensionName) {
                        const message = "Server indicated an extension that was not requested";
                        abortHandshake(websocket, socket, message);
                        return;
                    }
                    try {
                        perMessageDeflate.accept(extensions[PerMessageDeflate.extensionName]);
                    } catch (err) {
                        const message = "Invalid Sec-WebSocket-Extensions header";
                        abortHandshake(websocket, socket, message);
                        return;
                    }
                    websocket._extensions[PerMessageDeflate.extensionName] = perMessageDeflate;
                }
                websocket.setSocket(socket, head, {
                    allowSynchronousEvents: opts.allowSynchronousEvents,
                    generateMask: opts.generateMask,
                    maxPayload: opts.maxPayload,
                    skipUTF8Validation: opts.skipUTF8Validation
                });
            });
            if (opts.finishRequest) {
                opts.finishRequest(req, websocket);
            } else {
                req.end();
            }
        }
        function emitErrorAndClose(websocket, err) {
            websocket._readyState = WebSocket2.CLOSING;
            websocket._errorEmitted = true;
            websocket.emit("error", err);
            websocket.emitClose();
        }
        function netConnect(options) {
            options.path = options.socketPath;
            return net.connect(options);
        }
        function tlsConnect(options) {
            options.path = void 0;
            if (!options.servername && options.servername !== "") {
                options.servername = net.isIP(options.host) ? "" : options.host;
            }
            return tls.connect(options);
        }
        function abortHandshake(websocket, stream, message) {
            websocket._readyState = WebSocket2.CLOSING;
            const err = new Error(message);
            Error.captureStackTrace(err, abortHandshake);
            if (stream.setHeader) {
                stream[kAborted] = true;
                stream.abort();
                if (stream.socket && !stream.socket.destroyed) {
                    stream.socket.destroy();
                }
                process.nextTick(emitErrorAndClose, websocket, err);
            } else {
                stream.destroy(err);
                stream.once("error", websocket.emit.bind(websocket, "error"));
                stream.once("close", websocket.emitClose.bind(websocket));
            }
        }
        function sendAfterClose(websocket, data, cb) {
            if (data) {
                const length = isBlob(data) ? data.size : toBuffer(data).length;
                if (websocket._socket) websocket._sender._bufferedBytes += length;
                else websocket._bufferedAmount += length;
            }
            if (cb) {
                const err = new Error(`WebSocket is not open: readyState ${websocket.readyState} (${readyStates[websocket.readyState]})`);
                process.nextTick(cb, err);
            }
        }
        function receiverOnConclude(code, reason) {
            const websocket = this[kWebSocket];
            websocket._closeFrameReceived = true;
            websocket._closeMessage = reason;
            websocket._closeCode = code;
            if (websocket._socket[kWebSocket] === void 0) return;
            websocket._socket.removeListener("data", socketOnData);
            process.nextTick(resume, websocket._socket);
            if (code === 1005) websocket.close();
            else websocket.close(code, reason);
        }
        function receiverOnDrain() {
            const websocket = this[kWebSocket];
            if (!websocket.isPaused) websocket._socket.resume();
        }
        function receiverOnError(err) {
            const websocket = this[kWebSocket];
            if (websocket._socket[kWebSocket] !== void 0) {
                websocket._socket.removeListener("data", socketOnData);
                process.nextTick(resume, websocket._socket);
                websocket.close(err[kStatusCode]);
            }
            if (!websocket._errorEmitted) {
                websocket._errorEmitted = true;
                websocket.emit("error", err);
            }
        }
        function receiverOnFinish() {
            this[kWebSocket].emitClose();
        }
        function receiverOnMessage(data, isBinary) {
            this[kWebSocket].emit("message", data, isBinary);
        }
        function receiverOnPing(data) {
            const websocket = this[kWebSocket];
            if (websocket._autoPong) websocket.pong(data, !this._isServer, NOOP);
            websocket.emit("ping", data);
        }
        function receiverOnPong(data) {
            this[kWebSocket].emit("pong", data);
        }
        function resume(stream) {
            stream.resume();
        }
        function senderOnError(err) {
            const websocket = this[kWebSocket];
            if (websocket.readyState === WebSocket2.CLOSED) return;
            if (websocket.readyState === WebSocket2.OPEN) {
                websocket._readyState = WebSocket2.CLOSING;
                setCloseTimer(websocket);
            }
            this._socket.end();
            if (!websocket._errorEmitted) {
                websocket._errorEmitted = true;
                websocket.emit("error", err);
            }
        }
        function setCloseTimer(websocket) {
            websocket._closeTimer = setTimeout(websocket._socket.destroy.bind(websocket._socket), closeTimeout);
        }
        function socketOnClose() {
            const websocket = this[kWebSocket];
            this.removeListener("close", socketOnClose);
            this.removeListener("data", socketOnData);
            this.removeListener("end", socketOnEnd);
            websocket._readyState = WebSocket2.CLOSING;
            let chunk;
            if (!this._readableState.endEmitted && !websocket._closeFrameReceived && !websocket._receiver._writableState.errorEmitted && (chunk = websocket._socket.read()) !== null) {
                websocket._receiver.write(chunk);
            }
            websocket._receiver.end();
            this[kWebSocket] = void 0;
            clearTimeout(websocket._closeTimer);
            if (websocket._receiver._writableState.finished || websocket._receiver._writableState.errorEmitted) {
                websocket.emitClose();
            } else {
                websocket._receiver.on("error", receiverOnFinish);
                websocket._receiver.on("finish", receiverOnFinish);
            }
        }
        function socketOnData(chunk) {
            if (!this[kWebSocket]._receiver.write(chunk)) {
                this.pause();
            }
        }
        function socketOnEnd() {
            const websocket = this[kWebSocket];
            websocket._readyState = WebSocket2.CLOSING;
            websocket._receiver.end();
            this.end();
        }
        function socketOnError() {
            const websocket = this[kWebSocket];
            this.removeListener("error", socketOnError);
            this.on("error", NOOP);
            if (websocket) {
                websocket._readyState = WebSocket2.CLOSING;
                this.destroy();
            }
        }
    }
});
// ../common/temp/node_modules/.pnpm/ws@8.18.0_bufferutil@4.0.9_utf-8-validate@5.0.10/node_modules/ws/lib/subprotocol.js
var require_subprotocol = __commonJS({
    "../common/temp/node_modules/.pnpm/ws@8.18.0_bufferutil@4.0.9_utf-8-validate@5.0.10/node_modules/ws/lib/subprotocol.js" (exports, module) {
        "use strict";
        var { tokenChars } = require_validation();
        function parse(header) {
            const protocols = /* @__PURE__ */ new Set();
            let start = -1;
            let end = -1;
            let i = 0;
            for(i; i < header.length; i++){
                const code = header.charCodeAt(i);
                if (end === -1 && tokenChars[code] === 1) {
                    if (start === -1) start = i;
                } else if (i !== 0 && (code === 32 || code === 9)) {
                    if (end === -1 && start !== -1) end = i;
                } else if (code === 44) {
                    if (start === -1) {
                        throw new SyntaxError(`Unexpected character at index ${i}`);
                    }
                    if (end === -1) end = i;
                    const protocol2 = header.slice(start, end);
                    if (protocols.has(protocol2)) {
                        throw new SyntaxError(`The "${protocol2}" subprotocol is duplicated`);
                    }
                    protocols.add(protocol2);
                    start = end = -1;
                } else {
                    throw new SyntaxError(`Unexpected character at index ${i}`);
                }
            }
            if (start === -1 || end !== -1) {
                throw new SyntaxError("Unexpected end of input");
            }
            const protocol = header.slice(start, i);
            if (protocols.has(protocol)) {
                throw new SyntaxError(`The "${protocol}" subprotocol is duplicated`);
            }
            protocols.add(protocol);
            return protocols;
        }
        module.exports = {
            parse
        };
    }
});
// ../common/temp/node_modules/.pnpm/ws@8.18.0_bufferutil@4.0.9_utf-8-validate@5.0.10/node_modules/ws/lib/websocket-server.js
var require_websocket_server = __commonJS({
    "../common/temp/node_modules/.pnpm/ws@8.18.0_bufferutil@4.0.9_utf-8-validate@5.0.10/node_modules/ws/lib/websocket-server.js" (exports, module) {
        "use strict";
        var EventEmitter = __require("events");
        var http = __require("http");
        var { Duplex } = __require("stream");
        var { createHash } = __require("crypto");
        var extension = require_extension();
        var PerMessageDeflate = require_permessage_deflate();
        var subprotocol = require_subprotocol();
        var WebSocket2 = require_websocket();
        var { GUID, kWebSocket } = require_constants();
        var keyRegex = /^[+/0-9A-Za-z]{22}==$/;
        var RUNNING = 0;
        var CLOSING = 1;
        var CLOSED = 2;
        var WebSocketServer2 = class extends EventEmitter {
            /**
       * Create a `WebSocketServer` instance.
       *
       * @param {Object} options Configuration options
       * @param {Boolean} [options.allowSynchronousEvents=true] Specifies whether
       *     any of the `'message'`, `'ping'`, and `'pong'` events can be emitted
       *     multiple times in the same tick
       * @param {Boolean} [options.autoPong=true] Specifies whether or not to
       *     automatically send a pong in response to a ping
       * @param {Number} [options.backlog=511] The maximum length of the queue of
       *     pending connections
       * @param {Boolean} [options.clientTracking=true] Specifies whether or not to
       *     track clients
       * @param {Function} [options.handleProtocols] A hook to handle protocols
       * @param {String} [options.host] The hostname where to bind the server
       * @param {Number} [options.maxPayload=104857600] The maximum allowed message
       *     size
       * @param {Boolean} [options.noServer=false] Enable no server mode
       * @param {String} [options.path] Accept only connections matching this path
       * @param {(Boolean|Object)} [options.perMessageDeflate=false] Enable/disable
       *     permessage-deflate
       * @param {Number} [options.port] The port where to bind the server
       * @param {(http.Server|https.Server)} [options.server] A pre-created HTTP/S
       *     server to use
       * @param {Boolean} [options.skipUTF8Validation=false] Specifies whether or
       *     not to skip UTF-8 validation for text and close messages
       * @param {Function} [options.verifyClient] A hook to reject connections
       * @param {Function} [options.WebSocket=WebSocket] Specifies the `WebSocket`
       *     class to use. It must be the `WebSocket` class or class that extends it
       * @param {Function} [callback] A listener for the `listening` event
       */ constructor(options, callback){
                super();
                options = {
                    allowSynchronousEvents: true,
                    autoPong: true,
                    maxPayload: 100 * 1024 * 1024,
                    skipUTF8Validation: false,
                    perMessageDeflate: false,
                    handleProtocols: null,
                    clientTracking: true,
                    verifyClient: null,
                    noServer: false,
                    backlog: null,
                    // use default (511 as implemented in net.js)
                    server: null,
                    host: null,
                    path: null,
                    port: null,
                    WebSocket: WebSocket2,
                    ...options
                };
                if (options.port == null && !options.server && !options.noServer || options.port != null && (options.server || options.noServer) || options.server && options.noServer) {
                    throw new TypeError('One and only one of the "port", "server", or "noServer" options must be specified');
                }
                if (options.port != null) {
                    this._server = http.createServer((req, res)=>{
                        const body = http.STATUS_CODES[426];
                        res.writeHead(426, {
                            "Content-Length": body.length,
                            "Content-Type": "text/plain"
                        });
                        res.end(body);
                    });
                    this._server.listen(options.port, options.host, options.backlog, callback);
                } else if (options.server) {
                    this._server = options.server;
                }
                if (this._server) {
                    const emitConnection = this.emit.bind(this, "connection");
                    this._removeListeners = addListeners(this._server, {
                        listening: this.emit.bind(this, "listening"),
                        error: this.emit.bind(this, "error"),
                        upgrade: (req, socket, head)=>{
                            this.handleUpgrade(req, socket, head, emitConnection);
                        }
                    });
                }
                if (options.perMessageDeflate === true) options.perMessageDeflate = {};
                if (options.clientTracking) {
                    this.clients = /* @__PURE__ */ new Set();
                    this._shouldEmitClose = false;
                }
                this.options = options;
                this._state = RUNNING;
            }
            /**
       * Returns the bound address, the address family name, and port of the server
       * as reported by the operating system if listening on an IP socket.
       * If the server is listening on a pipe or UNIX domain socket, the name is
       * returned as a string.
       *
       * @return {(Object|String|null)} The address of the server
       * @public
       */ address() {
                if (this.options.noServer) {
                    throw new Error('The server is operating in "noServer" mode');
                }
                if (!this._server) return null;
                return this._server.address();
            }
            /**
       * Stop the server from accepting new connections and emit the `'close'` event
       * when all existing connections are closed.
       *
       * @param {Function} [cb] A one-time listener for the `'close'` event
       * @public
       */ close(cb) {
                if (this._state === CLOSED) {
                    if (cb) {
                        this.once("close", ()=>{
                            cb(new Error("The server is not running"));
                        });
                    }
                    process.nextTick(emitClose, this);
                    return;
                }
                if (cb) this.once("close", cb);
                if (this._state === CLOSING) return;
                this._state = CLOSING;
                if (this.options.noServer || this.options.server) {
                    if (this._server) {
                        this._removeListeners();
                        this._removeListeners = this._server = null;
                    }
                    if (this.clients) {
                        if (!this.clients.size) {
                            process.nextTick(emitClose, this);
                        } else {
                            this._shouldEmitClose = true;
                        }
                    } else {
                        process.nextTick(emitClose, this);
                    }
                } else {
                    const server = this._server;
                    this._removeListeners();
                    this._removeListeners = this._server = null;
                    server.close(()=>{
                        emitClose(this);
                    });
                }
            }
            /**
       * See if a given request should be handled by this server instance.
       *
       * @param {http.IncomingMessage} req Request object to inspect
       * @return {Boolean} `true` if the request is valid, else `false`
       * @public
       */ shouldHandle(req) {
                if (this.options.path) {
                    const index = req.url.indexOf("?");
                    const pathname = index !== -1 ? req.url.slice(0, index) : req.url;
                    if (pathname !== this.options.path) return false;
                }
                return true;
            }
            /**
       * Handle a HTTP Upgrade request.
       *
       * @param {http.IncomingMessage} req The request object
       * @param {Duplex} socket The network socket between the server and client
       * @param {Buffer} head The first packet of the upgraded stream
       * @param {Function} cb Callback
       * @public
       */ handleUpgrade(req, socket, head, cb) {
                socket.on("error", socketOnError);
                const key = req.headers["sec-websocket-key"];
                const upgrade = req.headers.upgrade;
                const version = +req.headers["sec-websocket-version"];
                if (req.method !== "GET") {
                    const message = "Invalid HTTP method";
                    abortHandshakeOrEmitwsClientError(this, req, socket, 405, message);
                    return;
                }
                if (upgrade === void 0 || upgrade.toLowerCase() !== "websocket") {
                    const message = "Invalid Upgrade header";
                    abortHandshakeOrEmitwsClientError(this, req, socket, 400, message);
                    return;
                }
                if (key === void 0 || !keyRegex.test(key)) {
                    const message = "Missing or invalid Sec-WebSocket-Key header";
                    abortHandshakeOrEmitwsClientError(this, req, socket, 400, message);
                    return;
                }
                if (version !== 8 && version !== 13) {
                    const message = "Missing or invalid Sec-WebSocket-Version header";
                    abortHandshakeOrEmitwsClientError(this, req, socket, 400, message);
                    return;
                }
                if (!this.shouldHandle(req)) {
                    abortHandshake(socket, 400);
                    return;
                }
                const secWebSocketProtocol = req.headers["sec-websocket-protocol"];
                let protocols = /* @__PURE__ */ new Set();
                if (secWebSocketProtocol !== void 0) {
                    try {
                        protocols = subprotocol.parse(secWebSocketProtocol);
                    } catch (err) {
                        const message = "Invalid Sec-WebSocket-Protocol header";
                        abortHandshakeOrEmitwsClientError(this, req, socket, 400, message);
                        return;
                    }
                }
                const secWebSocketExtensions = req.headers["sec-websocket-extensions"];
                const extensions = {};
                if (this.options.perMessageDeflate && secWebSocketExtensions !== void 0) {
                    const perMessageDeflate = new PerMessageDeflate(this.options.perMessageDeflate, true, this.options.maxPayload);
                    try {
                        const offers = extension.parse(secWebSocketExtensions);
                        if (offers[PerMessageDeflate.extensionName]) {
                            perMessageDeflate.accept(offers[PerMessageDeflate.extensionName]);
                            extensions[PerMessageDeflate.extensionName] = perMessageDeflate;
                        }
                    } catch (err) {
                        const message = "Invalid or unacceptable Sec-WebSocket-Extensions header";
                        abortHandshakeOrEmitwsClientError(this, req, socket, 400, message);
                        return;
                    }
                }
                if (this.options.verifyClient) {
                    const info = {
                        origin: req.headers[`${version === 8 ? "sec-websocket-origin" : "origin"}`],
                        secure: !!(req.socket.authorized || req.socket.encrypted),
                        req
                    };
                    if (this.options.verifyClient.length === 2) {
                        this.options.verifyClient(info, (verified, code, message, headers)=>{
                            if (!verified) {
                                return abortHandshake(socket, code || 401, message, headers);
                            }
                            this.completeUpgrade(extensions, key, protocols, req, socket, head, cb);
                        });
                        return;
                    }
                    if (!this.options.verifyClient(info)) return abortHandshake(socket, 401);
                }
                this.completeUpgrade(extensions, key, protocols, req, socket, head, cb);
            }
            /**
       * Upgrade the connection to WebSocket.
       *
       * @param {Object} extensions The accepted extensions
       * @param {String} key The value of the `Sec-WebSocket-Key` header
       * @param {Set} protocols The subprotocols
       * @param {http.IncomingMessage} req The request object
       * @param {Duplex} socket The network socket between the server and client
       * @param {Buffer} head The first packet of the upgraded stream
       * @param {Function} cb Callback
       * @throws {Error} If called more than once with the same socket
       * @private
       */ completeUpgrade(extensions, key, protocols, req, socket, head, cb) {
                if (!socket.readable || !socket.writable) return socket.destroy();
                if (socket[kWebSocket]) {
                    throw new Error("server.handleUpgrade() was called more than once with the same socket, possibly due to a misconfiguration");
                }
                if (this._state > RUNNING) return abortHandshake(socket, 503);
                const digest = createHash("sha1").update(key + GUID).digest("base64");
                const headers = [
                    "HTTP/1.1 101 Switching Protocols",
                    "Upgrade: websocket",
                    "Connection: Upgrade",
                    `Sec-WebSocket-Accept: ${digest}`
                ];
                const ws = new this.options.WebSocket(null, void 0, this.options);
                if (protocols.size) {
                    const protocol = this.options.handleProtocols ? this.options.handleProtocols(protocols, req) : protocols.values().next().value;
                    if (protocol) {
                        headers.push(`Sec-WebSocket-Protocol: ${protocol}`);
                        ws._protocol = protocol;
                    }
                }
                if (extensions[PerMessageDeflate.extensionName]) {
                    const params = extensions[PerMessageDeflate.extensionName].params;
                    const value = extension.format({
                        [PerMessageDeflate.extensionName]: [
                            params
                        ]
                    });
                    headers.push(`Sec-WebSocket-Extensions: ${value}`);
                    ws._extensions = extensions;
                }
                this.emit("headers", headers, req);
                socket.write(headers.concat("\r\n").join("\r\n"));
                socket.removeListener("error", socketOnError);
                ws.setSocket(socket, head, {
                    allowSynchronousEvents: this.options.allowSynchronousEvents,
                    maxPayload: this.options.maxPayload,
                    skipUTF8Validation: this.options.skipUTF8Validation
                });
                if (this.clients) {
                    this.clients.add(ws);
                    ws.on("close", ()=>{
                        this.clients.delete(ws);
                        if (this._shouldEmitClose && !this.clients.size) {
                            process.nextTick(emitClose, this);
                        }
                    });
                }
                cb(ws, req);
            }
        };
        module.exports = WebSocketServer2;
        function addListeners(server, map) {
            for (const event of Object.keys(map))server.on(event, map[event]);
            return function removeListeners() {
                for (const event of Object.keys(map)){
                    server.removeListener(event, map[event]);
                }
            };
        }
        function emitClose(server) {
            server._state = CLOSED;
            server.emit("close");
        }
        function socketOnError() {
            this.destroy();
        }
        function abortHandshake(socket, code, message, headers) {
            message = message || http.STATUS_CODES[code];
            headers = {
                Connection: "close",
                "Content-Type": "text/html",
                "Content-Length": Buffer.byteLength(message),
                ...headers
            };
            socket.once("finish", socket.destroy);
            socket.end(`HTTP/1.1 ${code} ${http.STATUS_CODES[code]}\r
` + Object.keys(headers).map((h)=>`${h}: ${headers[h]}`).join("\r\n") + "\r\n\r\n" + message);
        }
        function abortHandshakeOrEmitwsClientError(server, req, socket, code, message) {
            if (server.listenerCount("wsClientError")) {
                const err = new Error(message);
                Error.captureStackTrace(err, abortHandshakeOrEmitwsClientError);
                server.emit("wsClientError", err, socket, req);
            } else {
                abortHandshake(socket, code, message);
            }
        }
    }
});
;
// ../common/temp/node_modules/.pnpm/ws@8.18.0_bufferutil@4.0.9_utf-8-validate@5.0.10/node_modules/ws/wrapper.mjs
var import_stream = __toESM(require_stream(), 1);
var import_receiver = __toESM(require_receiver(), 1);
var import_sender = __toESM(require_sender(), 1);
var import_websocket = __toESM(require_websocket(), 1);
var import_websocket_server = __toESM(require_websocket_server(), 1);
var wrapper_default = import_websocket.default;
// src/browser/simple_client-node.ts
var nodeWebSocket = wrapper_default;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$simple_client$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["setDefaultWebSocketConstructor"])(nodeWebSocket);
;
 //# sourceMappingURL=simple_client-node.js.map
}),
"[project]/node_modules/convex/dist/esm/browser/index-node.js [app-ssr] (ecmascript) <locals>", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$client$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/browser/sync/client.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$simple_client$2d$node$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/browser/simple_client-node.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$http_client$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/browser/http_client.js [app-ssr] (ecmascript)"); //# sourceMappingURL=index.js.map
"use strict";
;
;
;
}),
];

//# sourceMappingURL=node_modules_convex_dist_esm_51c478a6._.js.map