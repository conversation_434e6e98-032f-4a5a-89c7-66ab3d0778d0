"use client";

import { Brain, GripVertical } from "lucide-react";
import type { AiBlock as AiBlockT } from "../types";

export function AiBlock({ block, onDragStart, onDropInto, onContentChange }: {
  block: AiBlockT;
  onDragStart?: (e: React.DragEvent, id: string) => void;
  onDropInto?: (e: React.DragEvent<HTMLDivElement>, parentId: string) => void;
  onContentChange?: (id: string, html: string) => void;
}) {
  return (
    <div
      contentEditable={false}
      draggable
      onDragStart={(e) => onDragStart?.(e, block.id)}
      className="my-2 p-3 rounded-lg border border-orange-300 bg-orange-50 text-orange-800 cursor-grab"
      data-id={block.id}
    >
      <div className="flex items-center gap-2 font-semibold mb-2">
        <GripVertical size={16} className="text-muted-foreground" />
        <Brain size={16} /> AI Instruction
      </div>
      <div
        onDrop={(e) => onDropInto?.(e, block.id)}
        onDragOver={(e) => { e.preventDefault(); e.stopPropagation(); }}
        onInput={(e) => onContentChange?.(block.id, (e.currentTarget as HTMLDivElement).innerHTML)}
        contentEditable
        suppressContentEditableWarning
        dir="ltr"
        data-placeholder="Write instructions and drag Findings here..."
        className="w-full p-2 border rounded bg-white text-foreground min-h-[56px] focus:outline-none"
        dangerouslySetInnerHTML={{ __html: block.innerContent }}
      />
    </div>
  );
}

