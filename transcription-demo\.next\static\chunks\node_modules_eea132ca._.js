(globalThis.TURBOPACK || (globalThis.TURBOPACK = [])).push([typeof document === "object" ? document.currentScript : undefined,
"[project]/node_modules/next/dist/shared/lib/router/utils/querystring.js [app-client] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

Object.defineProperty(exports, "__esModule", {
    value: true
});
0 && (module.exports = {
    assign: null,
    searchParamsToUrlQuery: null,
    urlQueryToSearchParams: null
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    assign: function() {
        return assign;
    },
    searchParamsToUrlQuery: function() {
        return searchParamsToUrlQuery;
    },
    urlQueryToSearchParams: function() {
        return urlQueryToSearchParams;
    }
});
function searchParamsToUrlQuery(searchParams) {
    const query = {};
    for (const [key, value] of searchParams.entries()){
        const existing = query[key];
        if (typeof existing === 'undefined') {
            query[key] = value;
        } else if (Array.isArray(existing)) {
            existing.push(value);
        } else {
            query[key] = [
                existing,
                value
            ];
        }
    }
    return query;
}
function stringifyUrlQueryParam(param) {
    if (typeof param === 'string') {
        return param;
    }
    if (typeof param === 'number' && !isNaN(param) || typeof param === 'boolean') {
        return String(param);
    } else {
        return '';
    }
}
function urlQueryToSearchParams(query) {
    const searchParams = new URLSearchParams();
    for (const [key, value] of Object.entries(query)){
        if (Array.isArray(value)) {
            for (const item of value){
                searchParams.append(key, stringifyUrlQueryParam(item));
            }
        } else {
            searchParams.set(key, stringifyUrlQueryParam(value));
        }
    }
    return searchParams;
}
function assign(target) {
    for(var _len = arguments.length, searchParamsList = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){
        searchParamsList[_key - 1] = arguments[_key];
    }
    for (const searchParams of searchParamsList){
        for (const key of searchParams.keys()){
            target.delete(key);
        }
        for (const [key, value] of searchParams.entries()){
            target.append(key, value);
        }
    }
    return target;
} //# sourceMappingURL=querystring.js.map
}),
"[project]/node_modules/next/dist/shared/lib/router/utils/format-url.js [app-client] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

// Format function modified from nodejs
// Copyright Joyent, Inc. and other Node contributors.
//
// Permission is hereby granted, free of charge, to any person obtaining a
// copy of this software and associated documentation files (the
// "Software"), to deal in the Software without restriction, including
// without limitation the rights to use, copy, modify, merge, publish,
// distribute, sublicense, and/or sell copies of the Software, and to permit
// persons to whom the Software is furnished to do so, subject to the
// following conditions:
//
// The above copyright notice and this permission notice shall be included
// in all copies or substantial portions of the Software.
//
// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS
// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN
// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,
// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR
// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE
// USE OR OTHER DEALINGS IN THE SOFTWARE.
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = /*#__PURE__*/ __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
0 && (module.exports = {
    formatUrl: null,
    formatWithValidation: null,
    urlObjectKeys: null
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    formatUrl: function() {
        return formatUrl;
    },
    formatWithValidation: function() {
        return formatWithValidation;
    },
    urlObjectKeys: function() {
        return urlObjectKeys;
    }
});
const _interop_require_wildcard = __turbopack_context__.r("[project]/node_modules/@swc/helpers/cjs/_interop_require_wildcard.cjs [app-client] (ecmascript)");
const _querystring = /*#__PURE__*/ _interop_require_wildcard._(__turbopack_context__.r("[project]/node_modules/next/dist/shared/lib/router/utils/querystring.js [app-client] (ecmascript)"));
const slashedProtocols = /https?|ftp|gopher|file/;
function formatUrl(urlObj) {
    let { auth, hostname } = urlObj;
    let protocol = urlObj.protocol || '';
    let pathname = urlObj.pathname || '';
    let hash = urlObj.hash || '';
    let query = urlObj.query || '';
    let host = false;
    auth = auth ? encodeURIComponent(auth).replace(/%3A/i, ':') + '@' : '';
    if (urlObj.host) {
        host = auth + urlObj.host;
    } else if (hostname) {
        host = auth + (~hostname.indexOf(':') ? "[" + hostname + "]" : hostname);
        if (urlObj.port) {
            host += ':' + urlObj.port;
        }
    }
    if (query && typeof query === 'object') {
        query = String(_querystring.urlQueryToSearchParams(query));
    }
    let search = urlObj.search || query && "?" + query || '';
    if (protocol && !protocol.endsWith(':')) protocol += ':';
    if (urlObj.slashes || (!protocol || slashedProtocols.test(protocol)) && host !== false) {
        host = '//' + (host || '');
        if (pathname && pathname[0] !== '/') pathname = '/' + pathname;
    } else if (!host) {
        host = '';
    }
    if (hash && hash[0] !== '#') hash = '#' + hash;
    if (search && search[0] !== '?') search = '?' + search;
    pathname = pathname.replace(/[?#]/g, encodeURIComponent);
    search = search.replace('#', '%23');
    return "" + protocol + host + pathname + search + hash;
}
const urlObjectKeys = [
    'auth',
    'hash',
    'host',
    'hostname',
    'href',
    'path',
    'pathname',
    'port',
    'protocol',
    'query',
    'search',
    'slashes'
];
function formatWithValidation(url) {
    if ("TURBOPACK compile-time truthy", 1) {
        if (url !== null && typeof url === 'object') {
            Object.keys(url).forEach((key)=>{
                if (!urlObjectKeys.includes(key)) {
                    console.warn("Unknown key passed via urlObject into url.format: " + key);
                }
            });
        }
    }
    return formatUrl(url);
} //# sourceMappingURL=format-url.js.map
}),
"[project]/node_modules/next/dist/client/use-merged-ref.js [app-client] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "useMergedRef", {
    enumerable: true,
    get: function() {
        return useMergedRef;
    }
});
const _react = __turbopack_context__.r("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
function useMergedRef(refA, refB) {
    const cleanupA = (0, _react.useRef)(null);
    const cleanupB = (0, _react.useRef)(null);
    // NOTE: In theory, we could skip the wrapping if only one of the refs is non-null.
    // (this happens often if the user doesn't pass a ref to Link/Form/Image)
    // But this can cause us to leak a cleanup-ref into user code (e.g. via `<Link legacyBehavior>`),
    // and the user might pass that ref into ref-merging library that doesn't support cleanup refs
    // (because it hasn't been updated for React 19)
    // which can then cause things to blow up, because a cleanup-returning ref gets called with `null`.
    // So in practice, it's safer to be defensive and always wrap the ref, even on React 19.
    return (0, _react.useCallback)((current)=>{
        if (current === null) {
            const cleanupFnA = cleanupA.current;
            if (cleanupFnA) {
                cleanupA.current = null;
                cleanupFnA();
            }
            const cleanupFnB = cleanupB.current;
            if (cleanupFnB) {
                cleanupB.current = null;
                cleanupFnB();
            }
        } else {
            if (refA) {
                cleanupA.current = applyRef(refA, current);
            }
            if (refB) {
                cleanupB.current = applyRef(refB, current);
            }
        }
    }, [
        refA,
        refB
    ]);
}
function applyRef(refA, current) {
    if (typeof refA === 'function') {
        const cleanup = refA(current);
        if (typeof cleanup === 'function') {
            return cleanup;
        } else {
            return ()=>refA(null);
        }
    } else {
        refA.current = current;
        return ()=>{
            refA.current = null;
        };
    }
}
if ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {
    Object.defineProperty(exports.default, '__esModule', {
        value: true
    });
    Object.assign(exports.default, exports);
    module.exports = exports.default;
} //# sourceMappingURL=use-merged-ref.js.map
}),
"[project]/node_modules/next/dist/shared/lib/utils.js [app-client] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = /*#__PURE__*/ __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
0 && (module.exports = {
    DecodeError: null,
    MiddlewareNotFoundError: null,
    MissingStaticPage: null,
    NormalizeError: null,
    PageNotFoundError: null,
    SP: null,
    ST: null,
    WEB_VITALS: null,
    execOnce: null,
    getDisplayName: null,
    getLocationOrigin: null,
    getURL: null,
    isAbsoluteUrl: null,
    isResSent: null,
    loadGetInitialProps: null,
    normalizeRepeatedSlashes: null,
    stringifyError: null
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    DecodeError: function() {
        return DecodeError;
    },
    MiddlewareNotFoundError: function() {
        return MiddlewareNotFoundError;
    },
    MissingStaticPage: function() {
        return MissingStaticPage;
    },
    NormalizeError: function() {
        return NormalizeError;
    },
    PageNotFoundError: function() {
        return PageNotFoundError;
    },
    SP: function() {
        return SP;
    },
    ST: function() {
        return ST;
    },
    WEB_VITALS: function() {
        return WEB_VITALS;
    },
    execOnce: function() {
        return execOnce;
    },
    getDisplayName: function() {
        return getDisplayName;
    },
    getLocationOrigin: function() {
        return getLocationOrigin;
    },
    getURL: function() {
        return getURL;
    },
    isAbsoluteUrl: function() {
        return isAbsoluteUrl;
    },
    isResSent: function() {
        return isResSent;
    },
    loadGetInitialProps: function() {
        return loadGetInitialProps;
    },
    normalizeRepeatedSlashes: function() {
        return normalizeRepeatedSlashes;
    },
    stringifyError: function() {
        return stringifyError;
    }
});
const WEB_VITALS = [
    'CLS',
    'FCP',
    'FID',
    'INP',
    'LCP',
    'TTFB'
];
function execOnce(fn) {
    let used = false;
    let result;
    return function() {
        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){
            args[_key] = arguments[_key];
        }
        if (!used) {
            used = true;
            result = fn(...args);
        }
        return result;
    };
}
// Scheme: https://tools.ietf.org/html/rfc3986#section-3.1
// Absolute URL: https://tools.ietf.org/html/rfc3986#section-4.3
const ABSOLUTE_URL_REGEX = /^[a-zA-Z][a-zA-Z\d+\-.]*?:/;
const isAbsoluteUrl = (url)=>ABSOLUTE_URL_REGEX.test(url);
function getLocationOrigin() {
    const { protocol, hostname, port } = window.location;
    return protocol + "//" + hostname + (port ? ':' + port : '');
}
function getURL() {
    const { href } = window.location;
    const origin = getLocationOrigin();
    return href.substring(origin.length);
}
function getDisplayName(Component) {
    return typeof Component === 'string' ? Component : Component.displayName || Component.name || 'Unknown';
}
function isResSent(res) {
    return res.finished || res.headersSent;
}
function normalizeRepeatedSlashes(url) {
    const urlParts = url.split('?');
    const urlNoQuery = urlParts[0];
    return urlNoQuery // first we replace any non-encoded backslashes with forward
    // then normalize repeated forward slashes
    .replace(/\\/g, '/').replace(/\/\/+/g, '/') + (urlParts[1] ? "?" + urlParts.slice(1).join('?') : '');
}
async function loadGetInitialProps(App, ctx) {
    if ("TURBOPACK compile-time truthy", 1) {
        var _App_prototype;
        if ((_App_prototype = App.prototype) == null ? void 0 : _App_prototype.getInitialProps) {
            const message = '"' + getDisplayName(App) + '.getInitialProps()" is defined as an instance method - visit https://nextjs.org/docs/messages/get-initial-props-as-an-instance-method for more information.';
            throw Object.defineProperty(new Error(message), "__NEXT_ERROR_CODE", {
                value: "E394",
                enumerable: false,
                configurable: true
            });
        }
    }
    // when called from _app `ctx` is nested in `ctx`
    const res = ctx.res || ctx.ctx && ctx.ctx.res;
    if (!App.getInitialProps) {
        if (ctx.ctx && ctx.Component) {
            // @ts-ignore pageProps default
            return {
                pageProps: await loadGetInitialProps(ctx.Component, ctx.ctx)
            };
        }
        return {};
    }
    const props = await App.getInitialProps(ctx);
    if (res && isResSent(res)) {
        return props;
    }
    if (!props) {
        const message = '"' + getDisplayName(App) + '.getInitialProps()" should resolve to an object. But found "' + props + '" instead.';
        throw Object.defineProperty(new Error(message), "__NEXT_ERROR_CODE", {
            value: "E394",
            enumerable: false,
            configurable: true
        });
    }
    if ("TURBOPACK compile-time truthy", 1) {
        if (Object.keys(props).length === 0 && !ctx.ctx) {
            console.warn("" + getDisplayName(App) + " returned an empty object from `getInitialProps`. This de-optimizes and prevents automatic static optimization. https://nextjs.org/docs/messages/empty-object-getInitialProps");
        }
    }
    return props;
}
const SP = typeof performance !== 'undefined';
const ST = SP && [
    'mark',
    'measure',
    'getEntriesByName'
].every((method)=>typeof performance[method] === 'function');
class DecodeError extends Error {
}
class NormalizeError extends Error {
}
class PageNotFoundError extends Error {
    constructor(page){
        super();
        this.code = 'ENOENT';
        this.name = 'PageNotFoundError';
        this.message = "Cannot find module for page: " + page;
    }
}
class MissingStaticPage extends Error {
    constructor(page, message){
        super();
        this.message = "Failed to load static file for page: " + page + " " + message;
    }
}
class MiddlewareNotFoundError extends Error {
    constructor(){
        super();
        this.code = 'ENOENT';
        this.message = "Cannot find the middleware module";
    }
}
function stringifyError(error) {
    return JSON.stringify({
        message: error.message,
        stack: error.stack
    });
} //# sourceMappingURL=utils.js.map
}),
"[project]/node_modules/next/dist/shared/lib/router/utils/is-local-url.js [app-client] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "isLocalURL", {
    enumerable: true,
    get: function() {
        return isLocalURL;
    }
});
const _utils = __turbopack_context__.r("[project]/node_modules/next/dist/shared/lib/utils.js [app-client] (ecmascript)");
const _hasbasepath = __turbopack_context__.r("[project]/node_modules/next/dist/client/has-base-path.js [app-client] (ecmascript)");
function isLocalURL(url) {
    // prevent a hydration mismatch on href for url with anchor refs
    if (!(0, _utils.isAbsoluteUrl)(url)) return true;
    try {
        // absolute urls can be local if they are on the same origin
        const locationOrigin = (0, _utils.getLocationOrigin)();
        const resolved = new URL(url, locationOrigin);
        return resolved.origin === locationOrigin && (0, _hasbasepath.hasBasePath)(resolved.pathname);
    } catch (_) {
        return false;
    }
} //# sourceMappingURL=is-local-url.js.map
}),
"[project]/node_modules/next/dist/shared/lib/utils/error-once.js [app-client] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = /*#__PURE__*/ __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "errorOnce", {
    enumerable: true,
    get: function() {
        return errorOnce;
    }
});
let errorOnce = (_)=>{};
if ("TURBOPACK compile-time truthy", 1) {
    const errors = new Set();
    errorOnce = (msg)=>{
        if (!errors.has(msg)) {
            console.error(msg);
        }
        errors.add(msg);
    };
} //# sourceMappingURL=error-once.js.map
}),
"[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = /*#__PURE__*/ __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
'use client';
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
0 && (module.exports = {
    default: null,
    useLinkStatus: null
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    /**
 * A React component that extends the HTML `<a>` element to provide
 * [prefetching](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#2-prefetching)
 * and client-side navigation. This is the primary way to navigate between routes in Next.js.
 *
 * @remarks
 * - Prefetching is only enabled in production.
 *
 * @see https://nextjs.org/docs/app/api-reference/components/link
 */ default: function() {
        return LinkComponent;
    },
    useLinkStatus: function() {
        return useLinkStatus;
    }
});
const _interop_require_wildcard = __turbopack_context__.r("[project]/node_modules/@swc/helpers/cjs/_interop_require_wildcard.cjs [app-client] (ecmascript)");
const _jsxruntime = __turbopack_context__.r("[project]/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
const _react = /*#__PURE__*/ _interop_require_wildcard._(__turbopack_context__.r("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)"));
const _formaturl = __turbopack_context__.r("[project]/node_modules/next/dist/shared/lib/router/utils/format-url.js [app-client] (ecmascript)");
const _approutercontextsharedruntime = __turbopack_context__.r("[project]/node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js [app-client] (ecmascript)");
const _usemergedref = __turbopack_context__.r("[project]/node_modules/next/dist/client/use-merged-ref.js [app-client] (ecmascript)");
const _utils = __turbopack_context__.r("[project]/node_modules/next/dist/shared/lib/utils.js [app-client] (ecmascript)");
const _addbasepath = __turbopack_context__.r("[project]/node_modules/next/dist/client/add-base-path.js [app-client] (ecmascript)");
const _warnonce = __turbopack_context__.r("[project]/node_modules/next/dist/shared/lib/utils/warn-once.js [app-client] (ecmascript)");
const _links = __turbopack_context__.r("[project]/node_modules/next/dist/client/components/links.js [app-client] (ecmascript)");
const _islocalurl = __turbopack_context__.r("[project]/node_modules/next/dist/shared/lib/router/utils/is-local-url.js [app-client] (ecmascript)");
const _approuterinstance = __turbopack_context__.r("[project]/node_modules/next/dist/client/components/app-router-instance.js [app-client] (ecmascript)");
const _erroronce = __turbopack_context__.r("[project]/node_modules/next/dist/shared/lib/utils/error-once.js [app-client] (ecmascript)");
const _segmentcache = __turbopack_context__.r("[project]/node_modules/next/dist/client/components/segment-cache.js [app-client] (ecmascript)");
function isModifiedEvent(event) {
    const eventTarget = event.currentTarget;
    const target = eventTarget.getAttribute('target');
    return target && target !== '_self' || event.metaKey || event.ctrlKey || event.shiftKey || event.altKey || // triggers resource download
    event.nativeEvent && event.nativeEvent.which === 2;
}
function linkClicked(e, href, as, linkInstanceRef, replace, scroll, onNavigate) {
    const { nodeName } = e.currentTarget;
    // anchors inside an svg have a lowercase nodeName
    const isAnchorNodeName = nodeName.toUpperCase() === 'A';
    if (isAnchorNodeName && isModifiedEvent(e) || e.currentTarget.hasAttribute('download')) {
        // ignore click for browser’s default behavior
        return;
    }
    if (!(0, _islocalurl.isLocalURL)(href)) {
        if (replace) {
            // browser default behavior does not replace the history state
            // so we need to do it manually
            e.preventDefault();
            location.replace(href);
        }
        // ignore click for browser’s default behavior
        return;
    }
    e.preventDefault();
    if (onNavigate) {
        let isDefaultPrevented = false;
        onNavigate({
            preventDefault: ()=>{
                isDefaultPrevented = true;
            }
        });
        if (isDefaultPrevented) {
            return;
        }
    }
    _react.default.startTransition(()=>{
        (0, _approuterinstance.dispatchNavigateAction)(as || href, replace ? 'replace' : 'push', scroll != null ? scroll : true, linkInstanceRef.current);
    });
}
function formatStringOrUrl(urlObjOrString) {
    if (typeof urlObjOrString === 'string') {
        return urlObjOrString;
    }
    return (0, _formaturl.formatUrl)(urlObjOrString);
}
function LinkComponent(props) {
    const [linkStatus, setOptimisticLinkStatus] = (0, _react.useOptimistic)(_links.IDLE_LINK_STATUS);
    let children;
    const linkInstanceRef = (0, _react.useRef)(null);
    const { href: hrefProp, as: asProp, children: childrenProp, prefetch: prefetchProp = null, passHref, replace, shallow, scroll, onClick, onMouseEnter: onMouseEnterProp, onTouchStart: onTouchStartProp, legacyBehavior = false, onNavigate, ref: forwardedRef, unstable_dynamicOnHover, ...restProps } = props;
    children = childrenProp;
    if (legacyBehavior && (typeof children === 'string' || typeof children === 'number')) {
        children = /*#__PURE__*/ (0, _jsxruntime.jsx)("a", {
            children: children
        });
    }
    const router = _react.default.useContext(_approutercontextsharedruntime.AppRouterContext);
    const prefetchEnabled = prefetchProp !== false;
    const fetchStrategy = prefetchProp !== false ? getFetchStrategyFromPrefetchProp(prefetchProp) : _segmentcache.FetchStrategy.PPR;
    if ("TURBOPACK compile-time truthy", 1) {
        function createPropError(args) {
            return Object.defineProperty(new Error("Failed prop type: The prop `" + args.key + "` expects a " + args.expected + " in `<Link>`, but got `" + args.actual + "` instead." + (typeof window !== 'undefined' ? "\nOpen your browser's console to view the Component stack trace." : '')), "__NEXT_ERROR_CODE", {
                value: "E319",
                enumerable: false,
                configurable: true
            });
        }
        // TypeScript trick for type-guarding:
        const requiredPropsGuard = {
            href: true
        };
        const requiredProps = Object.keys(requiredPropsGuard);
        requiredProps.forEach((key)=>{
            if (key === 'href') {
                if (props[key] == null || typeof props[key] !== 'string' && typeof props[key] !== 'object') {
                    throw createPropError({
                        key,
                        expected: '`string` or `object`',
                        actual: props[key] === null ? 'null' : typeof props[key]
                    });
                }
            } else {
                // TypeScript trick for type-guarding:
                // eslint-disable-next-line @typescript-eslint/no-unused-vars
                const _ = key;
            }
        });
        // TypeScript trick for type-guarding:
        const optionalPropsGuard = {
            as: true,
            replace: true,
            scroll: true,
            shallow: true,
            passHref: true,
            prefetch: true,
            unstable_dynamicOnHover: true,
            onClick: true,
            onMouseEnter: true,
            onTouchStart: true,
            legacyBehavior: true,
            onNavigate: true
        };
        const optionalProps = Object.keys(optionalPropsGuard);
        optionalProps.forEach((key)=>{
            const valType = typeof props[key];
            if (key === 'as') {
                if (props[key] && valType !== 'string' && valType !== 'object') {
                    throw createPropError({
                        key,
                        expected: '`string` or `object`',
                        actual: valType
                    });
                }
            } else if (key === 'onClick' || key === 'onMouseEnter' || key === 'onTouchStart' || key === 'onNavigate') {
                if (props[key] && valType !== 'function') {
                    throw createPropError({
                        key,
                        expected: '`function`',
                        actual: valType
                    });
                }
            } else if (key === 'replace' || key === 'scroll' || key === 'shallow' || key === 'passHref' || key === 'legacyBehavior' || key === 'unstable_dynamicOnHover') {
                if (props[key] != null && valType !== 'boolean') {
                    throw createPropError({
                        key,
                        expected: '`boolean`',
                        actual: valType
                    });
                }
            } else if (key === 'prefetch') {
                if (props[key] != null && valType !== 'boolean' && props[key] !== 'auto' && props[key] !== 'unstable_forceStale') {
                    throw createPropError({
                        key,
                        expected: '`boolean | "auto" | "unstable_forceStale"`',
                        actual: valType
                    });
                }
            } else {
                // TypeScript trick for type-guarding:
                // eslint-disable-next-line @typescript-eslint/no-unused-vars
                const _ = key;
            }
        });
    }
    if ("TURBOPACK compile-time truthy", 1) {
        if (props.locale) {
            (0, _warnonce.warnOnce)('The `locale` prop is not supported in `next/link` while using the `app` router. Read more about app router internalization: https://nextjs.org/docs/app/building-your-application/routing/internationalization');
        }
        if (!asProp) {
            let href;
            if (typeof hrefProp === 'string') {
                href = hrefProp;
            } else if (typeof hrefProp === 'object' && typeof hrefProp.pathname === 'string') {
                href = hrefProp.pathname;
            }
            if (href) {
                const hasDynamicSegment = href.split('/').some((segment)=>segment.startsWith('[') && segment.endsWith(']'));
                if (hasDynamicSegment) {
                    throw Object.defineProperty(new Error("Dynamic href `" + href + "` found in <Link> while using the `/app` router, this is not supported. Read more: https://nextjs.org/docs/messages/app-dir-dynamic-href"), "__NEXT_ERROR_CODE", {
                        value: "E267",
                        enumerable: false,
                        configurable: true
                    });
                }
            }
        }
    }
    const { href, as } = _react.default.useMemo({
        "LinkComponent.useMemo": ()=>{
            const resolvedHref = formatStringOrUrl(hrefProp);
            return {
                href: resolvedHref,
                as: asProp ? formatStringOrUrl(asProp) : resolvedHref
            };
        }
    }["LinkComponent.useMemo"], [
        hrefProp,
        asProp
    ]);
    // This will return the first child, if multiple are provided it will throw an error
    let child;
    if (legacyBehavior) {
        if ("TURBOPACK compile-time truthy", 1) {
            if (onClick) {
                console.warn('"onClick" was passed to <Link> with `href` of `' + hrefProp + '` but "legacyBehavior" was set. The legacy behavior requires onClick be set on the child of next/link');
            }
            if (onMouseEnterProp) {
                console.warn('"onMouseEnter" was passed to <Link> with `href` of `' + hrefProp + '` but "legacyBehavior" was set. The legacy behavior requires onMouseEnter be set on the child of next/link');
            }
            try {
                child = _react.default.Children.only(children);
            } catch (err) {
                if (!children) {
                    throw Object.defineProperty(new Error("No children were passed to <Link> with `href` of `" + hrefProp + "` but one child is required https://nextjs.org/docs/messages/link-no-children"), "__NEXT_ERROR_CODE", {
                        value: "E320",
                        enumerable: false,
                        configurable: true
                    });
                }
                throw Object.defineProperty(new Error("Multiple children were passed to <Link> with `href` of `" + hrefProp + "` but only one child is supported https://nextjs.org/docs/messages/link-multiple-children" + (typeof window !== 'undefined' ? " \nOpen your browser's console to view the Component stack trace." : '')), "__NEXT_ERROR_CODE", {
                    value: "E266",
                    enumerable: false,
                    configurable: true
                });
            }
        } else //TURBOPACK unreachable
        ;
    } else {
        if ("TURBOPACK compile-time truthy", 1) {
            if ((children == null ? void 0 : children.type) === 'a') {
                throw Object.defineProperty(new Error('Invalid <Link> with <a> child. Please remove <a> or use <Link legacyBehavior>.\nLearn more: https://nextjs.org/docs/messages/invalid-new-link-with-extra-anchor'), "__NEXT_ERROR_CODE", {
                    value: "E209",
                    enumerable: false,
                    configurable: true
                });
            }
        }
    }
    const childRef = legacyBehavior ? child && typeof child === 'object' && child.ref : forwardedRef;
    // Use a callback ref to attach an IntersectionObserver to the anchor tag on
    // mount. In the future we will also use this to keep track of all the
    // currently mounted <Link> instances, e.g. so we can re-prefetch them after
    // a revalidation or refresh.
    const observeLinkVisibilityOnMount = _react.default.useCallback({
        "LinkComponent.useCallback[observeLinkVisibilityOnMount]": (element)=>{
            if (router !== null) {
                linkInstanceRef.current = (0, _links.mountLinkInstance)(element, href, router, fetchStrategy, prefetchEnabled, setOptimisticLinkStatus);
            }
            return ({
                "LinkComponent.useCallback[observeLinkVisibilityOnMount]": ()=>{
                    if (linkInstanceRef.current) {
                        (0, _links.unmountLinkForCurrentNavigation)(linkInstanceRef.current);
                        linkInstanceRef.current = null;
                    }
                    (0, _links.unmountPrefetchableInstance)(element);
                }
            })["LinkComponent.useCallback[observeLinkVisibilityOnMount]"];
        }
    }["LinkComponent.useCallback[observeLinkVisibilityOnMount]"], [
        prefetchEnabled,
        href,
        router,
        fetchStrategy,
        setOptimisticLinkStatus
    ]);
    const mergedRef = (0, _usemergedref.useMergedRef)(observeLinkVisibilityOnMount, childRef);
    const childProps = {
        ref: mergedRef,
        onClick (e) {
            if ("TURBOPACK compile-time truthy", 1) {
                if (!e) {
                    throw Object.defineProperty(new Error('Component rendered inside next/link has to pass click event to "onClick" prop.'), "__NEXT_ERROR_CODE", {
                        value: "E312",
                        enumerable: false,
                        configurable: true
                    });
                }
            }
            if (!legacyBehavior && typeof onClick === 'function') {
                onClick(e);
            }
            if (legacyBehavior && child.props && typeof child.props.onClick === 'function') {
                child.props.onClick(e);
            }
            if (!router) {
                return;
            }
            if (e.defaultPrevented) {
                return;
            }
            linkClicked(e, href, as, linkInstanceRef, replace, scroll, onNavigate);
        },
        onMouseEnter (e) {
            if (!legacyBehavior && typeof onMouseEnterProp === 'function') {
                onMouseEnterProp(e);
            }
            if (legacyBehavior && child.props && typeof child.props.onMouseEnter === 'function') {
                child.props.onMouseEnter(e);
            }
            if (!router) {
                return;
            }
            if ("TURBOPACK compile-time truthy", 1) {
                return;
            }
            //TURBOPACK unreachable
            ;
            const upgradeToDynamicPrefetch = undefined;
        },
        onTouchStart: ("TURBOPACK compile-time falsy", 0) ? "TURBOPACK unreachable" : function onTouchStart(e) {
            if (!legacyBehavior && typeof onTouchStartProp === 'function') {
                onTouchStartProp(e);
            }
            if (legacyBehavior && child.props && typeof child.props.onTouchStart === 'function') {
                child.props.onTouchStart(e);
            }
            if (!router) {
                return;
            }
            if (!prefetchEnabled) {
                return;
            }
            const upgradeToDynamicPrefetch = unstable_dynamicOnHover === true;
            (0, _links.onNavigationIntent)(e.currentTarget, upgradeToDynamicPrefetch);
        }
    };
    // If child is an <a> tag and doesn't have a href attribute, or if the 'passHref' property is
    // defined, we specify the current 'href', so that repetition is not needed by the user.
    // If the url is absolute, we can bypass the logic to prepend the basePath.
    if ((0, _utils.isAbsoluteUrl)(as)) {
        childProps.href = as;
    } else if (!legacyBehavior || passHref || child.type === 'a' && !('href' in child.props)) {
        childProps.href = (0, _addbasepath.addBasePath)(as);
    }
    let link;
    if (legacyBehavior) {
        if ("TURBOPACK compile-time truthy", 1) {
            (0, _erroronce.errorOnce)('`legacyBehavior` is deprecated and will be removed in a future ' + 'release. A codemod is available to upgrade your components:\n\n' + 'npx @next/codemod@latest new-link .\n\n' + 'Learn more: https://nextjs.org/docs/app/building-your-application/upgrading/codemods#remove-a-tags-from-link-components');
        }
        link = /*#__PURE__*/ _react.default.cloneElement(child, childProps);
    } else {
        link = /*#__PURE__*/ (0, _jsxruntime.jsx)("a", {
            ...restProps,
            ...childProps,
            children: children
        });
    }
    return /*#__PURE__*/ (0, _jsxruntime.jsx)(LinkStatusContext.Provider, {
        value: linkStatus,
        children: link
    });
}
const LinkStatusContext = /*#__PURE__*/ (0, _react.createContext)(_links.IDLE_LINK_STATUS);
const useLinkStatus = ()=>{
    return (0, _react.useContext)(LinkStatusContext);
};
function getFetchStrategyFromPrefetchProp(prefetchProp) {
    if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
    ;
    else {
        return prefetchProp === null || prefetchProp === 'auto' ? _segmentcache.FetchStrategy.PPR : // (although invalid values should've been filtered out by prop validation in dev)
        _segmentcache.FetchStrategy.Full;
    }
}
if ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {
    Object.defineProperty(exports.default, '__esModule', {
        value: true
    });
    Object.assign(exports.default, exports);
    module.exports = exports.default;
} //# sourceMappingURL=link.js.map
}),
"[project]/node_modules/next/navigation.js [app-client] (ecmascript)", ((__turbopack_context__, module, exports) => {

module.exports = __turbopack_context__.r("[project]/node_modules/next/dist/client/components/navigation.js [app-client] (ecmascript)");
}),
"[project]/node_modules/@swc/helpers/esm/_class_apply_descriptor_get.js [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "_",
    ()=>_class_apply_descriptor_get
]);
function _class_apply_descriptor_get(receiver, descriptor) {
    if (descriptor.get) return descriptor.get.call(receiver);
    return descriptor.value;
}
;
}),
"[project]/node_modules/@swc/helpers/esm/_class_extract_field_descriptor.js [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "_",
    ()=>_class_extract_field_descriptor
]);
function _class_extract_field_descriptor(receiver, privateMap, action) {
    if (!privateMap.has(receiver)) throw new TypeError("attempted to " + action + " private field on non-instance");
    return privateMap.get(receiver);
}
;
}),
"[project]/node_modules/@swc/helpers/esm/_class_private_field_get.js [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "_",
    ()=>_class_private_field_get
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_class_apply_descriptor_get$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@swc/helpers/esm/_class_apply_descriptor_get.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_class_extract_field_descriptor$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@swc/helpers/esm/_class_extract_field_descriptor.js [app-client] (ecmascript)");
;
;
function _class_private_field_get(receiver, privateMap) {
    var descriptor = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_class_extract_field_descriptor$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(receiver, privateMap, "get");
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_class_apply_descriptor_get$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(receiver, descriptor);
}
;
}),
"[project]/node_modules/@swc/helpers/esm/_check_private_redeclaration.js [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "_",
    ()=>_check_private_redeclaration
]);
function _check_private_redeclaration(obj, privateCollection) {
    if (privateCollection.has(obj)) {
        throw new TypeError("Cannot initialize the same private elements twice on an object");
    }
}
;
}),
"[project]/node_modules/@swc/helpers/esm/_class_private_field_init.js [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "_",
    ()=>_class_private_field_init
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_check_private_redeclaration$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@swc/helpers/esm/_check_private_redeclaration.js [app-client] (ecmascript)");
;
function _class_private_field_init(obj, privateMap, value) {
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_check_private_redeclaration$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(obj, privateMap);
    privateMap.set(obj, value);
}
;
}),
"[project]/node_modules/@swc/helpers/esm/_class_apply_descriptor_set.js [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "_",
    ()=>_class_apply_descriptor_set
]);
function _class_apply_descriptor_set(receiver, descriptor, value) {
    if (descriptor.set) descriptor.set.call(receiver, value);
    else {
        if (!descriptor.writable) {
            // This should only throw in strict mode, but class bodies are
            // always strict and private fields can only be used inside
            // class bodies.
            throw new TypeError("attempted to set read only private field");
        }
        descriptor.value = value;
    }
}
;
}),
"[project]/node_modules/@swc/helpers/esm/_class_private_field_set.js [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "_",
    ()=>_class_private_field_set
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_class_apply_descriptor_set$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@swc/helpers/esm/_class_apply_descriptor_set.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_class_extract_field_descriptor$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@swc/helpers/esm/_class_extract_field_descriptor.js [app-client] (ecmascript)");
;
;
function _class_private_field_set(receiver, privateMap, value) {
    var descriptor = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_class_extract_field_descriptor$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(receiver, privateMap, "set");
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_class_apply_descriptor_set$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(receiver, descriptor, value);
    return value;
}
;
}),
"[project]/node_modules/@radix-ui/react-collection/dist/index.mjs [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "createCollection",
    ()=>createCollection,
    "unstable_createCollection",
    ()=>createCollection2
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_class_private_field_get$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@swc/helpers/esm/_class_private_field_get.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_class_private_field_init$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@swc/helpers/esm/_class_private_field_init.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_class_private_field_set$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@swc/helpers/esm/_class_private_field_set.js [app-client] (ecmascript)");
// src/collection-legacy.tsx
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$context$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-context/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$compose$2d$refs$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-compose-refs/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$slot$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-slot/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
"use client";
;
;
;
var _keys;
;
;
;
;
;
function createCollection(name) {
    const PROVIDER_NAME = name + "CollectionProvider";
    const [createCollectionContext, createCollectionScope] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$context$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContextScope"])(PROVIDER_NAME);
    const [CollectionProviderImpl, useCollectionContext] = createCollectionContext(PROVIDER_NAME, {
        collectionRef: {
            current: null
        },
        itemMap: /* @__PURE__ */ new Map()
    });
    const CollectionProvider = (props)=>{
        const { scope, children } = props;
        const ref = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useRef(null);
        const itemMap = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useRef(/* @__PURE__ */ new Map()).current;
        return /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(CollectionProviderImpl, {
            scope,
            itemMap,
            collectionRef: ref,
            children
        });
    };
    CollectionProvider.displayName = PROVIDER_NAME;
    const COLLECTION_SLOT_NAME = name + "CollectionSlot";
    const CollectionSlotImpl = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$slot$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createSlot"])(COLLECTION_SLOT_NAME);
    const CollectionSlot = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].forwardRef((props, forwardedRef)=>{
        const { scope, children } = props;
        const context = useCollectionContext(COLLECTION_SLOT_NAME, scope);
        const composedRefs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$compose$2d$refs$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useComposedRefs"])(forwardedRef, context.collectionRef);
        return /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(CollectionSlotImpl, {
            ref: composedRefs,
            children
        });
    });
    CollectionSlot.displayName = COLLECTION_SLOT_NAME;
    const ITEM_SLOT_NAME = name + "CollectionItemSlot";
    const ITEM_DATA_ATTR = "data-radix-collection-item";
    const CollectionItemSlotImpl = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$slot$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createSlot"])(ITEM_SLOT_NAME);
    const CollectionItemSlot = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].forwardRef((props, forwardedRef)=>{
        const { scope, children, ...itemData } = props;
        const ref = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useRef(null);
        const composedRefs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$compose$2d$refs$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useComposedRefs"])(forwardedRef, ref);
        const context = useCollectionContext(ITEM_SLOT_NAME, scope);
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useEffect({
            "createCollection.CollectionItemSlot.useEffect": ()=>{
                context.itemMap.set(ref, {
                    ref,
                    ...itemData
                });
                return ({
                    "createCollection.CollectionItemSlot.useEffect": ()=>void context.itemMap.delete(ref)
                })["createCollection.CollectionItemSlot.useEffect"];
            }
        }["createCollection.CollectionItemSlot.useEffect"]);
        return /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(CollectionItemSlotImpl, {
            ...{
                [ITEM_DATA_ATTR]: ""
            },
            ref: composedRefs,
            children
        });
    });
    CollectionItemSlot.displayName = ITEM_SLOT_NAME;
    function useCollection(scope) {
        const context = useCollectionContext(name + "CollectionConsumer", scope);
        const getItems = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useCallback({
            "createCollection.useCollection.useCallback[getItems]": ()=>{
                const collectionNode = context.collectionRef.current;
                if (!collectionNode) return [];
                const orderedNodes = Array.from(collectionNode.querySelectorAll("[".concat(ITEM_DATA_ATTR, "]")));
                const items = Array.from(context.itemMap.values());
                const orderedItems = items.sort({
                    "createCollection.useCollection.useCallback[getItems].orderedItems": (a, b)=>orderedNodes.indexOf(a.ref.current) - orderedNodes.indexOf(b.ref.current)
                }["createCollection.useCollection.useCallback[getItems].orderedItems"]);
                return orderedItems;
            }
        }["createCollection.useCollection.useCallback[getItems]"], [
            context.collectionRef,
            context.itemMap
        ]);
        return getItems;
    }
    return [
        {
            Provider: CollectionProvider,
            Slot: CollectionSlot,
            ItemSlot: CollectionItemSlot
        },
        useCollection,
        createCollectionScope
    ];
}
;
;
;
;
// src/ordered-dictionary.ts
var __instanciated = /* @__PURE__ */ new WeakMap();
var OrderedDict = (_keys = /*#__PURE__*/ new WeakMap(), class _OrderedDict extends Map {
    set(key, value) {
        if (__instanciated.get(this)) {
            if (this.has(key)) {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_class_private_field_get$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, _keys)[(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_class_private_field_get$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, _keys).indexOf(key)] = key;
            } else {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_class_private_field_get$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, _keys).push(key);
            }
        }
        super.set(key, value);
        return this;
    }
    insert(index, key, value) {
        const has = this.has(key);
        const length = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_class_private_field_get$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, _keys).length;
        const relativeIndex = toSafeInteger(index);
        let actualIndex = relativeIndex >= 0 ? relativeIndex : length + relativeIndex;
        const safeIndex = actualIndex < 0 || actualIndex >= length ? -1 : actualIndex;
        if (safeIndex === this.size || has && safeIndex === this.size - 1 || safeIndex === -1) {
            this.set(key, value);
            return this;
        }
        const size = this.size + (has ? 0 : 1);
        if (relativeIndex < 0) {
            actualIndex++;
        }
        const keys = [
            ...(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_class_private_field_get$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, _keys)
        ];
        let nextValue;
        let shouldSkip = false;
        for(let i = actualIndex; i < size; i++){
            if (actualIndex === i) {
                let nextKey = keys[i];
                if (keys[i] === key) {
                    nextKey = keys[i + 1];
                }
                if (has) {
                    this.delete(key);
                }
                nextValue = this.get(nextKey);
                this.set(key, value);
            } else {
                if (!shouldSkip && keys[i - 1] === key) {
                    shouldSkip = true;
                }
                const currentKey = keys[shouldSkip ? i : i - 1];
                const currentValue = nextValue;
                nextValue = this.get(currentKey);
                this.delete(currentKey);
                this.set(currentKey, currentValue);
            }
        }
        return this;
    }
    with(index, key, value) {
        const copy = new _OrderedDict(this);
        copy.insert(index, key, value);
        return copy;
    }
    before(key) {
        const index = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_class_private_field_get$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, _keys).indexOf(key) - 1;
        if (index < 0) {
            return void 0;
        }
        return this.entryAt(index);
    }
    /**
   * Sets a new key-value pair at the position before the given key.
   */ setBefore(key, newKey, value) {
        const index = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_class_private_field_get$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, _keys).indexOf(key);
        if (index === -1) {
            return this;
        }
        return this.insert(index, newKey, value);
    }
    after(key) {
        let index = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_class_private_field_get$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, _keys).indexOf(key);
        index = index === -1 || index === this.size - 1 ? -1 : index + 1;
        if (index === -1) {
            return void 0;
        }
        return this.entryAt(index);
    }
    /**
   * Sets a new key-value pair at the position after the given key.
   */ setAfter(key, newKey, value) {
        const index = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_class_private_field_get$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, _keys).indexOf(key);
        if (index === -1) {
            return this;
        }
        return this.insert(index + 1, newKey, value);
    }
    first() {
        return this.entryAt(0);
    }
    last() {
        return this.entryAt(-1);
    }
    clear() {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_class_private_field_set$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, _keys, []);
        return super.clear();
    }
    delete(key) {
        const deleted = super.delete(key);
        if (deleted) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_class_private_field_get$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, _keys).splice((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_class_private_field_get$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, _keys).indexOf(key), 1);
        }
        return deleted;
    }
    deleteAt(index) {
        const key = this.keyAt(index);
        if (key !== void 0) {
            return this.delete(key);
        }
        return false;
    }
    at(index) {
        const key = at((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_class_private_field_get$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, _keys), index);
        if (key !== void 0) {
            return this.get(key);
        }
    }
    entryAt(index) {
        const key = at((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_class_private_field_get$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, _keys), index);
        if (key !== void 0) {
            return [
                key,
                this.get(key)
            ];
        }
    }
    indexOf(key) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_class_private_field_get$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, _keys).indexOf(key);
    }
    keyAt(index) {
        return at((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_class_private_field_get$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, _keys), index);
    }
    from(key, offset) {
        const index = this.indexOf(key);
        if (index === -1) {
            return void 0;
        }
        let dest = index + offset;
        if (dest < 0) dest = 0;
        if (dest >= this.size) dest = this.size - 1;
        return this.at(dest);
    }
    keyFrom(key, offset) {
        const index = this.indexOf(key);
        if (index === -1) {
            return void 0;
        }
        let dest = index + offset;
        if (dest < 0) dest = 0;
        if (dest >= this.size) dest = this.size - 1;
        return this.keyAt(dest);
    }
    find(predicate, thisArg) {
        let index = 0;
        for (const entry of this){
            if (Reflect.apply(predicate, thisArg, [
                entry,
                index,
                this
            ])) {
                return entry;
            }
            index++;
        }
        return void 0;
    }
    findIndex(predicate, thisArg) {
        let index = 0;
        for (const entry of this){
            if (Reflect.apply(predicate, thisArg, [
                entry,
                index,
                this
            ])) {
                return index;
            }
            index++;
        }
        return -1;
    }
    filter(predicate, thisArg) {
        const entries = [];
        let index = 0;
        for (const entry of this){
            if (Reflect.apply(predicate, thisArg, [
                entry,
                index,
                this
            ])) {
                entries.push(entry);
            }
            index++;
        }
        return new _OrderedDict(entries);
    }
    map(callbackfn, thisArg) {
        const entries = [];
        let index = 0;
        for (const entry of this){
            entries.push([
                entry[0],
                Reflect.apply(callbackfn, thisArg, [
                    entry,
                    index,
                    this
                ])
            ]);
            index++;
        }
        return new _OrderedDict(entries);
    }
    reduce() {
        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){
            args[_key] = arguments[_key];
        }
        const [callbackfn, initialValue] = args;
        let index = 0;
        let accumulator = initialValue !== null && initialValue !== void 0 ? initialValue : this.at(0);
        for (const entry of this){
            if (index === 0 && args.length === 1) {
                accumulator = entry;
            } else {
                accumulator = Reflect.apply(callbackfn, this, [
                    accumulator,
                    entry,
                    index,
                    this
                ]);
            }
            index++;
        }
        return accumulator;
    }
    reduceRight() {
        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){
            args[_key] = arguments[_key];
        }
        const [callbackfn, initialValue] = args;
        let accumulator = initialValue !== null && initialValue !== void 0 ? initialValue : this.at(-1);
        for(let index = this.size - 1; index >= 0; index--){
            const entry = this.at(index);
            if (index === this.size - 1 && args.length === 1) {
                accumulator = entry;
            } else {
                accumulator = Reflect.apply(callbackfn, this, [
                    accumulator,
                    entry,
                    index,
                    this
                ]);
            }
        }
        return accumulator;
    }
    toSorted(compareFn) {
        const entries = [
            ...this.entries()
        ].sort(compareFn);
        return new _OrderedDict(entries);
    }
    toReversed() {
        const reversed = new _OrderedDict();
        for(let index = this.size - 1; index >= 0; index--){
            const key = this.keyAt(index);
            const element = this.get(key);
            reversed.set(key, element);
        }
        return reversed;
    }
    toSpliced() {
        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){
            args[_key] = arguments[_key];
        }
        const entries = [
            ...this.entries()
        ];
        entries.splice(...args);
        return new _OrderedDict(entries);
    }
    slice(start, end) {
        const result = new _OrderedDict();
        let stop = this.size - 1;
        if (start === void 0) {
            return result;
        }
        if (start < 0) {
            start = start + this.size;
        }
        if (end !== void 0 && end > 0) {
            stop = end - 1;
        }
        for(let index = start; index <= stop; index++){
            const key = this.keyAt(index);
            const element = this.get(key);
            result.set(key, element);
        }
        return result;
    }
    every(predicate, thisArg) {
        let index = 0;
        for (const entry of this){
            if (!Reflect.apply(predicate, thisArg, [
                entry,
                index,
                this
            ])) {
                return false;
            }
            index++;
        }
        return true;
    }
    some(predicate, thisArg) {
        let index = 0;
        for (const entry of this){
            if (Reflect.apply(predicate, thisArg, [
                entry,
                index,
                this
            ])) {
                return true;
            }
            index++;
        }
        return false;
    }
    constructor(entries){
        super(entries), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_class_private_field_init$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, _keys, {
            writable: true,
            value: void 0
        });
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_class_private_field_set$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, _keys, [
            ...super.keys()
        ]);
        __instanciated.set(this, true);
    }
});
function at(array, index) {
    if ("at" in Array.prototype) {
        return Array.prototype.at.call(array, index);
    }
    const actualIndex = toSafeIndex(array, index);
    return actualIndex === -1 ? void 0 : array[actualIndex];
}
function toSafeIndex(array, index) {
    const length = array.length;
    const relativeIndex = toSafeInteger(index);
    const actualIndex = relativeIndex >= 0 ? relativeIndex : length + relativeIndex;
    return actualIndex < 0 || actualIndex >= length ? -1 : actualIndex;
}
function toSafeInteger(number) {
    return number !== number || number === 0 ? 0 : Math.trunc(number);
}
;
function createCollection2(name) {
    const PROVIDER_NAME = name + "CollectionProvider";
    const [createCollectionContext, createCollectionScope] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$context$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContextScope"])(PROVIDER_NAME);
    const [CollectionContextProvider, useCollectionContext] = createCollectionContext(PROVIDER_NAME, {
        collectionElement: null,
        collectionRef: {
            current: null
        },
        collectionRefObject: {
            current: null
        },
        itemMap: new OrderedDict(),
        setItemMap: ()=>void 0
    });
    const CollectionProvider = (param)=>{
        let { state, ...props } = param;
        return state ? /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(CollectionProviderImpl, {
            ...props,
            state
        }) : /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(CollectionInit, {
            ...props
        });
    };
    CollectionProvider.displayName = PROVIDER_NAME;
    const CollectionInit = (props)=>{
        const state = useInitCollection();
        return /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(CollectionProviderImpl, {
            ...props,
            state
        });
    };
    CollectionInit.displayName = PROVIDER_NAME + "Init";
    const CollectionProviderImpl = (props)=>{
        const { scope, children, state } = props;
        const ref = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useRef(null);
        const [collectionElement, setCollectionElement] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useState(null);
        const composeRefs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$compose$2d$refs$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useComposedRefs"])(ref, setCollectionElement);
        const [itemMap, setItemMap] = state;
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useEffect({
            "createCollection2.CollectionProviderImpl.useEffect": ()=>{
                if (!collectionElement) return;
                const observer = getChildListObserver({
                    "createCollection2.CollectionProviderImpl.useEffect.observer": ()=>{}
                }["createCollection2.CollectionProviderImpl.useEffect.observer"]);
                observer.observe(collectionElement, {
                    childList: true,
                    subtree: true
                });
                return ({
                    "createCollection2.CollectionProviderImpl.useEffect": ()=>{
                        observer.disconnect();
                    }
                })["createCollection2.CollectionProviderImpl.useEffect"];
            }
        }["createCollection2.CollectionProviderImpl.useEffect"], [
            collectionElement
        ]);
        return /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(CollectionContextProvider, {
            scope,
            itemMap,
            setItemMap,
            collectionRef: composeRefs,
            collectionRefObject: ref,
            collectionElement,
            children
        });
    };
    CollectionProviderImpl.displayName = PROVIDER_NAME + "Impl";
    const COLLECTION_SLOT_NAME = name + "CollectionSlot";
    const CollectionSlotImpl = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$slot$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createSlot"])(COLLECTION_SLOT_NAME);
    const CollectionSlot = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].forwardRef((props, forwardedRef)=>{
        const { scope, children } = props;
        const context = useCollectionContext(COLLECTION_SLOT_NAME, scope);
        const composedRefs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$compose$2d$refs$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useComposedRefs"])(forwardedRef, context.collectionRef);
        return /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(CollectionSlotImpl, {
            ref: composedRefs,
            children
        });
    });
    CollectionSlot.displayName = COLLECTION_SLOT_NAME;
    const ITEM_SLOT_NAME = name + "CollectionItemSlot";
    const ITEM_DATA_ATTR = "data-radix-collection-item";
    const CollectionItemSlotImpl = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$slot$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createSlot"])(ITEM_SLOT_NAME);
    const CollectionItemSlot = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].forwardRef((props, forwardedRef)=>{
        const { scope, children, ...itemData } = props;
        const ref = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useRef(null);
        const [element, setElement] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useState(null);
        const composedRefs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$compose$2d$refs$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useComposedRefs"])(forwardedRef, ref, setElement);
        const context = useCollectionContext(ITEM_SLOT_NAME, scope);
        const { setItemMap } = context;
        const itemDataRef = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useRef(itemData);
        if (!shallowEqual(itemDataRef.current, itemData)) {
            itemDataRef.current = itemData;
        }
        const memoizedItemData = itemDataRef.current;
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useEffect({
            "createCollection2.CollectionItemSlot.useEffect": ()=>{
                const itemData2 = memoizedItemData;
                setItemMap({
                    "createCollection2.CollectionItemSlot.useEffect": (map)=>{
                        if (!element) {
                            return map;
                        }
                        if (!map.has(element)) {
                            map.set(element, {
                                ...itemData2,
                                element
                            });
                            return map.toSorted(sortByDocumentPosition);
                        }
                        return map.set(element, {
                            ...itemData2,
                            element
                        }).toSorted(sortByDocumentPosition);
                    }
                }["createCollection2.CollectionItemSlot.useEffect"]);
                return ({
                    "createCollection2.CollectionItemSlot.useEffect": ()=>{
                        setItemMap({
                            "createCollection2.CollectionItemSlot.useEffect": (map)=>{
                                if (!element || !map.has(element)) {
                                    return map;
                                }
                                map.delete(element);
                                return new OrderedDict(map);
                            }
                        }["createCollection2.CollectionItemSlot.useEffect"]);
                    }
                })["createCollection2.CollectionItemSlot.useEffect"];
            }
        }["createCollection2.CollectionItemSlot.useEffect"], [
            element,
            memoizedItemData,
            setItemMap
        ]);
        return /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(CollectionItemSlotImpl, {
            ...{
                [ITEM_DATA_ATTR]: ""
            },
            ref: composedRefs,
            children
        });
    });
    CollectionItemSlot.displayName = ITEM_SLOT_NAME;
    function useInitCollection() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useState(new OrderedDict());
    }
    function useCollection(scope) {
        const { itemMap } = useCollectionContext(name + "CollectionConsumer", scope);
        return itemMap;
    }
    const functions = {
        createCollectionScope,
        useCollection,
        useInitCollection
    };
    return [
        {
            Provider: CollectionProvider,
            Slot: CollectionSlot,
            ItemSlot: CollectionItemSlot
        },
        functions
    ];
}
function shallowEqual(a, b) {
    if (a === b) return true;
    if (typeof a !== "object" || typeof b !== "object") return false;
    if (a == null || b == null) return false;
    const keysA = Object.keys(a);
    const keysB = Object.keys(b);
    if (keysA.length !== keysB.length) return false;
    for (const key of keysA){
        if (!Object.prototype.hasOwnProperty.call(b, key)) return false;
        if (a[key] !== b[key]) return false;
    }
    return true;
}
function isElementPreceding(a, b) {
    return !!(b.compareDocumentPosition(a) & Node.DOCUMENT_POSITION_PRECEDING);
}
function sortByDocumentPosition(a, b) {
    return !a[1].element || !b[1].element ? 0 : isElementPreceding(a[1].element, b[1].element) ? -1 : 1;
}
function getChildListObserver(callback) {
    const observer = new MutationObserver((mutationsList)=>{
        for (const mutation of mutationsList){
            if (mutation.type === "childList") {
                callback();
                return;
            }
        }
    });
    return observer;
}
;
 //# sourceMappingURL=index.mjs.map
}),
"[project]/node_modules/@radix-ui/react-direction/dist/index.mjs [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

// packages/react/direction/src/direction.tsx
__turbopack_context__.s([
    "DirectionProvider",
    ()=>DirectionProvider,
    "Provider",
    ()=>Provider,
    "useDirection",
    ()=>useDirection
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
;
;
var DirectionContext = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"](void 0);
var DirectionProvider = (props)=>{
    const { dir, children } = props;
    return /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(DirectionContext.Provider, {
        value: dir,
        children
    });
};
function useDirection(localDir) {
    const globalDir = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"](DirectionContext);
    return localDir || globalDir || "ltr";
}
var Provider = DirectionProvider;
;
 //# sourceMappingURL=index.mjs.map
}),
"[project]/node_modules/@radix-ui/react-roving-focus/dist/index.mjs [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "Item",
    ()=>Item,
    "Root",
    ()=>Root,
    "RovingFocusGroup",
    ()=>RovingFocusGroup,
    "RovingFocusGroupItem",
    ()=>RovingFocusGroupItem,
    "createRovingFocusGroupScope",
    ()=>createRovingFocusGroupScope
]);
// src/roving-focus-group.tsx
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$primitive$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/primitive/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$collection$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-collection/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$compose$2d$refs$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-compose-refs/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$context$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-context/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$id$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-id/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$primitive$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-primitive/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$use$2d$callback$2d$ref$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$use$2d$controllable$2d$state$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$direction$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-direction/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
"use client";
;
;
;
;
;
;
;
;
;
;
;
var ENTRY_FOCUS = "rovingFocusGroup.onEntryFocus";
var EVENT_OPTIONS = {
    bubbles: false,
    cancelable: true
};
var GROUP_NAME = "RovingFocusGroup";
var [Collection, useCollection, createCollectionScope] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$collection$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createCollection"])(GROUP_NAME);
var [createRovingFocusGroupContext, createRovingFocusGroupScope] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$context$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContextScope"])(GROUP_NAME, [
    createCollectionScope
]);
var [RovingFocusProvider, useRovingFocusContext] = createRovingFocusGroupContext(GROUP_NAME);
var RovingFocusGroup = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"]((props, forwardedRef)=>{
    return /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(Collection.Provider, {
        scope: props.__scopeRovingFocusGroup,
        children: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(Collection.Slot, {
            scope: props.__scopeRovingFocusGroup,
            children: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(RovingFocusGroupImpl, {
                ...props,
                ref: forwardedRef
            })
        })
    });
});
RovingFocusGroup.displayName = GROUP_NAME;
var RovingFocusGroupImpl = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"]((props, forwardedRef)=>{
    const { __scopeRovingFocusGroup, orientation, loop = false, dir, currentTabStopId: currentTabStopIdProp, defaultCurrentTabStopId, onCurrentTabStopIdChange, onEntryFocus, preventScrollOnEntryFocus = false, ...groupProps } = props;
    const ref = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"](null);
    const composedRefs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$compose$2d$refs$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useComposedRefs"])(forwardedRef, ref);
    const direction = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$direction$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useDirection"])(dir);
    const [currentTabStopId, setCurrentTabStopId] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$use$2d$controllable$2d$state$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useControllableState"])({
        prop: currentTabStopIdProp,
        defaultProp: defaultCurrentTabStopId !== null && defaultCurrentTabStopId !== void 0 ? defaultCurrentTabStopId : null,
        onChange: onCurrentTabStopIdChange,
        caller: GROUP_NAME
    });
    const [isTabbingBackOut, setIsTabbingBackOut] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"](false);
    const handleEntryFocus = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$use$2d$callback$2d$ref$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallbackRef"])(onEntryFocus);
    const getItems = useCollection(__scopeRovingFocusGroup);
    const isClickFocusRef = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"](false);
    const [focusableItemsCount, setFocusableItemsCount] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"](0);
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"]({
        "RovingFocusGroupImpl.useEffect": ()=>{
            const node = ref.current;
            if (node) {
                node.addEventListener(ENTRY_FOCUS, handleEntryFocus);
                return ({
                    "RovingFocusGroupImpl.useEffect": ()=>node.removeEventListener(ENTRY_FOCUS, handleEntryFocus)
                })["RovingFocusGroupImpl.useEffect"];
            }
        }
    }["RovingFocusGroupImpl.useEffect"], [
        handleEntryFocus
    ]);
    return /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(RovingFocusProvider, {
        scope: __scopeRovingFocusGroup,
        orientation,
        dir: direction,
        loop,
        currentTabStopId,
        onItemFocus: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"]({
            "RovingFocusGroupImpl.useCallback": (tabStopId)=>setCurrentTabStopId(tabStopId)
        }["RovingFocusGroupImpl.useCallback"], [
            setCurrentTabStopId
        ]),
        onItemShiftTab: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"]({
            "RovingFocusGroupImpl.useCallback": ()=>setIsTabbingBackOut(true)
        }["RovingFocusGroupImpl.useCallback"], []),
        onFocusableItemAdd: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"]({
            "RovingFocusGroupImpl.useCallback": ()=>setFocusableItemsCount({
                    "RovingFocusGroupImpl.useCallback": (prevCount)=>prevCount + 1
                }["RovingFocusGroupImpl.useCallback"])
        }["RovingFocusGroupImpl.useCallback"], []),
        onFocusableItemRemove: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"]({
            "RovingFocusGroupImpl.useCallback": ()=>setFocusableItemsCount({
                    "RovingFocusGroupImpl.useCallback": (prevCount)=>prevCount - 1
                }["RovingFocusGroupImpl.useCallback"])
        }["RovingFocusGroupImpl.useCallback"], []),
        children: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$primitive$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Primitive"].div, {
            tabIndex: isTabbingBackOut || focusableItemsCount === 0 ? -1 : 0,
            "data-orientation": orientation,
            ...groupProps,
            ref: composedRefs,
            style: {
                outline: "none",
                ...props.style
            },
            onMouseDown: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$primitive$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["composeEventHandlers"])(props.onMouseDown, ()=>{
                isClickFocusRef.current = true;
            }),
            onFocus: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$primitive$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["composeEventHandlers"])(props.onFocus, (event)=>{
                const isKeyboardFocus = !isClickFocusRef.current;
                if (event.target === event.currentTarget && isKeyboardFocus && !isTabbingBackOut) {
                    const entryFocusEvent = new CustomEvent(ENTRY_FOCUS, EVENT_OPTIONS);
                    event.currentTarget.dispatchEvent(entryFocusEvent);
                    if (!entryFocusEvent.defaultPrevented) {
                        const items = getItems().filter((item)=>item.focusable);
                        const activeItem = items.find((item)=>item.active);
                        const currentItem = items.find((item)=>item.id === currentTabStopId);
                        const candidateItems = [
                            activeItem,
                            currentItem,
                            ...items
                        ].filter(Boolean);
                        const candidateNodes = candidateItems.map((item)=>item.ref.current);
                        focusFirst(candidateNodes, preventScrollOnEntryFocus);
                    }
                }
                isClickFocusRef.current = false;
            }),
            onBlur: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$primitive$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["composeEventHandlers"])(props.onBlur, ()=>setIsTabbingBackOut(false))
        })
    });
});
var ITEM_NAME = "RovingFocusGroupItem";
var RovingFocusGroupItem = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"]((props, forwardedRef)=>{
    const { __scopeRovingFocusGroup, focusable = true, active = false, tabStopId, children, ...itemProps } = props;
    const autoId = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$id$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useId"])();
    const id = tabStopId || autoId;
    const context = useRovingFocusContext(ITEM_NAME, __scopeRovingFocusGroup);
    const isCurrentTabStop = context.currentTabStopId === id;
    const getItems = useCollection(__scopeRovingFocusGroup);
    const { onFocusableItemAdd, onFocusableItemRemove, currentTabStopId } = context;
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"]({
        "RovingFocusGroupItem.useEffect": ()=>{
            if (focusable) {
                onFocusableItemAdd();
                return ({
                    "RovingFocusGroupItem.useEffect": ()=>onFocusableItemRemove()
                })["RovingFocusGroupItem.useEffect"];
            }
        }
    }["RovingFocusGroupItem.useEffect"], [
        focusable,
        onFocusableItemAdd,
        onFocusableItemRemove
    ]);
    return /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(Collection.ItemSlot, {
        scope: __scopeRovingFocusGroup,
        id,
        focusable,
        active,
        children: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$primitive$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Primitive"].span, {
            tabIndex: isCurrentTabStop ? 0 : -1,
            "data-orientation": context.orientation,
            ...itemProps,
            ref: forwardedRef,
            onMouseDown: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$primitive$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["composeEventHandlers"])(props.onMouseDown, (event)=>{
                if (!focusable) event.preventDefault();
                else context.onItemFocus(id);
            }),
            onFocus: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$primitive$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["composeEventHandlers"])(props.onFocus, ()=>context.onItemFocus(id)),
            onKeyDown: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$primitive$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["composeEventHandlers"])(props.onKeyDown, (event)=>{
                if (event.key === "Tab" && event.shiftKey) {
                    context.onItemShiftTab();
                    return;
                }
                if (event.target !== event.currentTarget) return;
                const focusIntent = getFocusIntent(event, context.orientation, context.dir);
                if (focusIntent !== void 0) {
                    if (event.metaKey || event.ctrlKey || event.altKey || event.shiftKey) return;
                    event.preventDefault();
                    const items = getItems().filter((item)=>item.focusable);
                    let candidateNodes = items.map((item)=>item.ref.current);
                    if (focusIntent === "last") candidateNodes.reverse();
                    else if (focusIntent === "prev" || focusIntent === "next") {
                        if (focusIntent === "prev") candidateNodes.reverse();
                        const currentIndex = candidateNodes.indexOf(event.currentTarget);
                        candidateNodes = context.loop ? wrapArray(candidateNodes, currentIndex + 1) : candidateNodes.slice(currentIndex + 1);
                    }
                    setTimeout(()=>focusFirst(candidateNodes));
                }
            }),
            children: typeof children === "function" ? children({
                isCurrentTabStop,
                hasTabStop: currentTabStopId != null
            }) : children
        })
    });
});
RovingFocusGroupItem.displayName = ITEM_NAME;
var MAP_KEY_TO_FOCUS_INTENT = {
    ArrowLeft: "prev",
    ArrowUp: "prev",
    ArrowRight: "next",
    ArrowDown: "next",
    PageUp: "first",
    Home: "first",
    PageDown: "last",
    End: "last"
};
function getDirectionAwareKey(key, dir) {
    if (dir !== "rtl") return key;
    return key === "ArrowLeft" ? "ArrowRight" : key === "ArrowRight" ? "ArrowLeft" : key;
}
function getFocusIntent(event, orientation, dir) {
    const key = getDirectionAwareKey(event.key, dir);
    if (orientation === "vertical" && [
        "ArrowLeft",
        "ArrowRight"
    ].includes(key)) return void 0;
    if (orientation === "horizontal" && [
        "ArrowUp",
        "ArrowDown"
    ].includes(key)) return void 0;
    return MAP_KEY_TO_FOCUS_INTENT[key];
}
function focusFirst(candidates) {
    let preventScroll = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;
    const PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;
    for (const candidate of candidates){
        if (candidate === PREVIOUSLY_FOCUSED_ELEMENT) return;
        candidate.focus({
            preventScroll
        });
        if (document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT) return;
    }
}
function wrapArray(array, startIndex) {
    return array.map((_, index)=>array[(startIndex + index) % array.length]);
}
var Root = RovingFocusGroup;
var Item = RovingFocusGroupItem;
;
 //# sourceMappingURL=index.mjs.map
}),
"[project]/node_modules/@radix-ui/react-tabs/dist/index.mjs [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "Content",
    ()=>Content,
    "List",
    ()=>List,
    "Root",
    ()=>Root2,
    "Tabs",
    ()=>Tabs,
    "TabsContent",
    ()=>TabsContent,
    "TabsList",
    ()=>TabsList,
    "TabsTrigger",
    ()=>TabsTrigger,
    "Trigger",
    ()=>Trigger,
    "createTabsScope",
    ()=>createTabsScope
]);
// src/tabs.tsx
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$primitive$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/primitive/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$context$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-context/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$roving$2d$focus$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-roving-focus/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$presence$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-presence/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$primitive$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-primitive/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$direction$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-direction/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$use$2d$controllable$2d$state$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$id$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-id/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
"use client";
;
;
;
;
;
;
;
;
;
;
;
var TABS_NAME = "Tabs";
var [createTabsContext, createTabsScope] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$context$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContextScope"])(TABS_NAME, [
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$roving$2d$focus$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createRovingFocusGroupScope"]
]);
var useRovingFocusGroupScope = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$roving$2d$focus$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createRovingFocusGroupScope"])();
var [TabsProvider, useTabsContext] = createTabsContext(TABS_NAME);
var Tabs = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"]((props, forwardedRef)=>{
    const { __scopeTabs, value: valueProp, onValueChange, defaultValue, orientation = "horizontal", dir, activationMode = "automatic", ...tabsProps } = props;
    const direction = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$direction$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useDirection"])(dir);
    const [value, setValue] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$use$2d$controllable$2d$state$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useControllableState"])({
        prop: valueProp,
        onChange: onValueChange,
        defaultProp: defaultValue !== null && defaultValue !== void 0 ? defaultValue : "",
        caller: TABS_NAME
    });
    return /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(TabsProvider, {
        scope: __scopeTabs,
        baseId: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$id$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useId"])(),
        value,
        onValueChange: setValue,
        orientation,
        dir: direction,
        activationMode,
        children: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$primitive$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Primitive"].div, {
            dir: direction,
            "data-orientation": orientation,
            ...tabsProps,
            ref: forwardedRef
        })
    });
});
Tabs.displayName = TABS_NAME;
var TAB_LIST_NAME = "TabsList";
var TabsList = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"]((props, forwardedRef)=>{
    const { __scopeTabs, loop = true, ...listProps } = props;
    const context = useTabsContext(TAB_LIST_NAME, __scopeTabs);
    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeTabs);
    return /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$roving$2d$focus$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Root"], {
        asChild: true,
        ...rovingFocusGroupScope,
        orientation: context.orientation,
        dir: context.dir,
        loop,
        children: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$primitive$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Primitive"].div, {
            role: "tablist",
            "aria-orientation": context.orientation,
            ...listProps,
            ref: forwardedRef
        })
    });
});
TabsList.displayName = TAB_LIST_NAME;
var TRIGGER_NAME = "TabsTrigger";
var TabsTrigger = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"]((props, forwardedRef)=>{
    const { __scopeTabs, value, disabled = false, ...triggerProps } = props;
    const context = useTabsContext(TRIGGER_NAME, __scopeTabs);
    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeTabs);
    const triggerId = makeTriggerId(context.baseId, value);
    const contentId = makeContentId(context.baseId, value);
    const isSelected = value === context.value;
    return /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$roving$2d$focus$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Item"], {
        asChild: true,
        ...rovingFocusGroupScope,
        focusable: !disabled,
        active: isSelected,
        children: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$primitive$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Primitive"].button, {
            type: "button",
            role: "tab",
            "aria-selected": isSelected,
            "aria-controls": contentId,
            "data-state": isSelected ? "active" : "inactive",
            "data-disabled": disabled ? "" : void 0,
            disabled,
            id: triggerId,
            ...triggerProps,
            ref: forwardedRef,
            onMouseDown: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$primitive$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["composeEventHandlers"])(props.onMouseDown, (event)=>{
                if (!disabled && event.button === 0 && event.ctrlKey === false) {
                    context.onValueChange(value);
                } else {
                    event.preventDefault();
                }
            }),
            onKeyDown: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$primitive$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["composeEventHandlers"])(props.onKeyDown, (event)=>{
                if ([
                    " ",
                    "Enter"
                ].includes(event.key)) context.onValueChange(value);
            }),
            onFocus: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$primitive$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["composeEventHandlers"])(props.onFocus, ()=>{
                const isAutomaticActivation = context.activationMode !== "manual";
                if (!isSelected && !disabled && isAutomaticActivation) {
                    context.onValueChange(value);
                }
            })
        })
    });
});
TabsTrigger.displayName = TRIGGER_NAME;
var CONTENT_NAME = "TabsContent";
var TabsContent = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"]((props, forwardedRef)=>{
    const { __scopeTabs, value, forceMount, children, ...contentProps } = props;
    const context = useTabsContext(CONTENT_NAME, __scopeTabs);
    const triggerId = makeTriggerId(context.baseId, value);
    const contentId = makeContentId(context.baseId, value);
    const isSelected = value === context.value;
    const isMountAnimationPreventedRef = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"](isSelected);
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"]({
        "TabsContent.useEffect": ()=>{
            const rAF = requestAnimationFrame({
                "TabsContent.useEffect.rAF": ()=>isMountAnimationPreventedRef.current = false
            }["TabsContent.useEffect.rAF"]);
            return ({
                "TabsContent.useEffect": ()=>cancelAnimationFrame(rAF)
            })["TabsContent.useEffect"];
        }
    }["TabsContent.useEffect"], []);
    return /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$presence$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Presence"], {
        present: forceMount || isSelected,
        children: (param)=>{
            let { present } = param;
            return /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$primitive$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Primitive"].div, {
                "data-state": isSelected ? "active" : "inactive",
                "data-orientation": context.orientation,
                role: "tabpanel",
                "aria-labelledby": triggerId,
                hidden: !present,
                id: contentId,
                tabIndex: 0,
                ...contentProps,
                ref: forwardedRef,
                style: {
                    ...props.style,
                    animationDuration: isMountAnimationPreventedRef.current ? "0s" : void 0
                },
                children: present && children
            });
        }
    });
});
TabsContent.displayName = CONTENT_NAME;
function makeTriggerId(baseId, value) {
    return "".concat(baseId, "-trigger-").concat(value);
}
function makeContentId(baseId, value) {
    return "".concat(baseId, "-content-").concat(value);
}
var Root2 = Tabs;
var List = TabsList;
var Trigger = TabsTrigger;
var Content = TabsContent;
;
 //# sourceMappingURL=index.mjs.map
}),
"[project]/node_modules/lucide-react/dist/esm/icons/pause.js [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s([
    "__iconNode",
    ()=>__iconNode,
    "default",
    ()=>Pause
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js [app-client] (ecmascript)");
;
const __iconNode = [
    [
        "rect",
        {
            x: "14",
            y: "3",
            width: "5",
            height: "18",
            rx: "1",
            key: "kaeet6"
        }
    ],
    [
        "rect",
        {
            x: "5",
            y: "3",
            width: "5",
            height: "18",
            rx: "1",
            key: "1wsw3u"
        }
    ]
];
const Pause = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("pause", __iconNode);
;
 //# sourceMappingURL=pause.js.map
}),
"[project]/node_modules/lucide-react/dist/esm/icons/pause.js [app-client] (ecmascript) <export default as Pause>", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "Pause",
    ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$pause$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$pause$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/pause.js [app-client] (ecmascript)");
}),
"[project]/node_modules/lucide-react/dist/esm/icons/waves.js [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s([
    "__iconNode",
    ()=>__iconNode,
    "default",
    ()=>Waves
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js [app-client] (ecmascript)");
;
const __iconNode = [
    [
        "path",
        {
            d: "M2 6c.6.5 1.2 1 2.5 1C7 7 7 5 9.5 5c2.6 0 2.4 2 5 2 2.5 0 2.5-2 5-2 1.3 0 1.9.5 2.5 1",
            key: "knzxuh"
        }
    ],
    [
        "path",
        {
            d: "M2 12c.6.5 1.2 1 2.5 1 2.5 0 2.5-2 5-2 2.6 0 2.4 2 5 2 2.5 0 2.5-2 5-2 1.3 0 1.9.5 2.5 1",
            key: "2jd2cc"
        }
    ],
    [
        "path",
        {
            d: "M2 18c.6.5 1.2 1 2.5 1 2.5 0 2.5-2 5-2 2.6 0 2.4 2 5 2 2.5 0 2.5-2 5-2 1.3 0 1.9.5 2.5 1",
            key: "rd2r6e"
        }
    ]
];
const Waves = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("waves", __iconNode);
;
 //# sourceMappingURL=waves.js.map
}),
"[project]/node_modules/lucide-react/dist/esm/icons/waves.js [app-client] (ecmascript) <export default as Waves>", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "Waves",
    ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$waves$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$waves$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/waves.js [app-client] (ecmascript)");
}),
"[project]/node_modules/lucide-react/dist/esm/icons/check.js [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s([
    "__iconNode",
    ()=>__iconNode,
    "default",
    ()=>Check
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js [app-client] (ecmascript)");
;
const __iconNode = [
    [
        "path",
        {
            d: "M20 6 9 17l-5-5",
            key: "1gmf2c"
        }
    ]
];
const Check = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("check", __iconNode);
;
 //# sourceMappingURL=check.js.map
}),
"[project]/node_modules/lucide-react/dist/esm/icons/check.js [app-client] (ecmascript) <export default as Check>", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "Check",
    ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$check$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$check$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/check.js [app-client] (ecmascript)");
}),
"[project]/node_modules/lucide-react/dist/esm/icons/sparkles.js [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s([
    "__iconNode",
    ()=>__iconNode,
    "default",
    ()=>Sparkles
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js [app-client] (ecmascript)");
;
const __iconNode = [
    [
        "path",
        {
            d: "M11.017 2.814a1 1 0 0 1 1.966 0l1.051 5.558a2 2 0 0 0 1.594 1.594l5.558 1.051a1 1 0 0 1 0 1.966l-5.558 1.051a2 2 0 0 0-1.594 1.594l-1.051 5.558a1 1 0 0 1-1.966 0l-1.051-5.558a2 2 0 0 0-1.594-1.594l-5.558-1.051a1 1 0 0 1 0-1.966l5.558-1.051a2 2 0 0 0 1.594-1.594z",
            key: "1s2grr"
        }
    ],
    [
        "path",
        {
            d: "M20 2v4",
            key: "1rf3ol"
        }
    ],
    [
        "path",
        {
            d: "M22 4h-4",
            key: "gwowj6"
        }
    ],
    [
        "circle",
        {
            cx: "4",
            cy: "20",
            r: "2",
            key: "6kqj1y"
        }
    ]
];
const Sparkles = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("sparkles", __iconNode);
;
 //# sourceMappingURL=sparkles.js.map
}),
"[project]/node_modules/lucide-react/dist/esm/icons/sparkles.js [app-client] (ecmascript) <export default as Sparkles>", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "Sparkles",
    ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$sparkles$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$sparkles$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/sparkles.js [app-client] (ecmascript)");
}),
"[project]/node_modules/convex/dist/esm/index.js [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "version",
    ()=>version
]);
"use strict";
const version = "1.27.0"; //# sourceMappingURL=index.js.map
}),
"[project]/node_modules/convex/dist/esm/values/base64.js [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "byteLength",
    ()=>byteLength,
    "fromByteArray",
    ()=>fromByteArray,
    "fromByteArrayUrlSafeNoPadding",
    ()=>fromByteArrayUrlSafeNoPadding,
    "toByteArray",
    ()=>toByteArray
]);
"use strict";
var lookup = [];
var revLookup = [];
var Arr = Uint8Array;
var code = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
for(var i = 0, len = code.length; i < len; ++i){
    lookup[i] = code[i];
    revLookup[code.charCodeAt(i)] = i;
}
revLookup["-".charCodeAt(0)] = 62;
revLookup["_".charCodeAt(0)] = 63;
function getLens(b64) {
    var len = b64.length;
    if (len % 4 > 0) {
        throw new Error("Invalid string. Length must be a multiple of 4");
    }
    var validLen = b64.indexOf("=");
    if (validLen === -1) validLen = len;
    var placeHoldersLen = validLen === len ? 0 : 4 - validLen % 4;
    return [
        validLen,
        placeHoldersLen
    ];
}
function byteLength(b64) {
    var lens = getLens(b64);
    var validLen = lens[0];
    var placeHoldersLen = lens[1];
    return (validLen + placeHoldersLen) * 3 / 4 - placeHoldersLen;
}
function _byteLength(_b64, validLen, placeHoldersLen) {
    return (validLen + placeHoldersLen) * 3 / 4 - placeHoldersLen;
}
function toByteArray(b64) {
    var tmp;
    var lens = getLens(b64);
    var validLen = lens[0];
    var placeHoldersLen = lens[1];
    var arr = new Arr(_byteLength(b64, validLen, placeHoldersLen));
    var curByte = 0;
    var len = placeHoldersLen > 0 ? validLen - 4 : validLen;
    var i;
    for(i = 0; i < len; i += 4){
        tmp = revLookup[b64.charCodeAt(i)] << 18 | revLookup[b64.charCodeAt(i + 1)] << 12 | revLookup[b64.charCodeAt(i + 2)] << 6 | revLookup[b64.charCodeAt(i + 3)];
        arr[curByte++] = tmp >> 16 & 255;
        arr[curByte++] = tmp >> 8 & 255;
        arr[curByte++] = tmp & 255;
    }
    if (placeHoldersLen === 2) {
        tmp = revLookup[b64.charCodeAt(i)] << 2 | revLookup[b64.charCodeAt(i + 1)] >> 4;
        arr[curByte++] = tmp & 255;
    }
    if (placeHoldersLen === 1) {
        tmp = revLookup[b64.charCodeAt(i)] << 10 | revLookup[b64.charCodeAt(i + 1)] << 4 | revLookup[b64.charCodeAt(i + 2)] >> 2;
        arr[curByte++] = tmp >> 8 & 255;
        arr[curByte++] = tmp & 255;
    }
    return arr;
}
function tripletToBase64(num) {
    return lookup[num >> 18 & 63] + lookup[num >> 12 & 63] + lookup[num >> 6 & 63] + lookup[num & 63];
}
function encodeChunk(uint8, start, end) {
    var tmp;
    var output = [];
    for(var i = start; i < end; i += 3){
        tmp = (uint8[i] << 16 & 16711680) + (uint8[i + 1] << 8 & 65280) + (uint8[i + 2] & 255);
        output.push(tripletToBase64(tmp));
    }
    return output.join("");
}
function fromByteArray(uint8) {
    var tmp;
    var len = uint8.length;
    var extraBytes = len % 3;
    var parts = [];
    var maxChunkLength = 16383;
    for(var i = 0, len2 = len - extraBytes; i < len2; i += maxChunkLength){
        parts.push(encodeChunk(uint8, i, i + maxChunkLength > len2 ? len2 : i + maxChunkLength));
    }
    if (extraBytes === 1) {
        tmp = uint8[len - 1];
        parts.push(lookup[tmp >> 2] + lookup[tmp << 4 & 63] + "==");
    } else if (extraBytes === 2) {
        tmp = (uint8[len - 2] << 8) + uint8[len - 1];
        parts.push(lookup[tmp >> 10] + lookup[tmp >> 4 & 63] + lookup[tmp << 2 & 63] + "=");
    }
    return parts.join("");
}
function fromByteArrayUrlSafeNoPadding(uint8) {
    return fromByteArray(uint8).replace(/\+/g, "-").replace(/\//g, "_").replace(/=/g, "");
} //# sourceMappingURL=base64.js.map
}),
"[project]/node_modules/convex/dist/esm/common/index.js [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "isSimpleObject",
    ()=>isSimpleObject,
    "parseArgs",
    ()=>parseArgs,
    "validateDeploymentUrl",
    ()=>validateDeploymentUrl
]);
"use strict";
function parseArgs(args) {
    if (args === void 0) {
        return {};
    }
    if (!isSimpleObject(args)) {
        throw new Error("The arguments to a Convex function must be an object. Received: ".concat(args));
    }
    return args;
}
function validateDeploymentUrl(deploymentUrl) {
    if (typeof deploymentUrl === "undefined") {
        throw new Error("Client created with undefined deployment address. If you used an environment variable, check that it's set.");
    }
    if (typeof deploymentUrl !== "string") {
        throw new Error("Invalid deployment address: found ".concat(deploymentUrl, '".'));
    }
    if (!(deploymentUrl.startsWith("http:") || deploymentUrl.startsWith("https:"))) {
        throw new Error('Invalid deployment address: Must start with "https://" or "http://". Found "'.concat(deploymentUrl, '".'));
    }
    try {
        new URL(deploymentUrl);
    } catch (e) {
        throw new Error('Invalid deployment address: "'.concat(deploymentUrl, '" is not a valid URL. If you believe this URL is correct, use the `skipConvexDeploymentUrlCheck` option to bypass this.'));
    }
    if (deploymentUrl.endsWith(".convex.site")) {
        throw new Error('Invalid deployment address: "'.concat(deploymentUrl, '" ends with .convex.site, which is used for HTTP Actions. Convex deployment URLs typically end with .convex.cloud? If you believe this URL is correct, use the `skipConvexDeploymentUrlCheck` option to bypass this.'));
    }
}
function isSimpleObject(value) {
    var // Objects generated from other contexts (e.g. across Node.js `vm` modules) will not satisfy the previous
    // conditions but are still simple objects.
    _prototype_constructor;
    const isObject = typeof value === "object";
    const prototype = Object.getPrototypeOf(value);
    const isSimple = prototype === null || prototype === Object.prototype || (prototype === null || prototype === void 0 ? void 0 : (_prototype_constructor = prototype.constructor) === null || _prototype_constructor === void 0 ? void 0 : _prototype_constructor.name) === "Object";
    return isObject && isSimple;
} //# sourceMappingURL=index.js.map
}),
"[project]/node_modules/convex/dist/esm/values/value.js [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "base64ToBigInt",
    ()=>base64ToBigInt,
    "bigIntToBase64",
    ()=>bigIntToBase64,
    "convexOrUndefinedToJson",
    ()=>convexOrUndefinedToJson,
    "convexToJson",
    ()=>convexToJson,
    "jsonToConvex",
    ()=>jsonToConvex,
    "modernBase64ToBigInt",
    ()=>modernBase64ToBigInt,
    "modernBigIntToBase64",
    ()=>modernBigIntToBase64,
    "patchValueToJson",
    ()=>patchValueToJson,
    "slowBase64ToBigInt",
    ()=>slowBase64ToBigInt,
    "slowBigIntToBase64",
    ()=>slowBigIntToBase64,
    "stringifyValueForError",
    ()=>stringifyValueForError
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$base64$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/values/base64.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$common$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/common/index.js [app-client] (ecmascript)");
"use strict";
;
;
const LITTLE_ENDIAN = true;
const MIN_INT64 = BigInt("-9223372036854775808");
const MAX_INT64 = BigInt("9223372036854775807");
const ZERO = BigInt("0");
const EIGHT = BigInt("8");
const TWOFIFTYSIX = BigInt("256");
function isSpecial(n) {
    return Number.isNaN(n) || !Number.isFinite(n) || Object.is(n, -0);
}
function slowBigIntToBase64(value) {
    if (value < ZERO) {
        value -= MIN_INT64 + MIN_INT64;
    }
    let hex = value.toString(16);
    if (hex.length % 2 === 1) hex = "0" + hex;
    const bytes = new Uint8Array(new ArrayBuffer(8));
    let i = 0;
    for (const hexByte of hex.match(/.{2}/g).reverse()){
        bytes.set([
            parseInt(hexByte, 16)
        ], i++);
        value >>= EIGHT;
    }
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$base64$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fromByteArray"](bytes);
}
function slowBase64ToBigInt(encoded) {
    const integerBytes = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$base64$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toByteArray"](encoded);
    if (integerBytes.byteLength !== 8) {
        throw new Error("Received ".concat(integerBytes.byteLength, " bytes, expected 8 for $integer"));
    }
    let value = ZERO;
    let power = ZERO;
    for (const byte of integerBytes){
        value += BigInt(byte) * TWOFIFTYSIX ** power;
        power++;
    }
    if (value > MAX_INT64) {
        value += MIN_INT64 + MIN_INT64;
    }
    return value;
}
function modernBigIntToBase64(value) {
    if (value < MIN_INT64 || MAX_INT64 < value) {
        throw new Error("BigInt ".concat(value, " does not fit into a 64-bit signed integer."));
    }
    const buffer = new ArrayBuffer(8);
    new DataView(buffer).setBigInt64(0, value, true);
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$base64$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fromByteArray"](new Uint8Array(buffer));
}
function modernBase64ToBigInt(encoded) {
    const integerBytes = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$base64$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toByteArray"](encoded);
    if (integerBytes.byteLength !== 8) {
        throw new Error("Received ".concat(integerBytes.byteLength, " bytes, expected 8 for $integer"));
    }
    const intBytesView = new DataView(integerBytes.buffer);
    return intBytesView.getBigInt64(0, true);
}
const bigIntToBase64 = DataView.prototype.setBigInt64 ? modernBigIntToBase64 : slowBigIntToBase64;
const base64ToBigInt = DataView.prototype.getBigInt64 ? modernBase64ToBigInt : slowBase64ToBigInt;
const MAX_IDENTIFIER_LEN = 1024;
function validateObjectField(k) {
    if (k.length > MAX_IDENTIFIER_LEN) {
        throw new Error("Field name ".concat(k, " exceeds maximum field name length ").concat(MAX_IDENTIFIER_LEN, "."));
    }
    if (k.startsWith("$")) {
        throw new Error("Field name ".concat(k, " starts with a '$', which is reserved."));
    }
    for(let i = 0; i < k.length; i += 1){
        const charCode = k.charCodeAt(i);
        if (charCode < 32 || charCode >= 127) {
            throw new Error("Field name ".concat(k, " has invalid character '").concat(k[i], "': Field names can only contain non-control ASCII characters"));
        }
    }
}
function jsonToConvex(value) {
    if (value === null) {
        return value;
    }
    if (typeof value === "boolean") {
        return value;
    }
    if (typeof value === "number") {
        return value;
    }
    if (typeof value === "string") {
        return value;
    }
    if (Array.isArray(value)) {
        return value.map((value2)=>jsonToConvex(value2));
    }
    if (typeof value !== "object") {
        throw new Error("Unexpected type of ".concat(value));
    }
    const entries = Object.entries(value);
    if (entries.length === 1) {
        const key = entries[0][0];
        if (key === "$bytes") {
            if (typeof value.$bytes !== "string") {
                throw new Error("Malformed $bytes field on ".concat(value));
            }
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$base64$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toByteArray"](value.$bytes).buffer;
        }
        if (key === "$integer") {
            if (typeof value.$integer !== "string") {
                throw new Error("Malformed $integer field on ".concat(value));
            }
            return base64ToBigInt(value.$integer);
        }
        if (key === "$float") {
            if (typeof value.$float !== "string") {
                throw new Error("Malformed $float field on ".concat(value));
            }
            const floatBytes = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$base64$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toByteArray"](value.$float);
            if (floatBytes.byteLength !== 8) {
                throw new Error("Received ".concat(floatBytes.byteLength, " bytes, expected 8 for $float"));
            }
            const floatBytesView = new DataView(floatBytes.buffer);
            const float = floatBytesView.getFloat64(0, LITTLE_ENDIAN);
            if (!isSpecial(float)) {
                throw new Error("Float ".concat(float, " should be encoded as a number"));
            }
            return float;
        }
        if (key === "$set") {
            throw new Error("Received a Set which is no longer supported as a Convex type.");
        }
        if (key === "$map") {
            throw new Error("Received a Map which is no longer supported as a Convex type.");
        }
    }
    const out = {};
    for (const [k, v] of Object.entries(value)){
        validateObjectField(k);
        out[k] = jsonToConvex(v);
    }
    return out;
}
function stringifyValueForError(value) {
    return JSON.stringify(value, (_key, value2)=>{
        if (value2 === void 0) {
            return "undefined";
        }
        if (typeof value2 === "bigint") {
            return "".concat(value2.toString(), "n");
        }
        return value2;
    });
}
function convexToJsonInternal(value, originalValue, context, includeTopLevelUndefined) {
    if (value === void 0) {
        const contextText = context && " (present at path ".concat(context, " in original object ").concat(stringifyValueForError(originalValue), ")");
        throw new Error("undefined is not a valid Convex value".concat(contextText, ". To learn about Convex's supported types, see https://docs.convex.dev/using/types."));
    }
    if (value === null) {
        return value;
    }
    if (typeof value === "bigint") {
        if (value < MIN_INT64 || MAX_INT64 < value) {
            throw new Error("BigInt ".concat(value, " does not fit into a 64-bit signed integer."));
        }
        return {
            $integer: bigIntToBase64(value)
        };
    }
    if (typeof value === "number") {
        if (isSpecial(value)) {
            const buffer = new ArrayBuffer(8);
            new DataView(buffer).setFloat64(0, value, LITTLE_ENDIAN);
            return {
                $float: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$base64$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fromByteArray"](new Uint8Array(buffer))
            };
        } else {
            return value;
        }
    }
    if (typeof value === "boolean") {
        return value;
    }
    if (typeof value === "string") {
        return value;
    }
    if (value instanceof ArrayBuffer) {
        return {
            $bytes: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$base64$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fromByteArray"](new Uint8Array(value))
        };
    }
    if (Array.isArray(value)) {
        return value.map((value2, i)=>convexToJsonInternal(value2, originalValue, context + "[".concat(i, "]"), false));
    }
    if (value instanceof Set) {
        throw new Error(errorMessageForUnsupportedType(context, "Set", [
            ...value
        ], originalValue));
    }
    if (value instanceof Map) {
        throw new Error(errorMessageForUnsupportedType(context, "Map", [
            ...value
        ], originalValue));
    }
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$common$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isSimpleObject"])(value)) {
        var _value_constructor;
        const theType = value === null || value === void 0 ? void 0 : (_value_constructor = value.constructor) === null || _value_constructor === void 0 ? void 0 : _value_constructor.name;
        const typeName = theType ? "".concat(theType, " ") : "";
        throw new Error(errorMessageForUnsupportedType(context, typeName, value, originalValue));
    }
    const out = {};
    const entries = Object.entries(value);
    entries.sort((param, param1)=>{
        let [k1, _v1] = param, [k2, _v2] = param1;
        return k1 === k2 ? 0 : k1 < k2 ? -1 : 1;
    });
    for (const [k, v] of entries){
        if (v !== void 0) {
            validateObjectField(k);
            out[k] = convexToJsonInternal(v, originalValue, context + ".".concat(k), false);
        } else if (includeTopLevelUndefined) {
            validateObjectField(k);
            out[k] = convexOrUndefinedToJsonInternal(v, originalValue, context + ".".concat(k));
        }
    }
    return out;
}
function errorMessageForUnsupportedType(context, typeName, value, originalValue) {
    if (context) {
        return "".concat(typeName).concat(stringifyValueForError(value), " is not a supported Convex type (present at path ").concat(context, " in original object ").concat(stringifyValueForError(originalValue), "). To learn about Convex's supported types, see https://docs.convex.dev/using/types.");
    } else {
        return "".concat(typeName).concat(stringifyValueForError(value), " is not a supported Convex type.");
    }
}
function convexOrUndefinedToJsonInternal(value, originalValue, context) {
    if (value === void 0) {
        return {
            $undefined: null
        };
    } else {
        if (originalValue === void 0) {
            throw new Error("Programming error. Current value is ".concat(stringifyValueForError(value), " but original value is undefined"));
        }
        return convexToJsonInternal(value, originalValue, context, false);
    }
}
function convexToJson(value) {
    return convexToJsonInternal(value, value, "", false);
}
function convexOrUndefinedToJson(value) {
    return convexOrUndefinedToJsonInternal(value, value, "");
}
function patchValueToJson(value) {
    return convexToJsonInternal(value, value, "", true);
} //# sourceMappingURL=value.js.map
}),
"[project]/node_modules/convex/dist/esm/values/validators.js [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "VAny",
    ()=>VAny,
    "VArray",
    ()=>VArray,
    "VBoolean",
    ()=>VBoolean,
    "VBytes",
    ()=>VBytes,
    "VFloat64",
    ()=>VFloat64,
    "VId",
    ()=>VId,
    "VInt64",
    ()=>VInt64,
    "VLiteral",
    ()=>VLiteral,
    "VNull",
    ()=>VNull,
    "VObject",
    ()=>VObject,
    "VRecord",
    ()=>VRecord,
    "VString",
    ()=>VString,
    "VUnion",
    ()=>VUnion
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$value$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/values/value.js [app-client] (ecmascript)");
"use strict";
var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value)=>key in obj ? __defProp(obj, key, {
        enumerable: true,
        configurable: true,
        writable: true,
        value
    }) : obj[key] = value;
var __publicField = (obj, key, value)=>__defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
;
class BaseValidator {
    /** @deprecated - use isOptional instead */ get optional() {
        return this.isOptional === "optional" ? true : false;
    }
    constructor({ isOptional }){
        /**
     * Only for TypeScript, the TS type of the JS values validated
     * by this validator.
     */ __publicField(this, "type");
        /**
     * Only for TypeScript, if this an Object validator, then
     * this is the TS type of its property names.
     */ __publicField(this, "fieldPaths");
        /**
     * Whether this is an optional Object property value validator.
     */ __publicField(this, "isOptional");
        /**
     * Always `"true"`.
     */ __publicField(this, "isConvexValidator");
        this.isOptional = isOptional;
        this.isConvexValidator = true;
    }
}
class VId extends BaseValidator {
    /** @internal */ get json() {
        return {
            type: "id",
            tableName: this.tableName
        };
    }
    /** @internal */ asOptional() {
        return new VId({
            isOptional: "optional",
            tableName: this.tableName
        });
    }
    /**
   * Usually you'd use `v.id(tableName)` instead.
   */ constructor({ isOptional, tableName }){
        super({
            isOptional
        });
        /**
     * The name of the table that the validated IDs must belong to.
     */ __publicField(this, "tableName");
        /**
     * The kind of validator, `"id"`.
     */ __publicField(this, "kind", "id");
        if (typeof tableName !== "string") {
            throw new Error("v.id(tableName) requires a string");
        }
        this.tableName = tableName;
    }
}
class VFloat64 extends BaseValidator {
    /** @internal */ get json() {
        return {
            type: "number"
        };
    }
    /** @internal */ asOptional() {
        return new VFloat64({
            isOptional: "optional"
        });
    }
    constructor(){
        super(...arguments);
        /**
     * The kind of validator, `"float64"`.
     */ __publicField(this, "kind", "float64");
    }
}
class VInt64 extends BaseValidator {
    /** @internal */ get json() {
        return {
            type: "bigint"
        };
    }
    /** @internal */ asOptional() {
        return new VInt64({
            isOptional: "optional"
        });
    }
    constructor(){
        super(...arguments);
        /**
     * The kind of validator, `"int64"`.
     */ __publicField(this, "kind", "int64");
    }
}
class VBoolean extends BaseValidator {
    /** @internal */ get json() {
        return {
            type: this.kind
        };
    }
    /** @internal */ asOptional() {
        return new VBoolean({
            isOptional: "optional"
        });
    }
    constructor(){
        super(...arguments);
        /**
     * The kind of validator, `"boolean"`.
     */ __publicField(this, "kind", "boolean");
    }
}
class VBytes extends BaseValidator {
    /** @internal */ get json() {
        return {
            type: this.kind
        };
    }
    /** @internal */ asOptional() {
        return new VBytes({
            isOptional: "optional"
        });
    }
    constructor(){
        super(...arguments);
        /**
     * The kind of validator, `"bytes"`.
     */ __publicField(this, "kind", "bytes");
    }
}
class VString extends BaseValidator {
    /** @internal */ get json() {
        return {
            type: this.kind
        };
    }
    /** @internal */ asOptional() {
        return new VString({
            isOptional: "optional"
        });
    }
    constructor(){
        super(...arguments);
        /**
     * The kind of validator, `"string"`.
     */ __publicField(this, "kind", "string");
    }
}
class VNull extends BaseValidator {
    /** @internal */ get json() {
        return {
            type: this.kind
        };
    }
    /** @internal */ asOptional() {
        return new VNull({
            isOptional: "optional"
        });
    }
    constructor(){
        super(...arguments);
        /**
     * The kind of validator, `"null"`.
     */ __publicField(this, "kind", "null");
    }
}
class VAny extends BaseValidator {
    /** @internal */ get json() {
        return {
            type: this.kind
        };
    }
    /** @internal */ asOptional() {
        return new VAny({
            isOptional: "optional"
        });
    }
    constructor(){
        super(...arguments);
        /**
     * The kind of validator, `"any"`.
     */ __publicField(this, "kind", "any");
    }
}
class VObject extends BaseValidator {
    /** @internal */ get json() {
        return {
            type: this.kind,
            value: globalThis.Object.fromEntries(globalThis.Object.entries(this.fields).map((param)=>{
                let [k, v] = param;
                return [
                    k,
                    {
                        fieldType: v.json,
                        optional: v.isOptional === "optional" ? true : false
                    }
                ];
            }))
        };
    }
    /** @internal */ asOptional() {
        return new VObject({
            isOptional: "optional",
            fields: this.fields
        });
    }
    /**
   * Usually you'd use `v.object({ ... })` instead.
   */ constructor({ isOptional, fields }){
        super({
            isOptional
        });
        /**
     * An object with the validator for each property.
     */ __publicField(this, "fields");
        /**
     * The kind of validator, `"object"`.
     */ __publicField(this, "kind", "object");
        globalThis.Object.values(fields).forEach((v)=>{
            if (!v.isConvexValidator) {
                throw new Error("v.object() entries must be valiators");
            }
        });
        this.fields = fields;
    }
}
class VLiteral extends BaseValidator {
    /** @internal */ get json() {
        return {
            type: this.kind,
            value: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$value$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["convexToJson"])(this.value)
        };
    }
    /** @internal */ asOptional() {
        return new VLiteral({
            isOptional: "optional",
            value: this.value
        });
    }
    /**
   * Usually you'd use `v.literal(value)` instead.
   */ constructor({ isOptional, value }){
        super({
            isOptional
        });
        /**
     * The value that the validated values must be equal to.
     */ __publicField(this, "value");
        /**
     * The kind of validator, `"literal"`.
     */ __publicField(this, "kind", "literal");
        if (typeof value !== "string" && typeof value !== "boolean" && typeof value !== "number" && typeof value !== "bigint") {
            throw new Error("v.literal(value) must be a string, number, or boolean");
        }
        this.value = value;
    }
}
class VArray extends BaseValidator {
    /** @internal */ get json() {
        return {
            type: this.kind,
            value: this.element.json
        };
    }
    /** @internal */ asOptional() {
        return new VArray({
            isOptional: "optional",
            element: this.element
        });
    }
    /**
   * Usually you'd use `v.array(element)` instead.
   */ constructor({ isOptional, element }){
        super({
            isOptional
        });
        /**
     * The validator for the elements of the array.
     */ __publicField(this, "element");
        /**
     * The kind of validator, `"array"`.
     */ __publicField(this, "kind", "array");
        this.element = element;
    }
}
class VRecord extends BaseValidator {
    /** @internal */ get json() {
        return {
            type: this.kind,
            // This cast is needed because TypeScript thinks the key type is too wide
            keys: this.key.json,
            values: {
                fieldType: this.value.json,
                optional: false
            }
        };
    }
    /** @internal */ asOptional() {
        return new VRecord({
            isOptional: "optional",
            key: this.key,
            value: this.value
        });
    }
    /**
   * Usually you'd use `v.record(key, value)` instead.
   */ constructor({ isOptional, key, value }){
        super({
            isOptional
        });
        /**
     * The validator for the keys of the record.
     */ __publicField(this, "key");
        /**
     * The validator for the values of the record.
     */ __publicField(this, "value");
        /**
     * The kind of validator, `"record"`.
     */ __publicField(this, "kind", "record");
        if (key.isOptional === "optional") {
            throw new Error("Record validator cannot have optional keys");
        }
        if (value.isOptional === "optional") {
            throw new Error("Record validator cannot have optional values");
        }
        if (!key.isConvexValidator || !value.isConvexValidator) {
            throw new Error("Key and value of v.record() but be validators");
        }
        this.key = key;
        this.value = value;
    }
}
class VUnion extends BaseValidator {
    /** @internal */ get json() {
        return {
            type: this.kind,
            value: this.members.map((v)=>v.json)
        };
    }
    /** @internal */ asOptional() {
        return new VUnion({
            isOptional: "optional",
            members: this.members
        });
    }
    /**
   * Usually you'd use `v.union(...members)` instead.
   */ constructor({ isOptional, members }){
        super({
            isOptional
        });
        /**
     * The array of validators, one of which must match the value.
     */ __publicField(this, "members");
        /**
     * The kind of validator, `"union"`.
     */ __publicField(this, "kind", "union");
        members.forEach((member)=>{
            if (!member.isConvexValidator) {
                throw new Error("All members of v.union() must be validators");
            }
        });
        this.members = members;
    }
} //# sourceMappingURL=validators.js.map
}),
"[project]/node_modules/convex/dist/esm/values/validator.js [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "asObjectValidator",
    ()=>asObjectValidator,
    "isValidator",
    ()=>isValidator,
    "v",
    ()=>v
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$validators$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/values/validators.js [app-client] (ecmascript)");
"use strict";
;
function isValidator(v2) {
    return !!v2.isConvexValidator;
}
function asObjectValidator(obj) {
    if (isValidator(obj)) {
        return obj;
    } else {
        return v.object(obj);
    }
}
const v = {
    /**
   * Validates that the value corresponds to an ID of a document in given table.
   * @param tableName The name of the table.
   */ id: (tableName)=>{
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$validators$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["VId"]({
            isOptional: "required",
            tableName
        });
    },
    /**
   * Validates that the value is of type Null.
   */ null: ()=>{
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$validators$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["VNull"]({
            isOptional: "required"
        });
    },
    /**
   * Validates that the value is of Convex type Float64 (Number in JS).
   *
   * Alias for `v.float64()`
   */ number: ()=>{
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$validators$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["VFloat64"]({
            isOptional: "required"
        });
    },
    /**
   * Validates that the value is of Convex type Float64 (Number in JS).
   */ float64: ()=>{
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$validators$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["VFloat64"]({
            isOptional: "required"
        });
    },
    /**
   * @deprecated Use `v.int64()` instead
   */ bigint: ()=>{
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$validators$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["VInt64"]({
            isOptional: "required"
        });
    },
    /**
   * Validates that the value is of Convex type Int64 (BigInt in JS).
   */ int64: ()=>{
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$validators$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["VInt64"]({
            isOptional: "required"
        });
    },
    /**
   * Validates that the value is of type Boolean.
   */ boolean: ()=>{
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$validators$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["VBoolean"]({
            isOptional: "required"
        });
    },
    /**
   * Validates that the value is of type String.
   */ string: ()=>{
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$validators$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["VString"]({
            isOptional: "required"
        });
    },
    /**
   * Validates that the value is of Convex type Bytes (constructed in JS via `ArrayBuffer`).
   */ bytes: ()=>{
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$validators$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["VBytes"]({
            isOptional: "required"
        });
    },
    /**
   * Validates that the value is equal to the given literal value.
   * @param literal The literal value to compare against.
   */ literal: (literal)=>{
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$validators$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["VLiteral"]({
            isOptional: "required",
            value: literal
        });
    },
    /**
   * Validates that the value is an Array of the given element type.
   * @param element The validator for the elements of the array.
   */ array: (element)=>{
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$validators$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["VArray"]({
            isOptional: "required",
            element
        });
    },
    /**
   * Validates that the value is an Object with the given properties.
   * @param fields An object specifying the validator for each property.
   */ object: (fields)=>{
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$validators$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["VObject"]({
            isOptional: "required",
            fields
        });
    },
    /**
   * Validates that the value is a Record with keys and values that match the given types.
   * @param keys The validator for the keys of the record. This cannot contain string literals.
   * @param values The validator for the values of the record.
   */ record: (keys, values)=>{
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$validators$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["VRecord"]({
            isOptional: "required",
            key: keys,
            value: values
        });
    },
    /**
   * Validates that the value matches one of the given validators.
   * @param members The validators to match against.
   */ union: function() {
        for(var _len = arguments.length, members = new Array(_len), _key = 0; _key < _len; _key++){
            members[_key] = arguments[_key];
        }
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$validators$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["VUnion"]({
            isOptional: "required",
            members
        });
    },
    /**
   * Does not validate the value.
   */ any: ()=>{
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$validators$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["VAny"]({
            isOptional: "required"
        });
    },
    /**
   * Allows not specifying a value for a property in an Object.
   * @param value The property value validator to make optional.
   *
   * ```typescript
   * const objectWithOptionalFields = v.object({
   *   requiredField: v.string(),
   *   optionalField: v.optional(v.string()),
   * });
   * ```
   */ optional: (value)=>{
        return value.asOptional();
    }
}; //# sourceMappingURL=validator.js.map
}),
"[project]/node_modules/convex/dist/esm/values/errors.js [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "ConvexError",
    ()=>ConvexError
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$value$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/values/value.js [app-client] (ecmascript)");
"use strict";
var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value)=>key in obj ? __defProp(obj, key, {
        enumerable: true,
        configurable: true,
        writable: true,
        value
    }) : obj[key] = value;
var __publicField = (obj, key, value)=>__defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
var _a, _b;
;
const IDENTIFYING_FIELD = Symbol.for("ConvexError");
class ConvexError extends (_b = Error, _a = IDENTIFYING_FIELD, _b) {
    constructor(data){
        super(typeof data === "string" ? data : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$value$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["stringifyValueForError"])(data));
        __publicField(this, "name", "ConvexError");
        __publicField(this, "data");
        __publicField(this, _a, true);
        this.data = data;
    }
} //# sourceMappingURL=errors.js.map
}),
"[project]/node_modules/convex/dist/esm/values/compare_utf8.js [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "compareUTF8",
    ()=>compareUTF8,
    "greaterThan",
    ()=>greaterThan,
    "greaterThanEq",
    ()=>greaterThanEq,
    "lessThan",
    ()=>lessThan,
    "lessThanEq",
    ()=>lessThanEq,
    "utf16LengthForCodePoint",
    ()=>utf16LengthForCodePoint
]);
"use strict";
function compareUTF8(a, b) {
    const aLength = a.length;
    const bLength = b.length;
    const length = Math.min(aLength, bLength);
    for(let i = 0; i < length;){
        const aCodePoint = a.codePointAt(i);
        const bCodePoint = b.codePointAt(i);
        if (aCodePoint !== bCodePoint) {
            if (aCodePoint < 128 && bCodePoint < 128) {
                return aCodePoint - bCodePoint;
            }
            const aLength2 = utf8Bytes(aCodePoint, aBytes);
            const bLength2 = utf8Bytes(bCodePoint, bBytes);
            return compareArrays(aBytes, aLength2, bBytes, bLength2);
        }
        i += utf16LengthForCodePoint(aCodePoint);
    }
    return aLength - bLength;
}
function compareArrays(a, aLength, b, bLength) {
    const length = Math.min(aLength, bLength);
    for(let i = 0; i < length; i++){
        const aValue = a[i];
        const bValue = b[i];
        if (aValue !== bValue) {
            return aValue - bValue;
        }
    }
    return aLength - bLength;
}
function utf16LengthForCodePoint(aCodePoint) {
    return aCodePoint > 65535 ? 2 : 1;
}
const arr = ()=>Array.from({
        length: 4
    }, ()=>0);
const aBytes = arr();
const bBytes = arr();
function utf8Bytes(codePoint, bytes) {
    if (codePoint < 128) {
        bytes[0] = codePoint;
        return 1;
    }
    let count;
    let offset;
    if (codePoint <= 2047) {
        count = 1;
        offset = 192;
    } else if (codePoint <= 65535) {
        count = 2;
        offset = 224;
    } else if (codePoint <= 1114111) {
        count = 3;
        offset = 240;
    } else {
        throw new Error("Invalid code point");
    }
    bytes[0] = (codePoint >> 6 * count) + offset;
    let i = 1;
    for(; count > 0; count--){
        const temp = codePoint >> 6 * (count - 1);
        bytes[i++] = 128 | temp & 63;
    }
    return i;
}
function greaterThan(a, b) {
    return compareUTF8(a, b) > 0;
}
function greaterThanEq(a, b) {
    return compareUTF8(a, b) >= 0;
}
function lessThan(a, b) {
    return compareUTF8(a, b) < 0;
}
function lessThanEq(a, b) {
    return compareUTF8(a, b) <= 0;
} //# sourceMappingURL=compare_utf8.js.map
}),
"[project]/node_modules/convex/dist/esm/values/compare.js [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "compareValues",
    ()=>compareValues
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$compare_utf8$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/values/compare_utf8.js [app-client] (ecmascript)");
"use strict";
;
function compareValues(k1, k2) {
    return compareAsTuples(makeComparable(k1), makeComparable(k2));
}
function compareAsTuples(a, b) {
    if (a[0] === b[0]) {
        return compareSameTypeValues(a[1], b[1]);
    } else if (a[0] < b[0]) {
        return -1;
    }
    return 1;
}
function compareSameTypeValues(v1, v2) {
    if (v1 === void 0 || v1 === null) {
        return 0;
    }
    if (typeof v1 === "number") {
        if (typeof v2 !== "number") {
            throw new Error("Unexpected type ".concat(v2));
        }
        return compareNumbers(v1, v2);
    }
    if (typeof v1 === "string") {
        if (typeof v2 !== "string") {
            throw new Error("Unexpected type ".concat(v2));
        }
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$compare_utf8$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["compareUTF8"])(v1, v2);
    }
    if (typeof v1 === "bigint" || typeof v1 === "boolean" || typeof v1 === "string") {
        return v1 < v2 ? -1 : v1 === v2 ? 0 : 1;
    }
    if (!Array.isArray(v1) || !Array.isArray(v2)) {
        throw new Error("Unexpected type ".concat(v1));
    }
    for(let i = 0; i < v1.length && i < v2.length; i++){
        const cmp = compareAsTuples(v1[i], v2[i]);
        if (cmp !== 0) {
            return cmp;
        }
    }
    if (v1.length < v2.length) {
        return -1;
    }
    if (v1.length > v2.length) {
        return 1;
    }
    return 0;
}
function compareNumbers(v1, v2) {
    if (isNaN(v1) || isNaN(v2)) {
        const buffer1 = new ArrayBuffer(8);
        const buffer2 = new ArrayBuffer(8);
        new DataView(buffer1).setFloat64(0, v1, /* little-endian */ true);
        new DataView(buffer2).setFloat64(0, v2, /* little-endian */ true);
        const v1Bits = BigInt(new DataView(buffer1).getBigInt64(0, /* little-endian */ true));
        const v2Bits = BigInt(new DataView(buffer2).getBigInt64(0, /* little-endian */ true));
        const v1Sign = (v1Bits & 0x8000000000000000n) !== 0n;
        const v2Sign = (v2Bits & 0x8000000000000000n) !== 0n;
        if (isNaN(v1) !== isNaN(v2)) {
            if (isNaN(v1)) {
                return v1Sign ? -1 : 1;
            }
            return v2Sign ? 1 : -1;
        }
        if (v1Sign !== v2Sign) {
            return v1Sign ? -1 : 1;
        }
        return v1Bits < v2Bits ? -1 : v1Bits === v2Bits ? 0 : 1;
    }
    if (Object.is(v1, v2)) {
        return 0;
    }
    if (Object.is(v1, -0)) {
        return Object.is(v2, 0) ? -1 : -Math.sign(v2);
    }
    if (Object.is(v2, -0)) {
        return Object.is(v1, 0) ? 1 : Math.sign(v1);
    }
    return v1 < v2 ? -1 : 1;
}
function makeComparable(v) {
    if (v === void 0) {
        return [
            0,
            void 0
        ];
    }
    if (v === null) {
        return [
            1,
            null
        ];
    }
    if (typeof v === "bigint") {
        return [
            2,
            v
        ];
    }
    if (typeof v === "number") {
        return [
            3,
            v
        ];
    }
    if (typeof v === "boolean") {
        return [
            4,
            v
        ];
    }
    if (typeof v === "string") {
        return [
            5,
            v
        ];
    }
    if (v instanceof ArrayBuffer) {
        return [
            6,
            Array.from(new Uint8Array(v)).map(makeComparable)
        ];
    }
    if (Array.isArray(v)) {
        return [
            7,
            v.map(makeComparable)
        ];
    }
    const keys = Object.keys(v).sort();
    const pojo = keys.map((k)=>[
            k,
            v[k]
        ]);
    return [
        8,
        pojo.map(makeComparable)
    ];
} //# sourceMappingURL=compare.js.map
}),
"[project]/node_modules/convex/dist/esm/values/index.js [app-client] (ecmascript) <locals>", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$value$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/values/value.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$validator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/values/validator.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$base64$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/values/base64.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$errors$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/values/errors.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$compare$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/values/compare.js [app-client] (ecmascript)"); //# sourceMappingURL=index.js.map
"use strict";
;
;
;
;
;
;
}),
"[project]/node_modules/convex/dist/esm/browser/logging.js [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "DefaultLogger",
    ()=>DefaultLogger,
    "createHybridErrorStacktrace",
    ()=>createHybridErrorStacktrace,
    "forwardData",
    ()=>forwardData,
    "instantiateDefaultLogger",
    ()=>instantiateDefaultLogger,
    "instantiateNoopLogger",
    ()=>instantiateNoopLogger,
    "logFatalError",
    ()=>logFatalError,
    "logForFunction",
    ()=>logForFunction
]);
"use strict";
var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value)=>key in obj ? __defProp(obj, key, {
        enumerable: true,
        configurable: true,
        writable: true,
        value
    }) : obj[key] = value;
var __publicField = (obj, key, value)=>__defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
const INFO_COLOR = "color:rgb(0, 145, 255)";
function prefix_for_source(source) {
    switch(source){
        case "query":
            return "Q";
        case "mutation":
            return "M";
        case "action":
            return "A";
        case "any":
            return "?";
    }
}
class DefaultLogger {
    addLogLineListener(func) {
        let id = Math.random().toString(36).substring(2, 15);
        for(let i = 0; i < 10; i++){
            if (this._onLogLineFuncs[id] === void 0) {
                break;
            }
            id = Math.random().toString(36).substring(2, 15);
        }
        this._onLogLineFuncs[id] = func;
        return ()=>{
            delete this._onLogLineFuncs[id];
        };
    }
    logVerbose() {
        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){
            args[_key] = arguments[_key];
        }
        if (this._verbose) {
            for (const func of Object.values(this._onLogLineFuncs)){
                func("debug", "".concat(/* @__PURE__ */ new Date().toISOString()), ...args);
            }
        }
    }
    log() {
        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){
            args[_key] = arguments[_key];
        }
        for (const func of Object.values(this._onLogLineFuncs)){
            func("info", ...args);
        }
    }
    warn() {
        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){
            args[_key] = arguments[_key];
        }
        for (const func of Object.values(this._onLogLineFuncs)){
            func("warn", ...args);
        }
    }
    error() {
        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){
            args[_key] = arguments[_key];
        }
        for (const func of Object.values(this._onLogLineFuncs)){
            func("error", ...args);
        }
    }
    constructor(options){
        __publicField(this, "_onLogLineFuncs");
        __publicField(this, "_verbose");
        this._onLogLineFuncs = {};
        this._verbose = options.verbose;
    }
}
function instantiateDefaultLogger(options) {
    const logger = new DefaultLogger(options);
    logger.addLogLineListener(function(level) {
        for(var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){
            args[_key - 1] = arguments[_key];
        }
        switch(level){
            case "debug":
                console.debug(...args);
                break;
            case "info":
                console.log(...args);
                break;
            case "warn":
                console.warn(...args);
                break;
            case "error":
                console.error(...args);
                break;
            default:
                {
                    level;
                    console.log(...args);
                }
        }
    });
    return logger;
}
function instantiateNoopLogger(options) {
    return new DefaultLogger(options);
}
function logForFunction(logger, type, source, udfPath, message) {
    const prefix = prefix_for_source(source);
    if (typeof message === "object") {
        message = "ConvexError ".concat(JSON.stringify(message.errorData, null, 2));
    }
    if (type === "info") {
        const match = message.match(/^\[.*?\] /);
        if (match === null) {
            logger.error("[CONVEX ".concat(prefix, "(").concat(udfPath, ")] Could not parse console.log"));
            return;
        }
        const level = message.slice(1, match[0].length - 2);
        const args = message.slice(match[0].length);
        logger.log("%c[CONVEX ".concat(prefix, "(").concat(udfPath, ")] [").concat(level, "]"), INFO_COLOR, args);
    } else {
        logger.error("[CONVEX ".concat(prefix, "(").concat(udfPath, ")] ").concat(message));
    }
}
function logFatalError(logger, message) {
    const errorMessage = "[CONVEX FATAL ERROR] ".concat(message);
    logger.error(errorMessage);
    return new Error(errorMessage);
}
function createHybridErrorStacktrace(source, udfPath, result) {
    const prefix = prefix_for_source(source);
    return "[CONVEX ".concat(prefix, "(").concat(udfPath, ")] ").concat(result.errorMessage, "\n  Called by client");
}
function forwardData(result, error) {
    error.data = result.errorData;
    return error;
} //# sourceMappingURL=logging.js.map
}),
"[project]/node_modules/convex/dist/esm/browser/sync/udf_path_utils.js [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "canonicalizeUdfPath",
    ()=>canonicalizeUdfPath,
    "serializePathAndArgs",
    ()=>serializePathAndArgs
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/values/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$value$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/values/value.js [app-client] (ecmascript)");
"use strict";
;
function canonicalizeUdfPath(udfPath) {
    const pieces = udfPath.split(":");
    let moduleName;
    let functionName;
    if (pieces.length === 1) {
        moduleName = pieces[0];
        functionName = "default";
    } else {
        moduleName = pieces.slice(0, pieces.length - 1).join(":");
        functionName = pieces[pieces.length - 1];
    }
    if (moduleName.endsWith(".js")) {
        moduleName = moduleName.slice(0, -3);
    }
    return "".concat(moduleName, ":").concat(functionName);
}
function serializePathAndArgs(udfPath, args) {
    return JSON.stringify({
        udfPath: canonicalizeUdfPath(udfPath),
        args: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$value$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["convexToJson"])(args)
    });
} //# sourceMappingURL=udf_path_utils.js.map
}),
"[project]/node_modules/convex/dist/esm/browser/sync/local_state.js [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "LocalSyncState",
    ()=>LocalSyncState
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/values/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$value$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/values/value.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$udf_path_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/browser/sync/udf_path_utils.js [app-client] (ecmascript)");
"use strict";
var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value)=>key in obj ? __defProp(obj, key, {
        enumerable: true,
        configurable: true,
        writable: true,
        value
    }) : obj[key] = value;
var __publicField = (obj, key, value)=>__defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
;
;
class LocalSyncState {
    hasSyncedPastLastReconnect() {
        return this.outstandingQueriesOlderThanRestart.size === 0 && !this.outstandingAuthOlderThanRestart;
    }
    markAuthCompletion() {
        this.outstandingAuthOlderThanRestart = false;
    }
    subscribe(udfPath, args, journal, componentPath) {
        const canonicalizedUdfPath = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$udf_path_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["canonicalizeUdfPath"])(udfPath);
        const queryToken = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$udf_path_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["serializePathAndArgs"])(canonicalizedUdfPath, args);
        const existingEntry = this.querySet.get(queryToken);
        if (existingEntry !== void 0) {
            existingEntry.numSubscribers += 1;
            return {
                queryToken,
                modification: null,
                unsubscribe: ()=>this.removeSubscriber(queryToken)
            };
        } else {
            const queryId = this.nextQueryId++;
            const query = {
                id: queryId,
                canonicalizedUdfPath,
                args,
                numSubscribers: 1,
                journal,
                componentPath
            };
            this.querySet.set(queryToken, query);
            this.queryIdToToken.set(queryId, queryToken);
            const baseVersion = this.querySetVersion;
            const newVersion = this.querySetVersion + 1;
            const add = {
                type: "Add",
                queryId,
                udfPath: canonicalizedUdfPath,
                args: [
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$value$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["convexToJson"])(args)
                ],
                journal,
                componentPath
            };
            if (this.paused) {
                this.pendingQuerySetModifications.set(queryId, add);
            } else {
                this.querySetVersion = newVersion;
            }
            const modification = {
                type: "ModifyQuerySet",
                baseVersion,
                newVersion,
                modifications: [
                    add
                ]
            };
            return {
                queryToken,
                modification,
                unsubscribe: ()=>this.removeSubscriber(queryToken)
            };
        }
    }
    transition(transition) {
        for (const modification of transition.modifications){
            switch(modification.type){
                case "QueryUpdated":
                case "QueryFailed":
                    {
                        this.outstandingQueriesOlderThanRestart.delete(modification.queryId);
                        const journal = modification.journal;
                        if (journal !== void 0) {
                            const queryToken = this.queryIdToToken.get(modification.queryId);
                            if (queryToken !== void 0) {
                                this.querySet.get(queryToken).journal = journal;
                            }
                        }
                        break;
                    }
                case "QueryRemoved":
                    {
                        this.outstandingQueriesOlderThanRestart.delete(modification.queryId);
                        break;
                    }
                default:
                    {
                        modification;
                        throw new Error("Invalid modification ".concat(modification.type));
                    }
            }
        }
    }
    queryId(udfPath, args) {
        const canonicalizedUdfPath = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$udf_path_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["canonicalizeUdfPath"])(udfPath);
        const queryToken = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$udf_path_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["serializePathAndArgs"])(canonicalizedUdfPath, args);
        const existingEntry = this.querySet.get(queryToken);
        if (existingEntry !== void 0) {
            return existingEntry.id;
        }
        return null;
    }
    isCurrentOrNewerAuthVersion(version) {
        return version >= this.identityVersion;
    }
    setAuth(value) {
        this.auth = {
            tokenType: "User",
            value
        };
        const baseVersion = this.identityVersion;
        if (!this.paused) {
            this.identityVersion = baseVersion + 1;
        }
        return {
            type: "Authenticate",
            baseVersion,
            ...this.auth
        };
    }
    setAdminAuth(value, actingAs) {
        const auth = {
            tokenType: "Admin",
            value,
            impersonating: actingAs
        };
        this.auth = auth;
        const baseVersion = this.identityVersion;
        if (!this.paused) {
            this.identityVersion = baseVersion + 1;
        }
        return {
            type: "Authenticate",
            baseVersion,
            ...auth
        };
    }
    clearAuth() {
        this.auth = void 0;
        this.markAuthCompletion();
        const baseVersion = this.identityVersion;
        if (!this.paused) {
            this.identityVersion = baseVersion + 1;
        }
        return {
            type: "Authenticate",
            tokenType: "None",
            baseVersion
        };
    }
    hasAuth() {
        return !!this.auth;
    }
    isNewAuth(value) {
        var _this_auth;
        return ((_this_auth = this.auth) === null || _this_auth === void 0 ? void 0 : _this_auth.value) !== value;
    }
    queryPath(queryId) {
        const pathAndArgs = this.queryIdToToken.get(queryId);
        if (pathAndArgs) {
            return this.querySet.get(pathAndArgs).canonicalizedUdfPath;
        }
        return null;
    }
    queryArgs(queryId) {
        const pathAndArgs = this.queryIdToToken.get(queryId);
        if (pathAndArgs) {
            return this.querySet.get(pathAndArgs).args;
        }
        return null;
    }
    queryToken(queryId) {
        var _this_queryIdToToken_get;
        return (_this_queryIdToToken_get = this.queryIdToToken.get(queryId)) !== null && _this_queryIdToToken_get !== void 0 ? _this_queryIdToToken_get : null;
    }
    queryJournal(queryToken) {
        var _this_querySet_get;
        return (_this_querySet_get = this.querySet.get(queryToken)) === null || _this_querySet_get === void 0 ? void 0 : _this_querySet_get.journal;
    }
    restart(oldRemoteQueryResults) {
        this.unpause();
        this.outstandingQueriesOlderThanRestart.clear();
        const modifications = [];
        for (const localQuery of this.querySet.values()){
            const add = {
                type: "Add",
                queryId: localQuery.id,
                udfPath: localQuery.canonicalizedUdfPath,
                args: [
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$value$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["convexToJson"])(localQuery.args)
                ],
                journal: localQuery.journal,
                componentPath: localQuery.componentPath
            };
            modifications.push(add);
            if (!oldRemoteQueryResults.has(localQuery.id)) {
                this.outstandingQueriesOlderThanRestart.add(localQuery.id);
            }
        }
        this.querySetVersion = 1;
        const querySet = {
            type: "ModifyQuerySet",
            baseVersion: 0,
            newVersion: 1,
            modifications
        };
        if (!this.auth) {
            this.identityVersion = 0;
            return [
                querySet,
                void 0
            ];
        }
        this.outstandingAuthOlderThanRestart = true;
        const authenticate = {
            type: "Authenticate",
            baseVersion: 0,
            ...this.auth
        };
        this.identityVersion = 1;
        return [
            querySet,
            authenticate
        ];
    }
    pause() {
        this.paused = true;
    }
    resume() {
        const querySet = this.pendingQuerySetModifications.size > 0 ? {
            type: "ModifyQuerySet",
            baseVersion: this.querySetVersion,
            newVersion: ++this.querySetVersion,
            modifications: Array.from(this.pendingQuerySetModifications.values())
        } : void 0;
        const authenticate = this.auth !== void 0 ? {
            type: "Authenticate",
            baseVersion: this.identityVersion++,
            ...this.auth
        } : void 0;
        this.unpause();
        return [
            querySet,
            authenticate
        ];
    }
    unpause() {
        this.paused = false;
        this.pendingQuerySetModifications.clear();
    }
    removeSubscriber(queryToken) {
        const localQuery = this.querySet.get(queryToken);
        if (localQuery.numSubscribers > 1) {
            localQuery.numSubscribers -= 1;
            return null;
        } else {
            this.querySet.delete(queryToken);
            this.queryIdToToken.delete(localQuery.id);
            this.outstandingQueriesOlderThanRestart.delete(localQuery.id);
            const baseVersion = this.querySetVersion;
            const newVersion = this.querySetVersion + 1;
            const remove = {
                type: "Remove",
                queryId: localQuery.id
            };
            if (this.paused) {
                if (this.pendingQuerySetModifications.has(localQuery.id)) {
                    this.pendingQuerySetModifications.delete(localQuery.id);
                } else {
                    this.pendingQuerySetModifications.set(localQuery.id, remove);
                }
            } else {
                this.querySetVersion = newVersion;
            }
            return {
                type: "ModifyQuerySet",
                baseVersion,
                newVersion,
                modifications: [
                    remove
                ]
            };
        }
    }
    constructor(){
        __publicField(this, "nextQueryId");
        __publicField(this, "querySetVersion");
        __publicField(this, "querySet");
        __publicField(this, "queryIdToToken");
        __publicField(this, "identityVersion");
        __publicField(this, "auth");
        __publicField(this, "outstandingQueriesOlderThanRestart");
        __publicField(this, "outstandingAuthOlderThanRestart");
        __publicField(this, "paused");
        __publicField(this, "pendingQuerySetModifications");
        this.nextQueryId = 0;
        this.querySetVersion = 0;
        this.identityVersion = 0;
        this.querySet = /* @__PURE__ */ new Map();
        this.queryIdToToken = /* @__PURE__ */ new Map();
        this.outstandingQueriesOlderThanRestart = /* @__PURE__ */ new Set();
        this.outstandingAuthOlderThanRestart = false;
        this.paused = false;
        this.pendingQuerySetModifications = /* @__PURE__ */ new Map();
    }
} //# sourceMappingURL=local_state.js.map
}),
"[project]/node_modules/convex/dist/esm/browser/sync/request_manager.js [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "RequestManager",
    ()=>RequestManager
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/values/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$value$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/values/value.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$logging$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/browser/logging.js [app-client] (ecmascript)");
"use strict";
var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value)=>key in obj ? __defProp(obj, key, {
        enumerable: true,
        configurable: true,
        writable: true,
        value
    }) : obj[key] = value;
var __publicField = (obj, key, value)=>__defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
;
;
class RequestManager {
    request(message, sent) {
        const result = new Promise((resolve)=>{
            const status = sent ? "Requested" : "NotSent";
            this.inflightRequests.set(message.requestId, {
                message,
                status: {
                    status,
                    requestedAt: /* @__PURE__ */ new Date(),
                    onResult: resolve
                }
            });
            if (message.type === "Mutation") {
                this.inflightMutationsCount++;
            } else if (message.type === "Action") {
                this.inflightActionsCount++;
            }
        });
        this.markConnectionStateDirty();
        return result;
    }
    /**
   * Update the state after receiving a response.
   *
   * @returns A RequestId if the request is complete and its optimistic update
   * can be dropped, null otherwise.
   */ onResponse(response) {
        const requestInfo = this.inflightRequests.get(response.requestId);
        if (requestInfo === void 0) {
            return null;
        }
        if (requestInfo.status.status === "Completed") {
            return null;
        }
        const udfType = requestInfo.message.type === "Mutation" ? "mutation" : "action";
        const udfPath = requestInfo.message.udfPath;
        for (const line of response.logLines){
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$logging$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logForFunction"])(this.logger, "info", udfType, udfPath, line);
        }
        const status = requestInfo.status;
        let result;
        let onResolve;
        if (response.success) {
            result = {
                success: true,
                logLines: response.logLines,
                value: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$value$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsonToConvex"])(response.result)
            };
            onResolve = ()=>status.onResult(result);
        } else {
            const errorMessage = response.result;
            const { errorData } = response;
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$logging$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logForFunction"])(this.logger, "error", udfType, udfPath, errorMessage);
            result = {
                success: false,
                errorMessage,
                errorData: errorData !== void 0 ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$value$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsonToConvex"])(errorData) : void 0,
                logLines: response.logLines
            };
            onResolve = ()=>status.onResult(result);
        }
        if (response.type === "ActionResponse" || !response.success) {
            onResolve();
            this.inflightRequests.delete(response.requestId);
            this.requestsOlderThanRestart.delete(response.requestId);
            if (requestInfo.message.type === "Action") {
                this.inflightActionsCount--;
            } else if (requestInfo.message.type === "Mutation") {
                this.inflightMutationsCount--;
            }
            this.markConnectionStateDirty();
            return {
                requestId: response.requestId,
                result
            };
        }
        requestInfo.status = {
            status: "Completed",
            result,
            ts: response.ts,
            onResolve
        };
        return null;
    }
    // Remove and returns completed requests.
    removeCompleted(ts) {
        const completeRequests = /* @__PURE__ */ new Map();
        for (const [requestId, requestInfo] of this.inflightRequests.entries()){
            const status = requestInfo.status;
            if (status.status === "Completed" && status.ts.lessThanOrEqual(ts)) {
                status.onResolve();
                completeRequests.set(requestId, status.result);
                if (requestInfo.message.type === "Mutation") {
                    this.inflightMutationsCount--;
                } else if (requestInfo.message.type === "Action") {
                    this.inflightActionsCount--;
                }
                this.inflightRequests.delete(requestId);
                this.requestsOlderThanRestart.delete(requestId);
            }
        }
        if (completeRequests.size > 0) {
            this.markConnectionStateDirty();
        }
        return completeRequests;
    }
    restart() {
        this.requestsOlderThanRestart = new Set(this.inflightRequests.keys());
        const allMessages = [];
        for (const [requestId, value] of this.inflightRequests){
            if (value.status.status === "NotSent") {
                value.status.status = "Requested";
                allMessages.push(value.message);
                continue;
            }
            if (value.message.type === "Mutation") {
                allMessages.push(value.message);
            } else if (value.message.type === "Action") {
                this.inflightRequests.delete(requestId);
                this.requestsOlderThanRestart.delete(requestId);
                this.inflightActionsCount--;
                if (value.status.status === "Completed") {
                    throw new Error("Action should never be in 'Completed' state");
                }
                value.status.onResult({
                    success: false,
                    errorMessage: "Connection lost while action was in flight",
                    logLines: []
                });
            }
        }
        this.markConnectionStateDirty();
        return allMessages;
    }
    resume() {
        const allMessages = [];
        for (const [, value] of this.inflightRequests){
            if (value.status.status === "NotSent") {
                value.status.status = "Requested";
                allMessages.push(value.message);
                continue;
            }
        }
        return allMessages;
    }
    /**
   * @returns true if there are any requests that have been requested but have
   * not be completed yet.
   */ hasIncompleteRequests() {
        for (const requestInfo of this.inflightRequests.values()){
            if (requestInfo.status.status === "Requested") {
                return true;
            }
        }
        return false;
    }
    /**
   * @returns true if there are any inflight requests, including ones that have
   * completed on the server, but have not been applied.
   */ hasInflightRequests() {
        return this.inflightRequests.size > 0;
    }
    /**
   * @returns true if there are any inflight requests, that have been hanging around
   * since prior to the most recent restart.
   */ hasSyncedPastLastReconnect() {
        return this.requestsOlderThanRestart.size === 0;
    }
    timeOfOldestInflightRequest() {
        if (this.inflightRequests.size === 0) {
            return null;
        }
        let oldestInflightRequest = Date.now();
        for (const request of this.inflightRequests.values()){
            if (request.status.status !== "Completed") {
                if (request.status.requestedAt.getTime() < oldestInflightRequest) {
                    oldestInflightRequest = request.status.requestedAt.getTime();
                }
            }
        }
        return new Date(oldestInflightRequest);
    }
    /**
   * @returns The number of mutations currently in flight.
   */ inflightMutations() {
        return this.inflightMutationsCount;
    }
    /**
   * @returns The number of actions currently in flight.
   */ inflightActions() {
        return this.inflightActionsCount;
    }
    constructor(logger, markConnectionStateDirty){
        this.logger = logger;
        this.markConnectionStateDirty = markConnectionStateDirty;
        __publicField(this, "inflightRequests");
        __publicField(this, "requestsOlderThanRestart");
        __publicField(this, "inflightMutationsCount", 0);
        __publicField(this, "inflightActionsCount", 0);
        this.inflightRequests = /* @__PURE__ */ new Map();
        this.requestsOlderThanRestart = /* @__PURE__ */ new Set();
    }
} //# sourceMappingURL=request_manager.js.map
}),
"[project]/node_modules/convex/dist/esm/server/functionName.js [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "functionName",
    ()=>functionName
]);
"use strict";
const functionName = Symbol.for("functionName"); //# sourceMappingURL=functionName.js.map
}),
"[project]/node_modules/convex/dist/esm/server/components/paths.js [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "extractReferencePath",
    ()=>extractReferencePath,
    "getFunctionAddress",
    ()=>getFunctionAddress,
    "isFunctionHandle",
    ()=>isFunctionHandle,
    "setReferencePath",
    ()=>setReferencePath,
    "toReferencePath",
    ()=>toReferencePath
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$server$2f$functionName$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/server/functionName.js [app-client] (ecmascript)");
"use strict";
;
const toReferencePath = Symbol.for("toReferencePath");
function setReferencePath(obj, value) {
    obj[toReferencePath] = value;
}
function extractReferencePath(reference) {
    var _reference_toReferencePath;
    return (_reference_toReferencePath = reference[toReferencePath]) !== null && _reference_toReferencePath !== void 0 ? _reference_toReferencePath : null;
}
function isFunctionHandle(s) {
    return s.startsWith("function://");
}
function getFunctionAddress(functionReference) {
    let functionAddress;
    if (typeof functionReference === "string") {
        if (isFunctionHandle(functionReference)) {
            functionAddress = {
                functionHandle: functionReference
            };
        } else {
            functionAddress = {
                name: functionReference
            };
        }
    } else if (functionReference[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$server$2f$functionName$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["functionName"]]) {
        functionAddress = {
            name: functionReference[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$server$2f$functionName$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["functionName"]]
        };
    } else {
        const referencePath = extractReferencePath(functionReference);
        if (!referencePath) {
            throw new Error("".concat(functionReference, " is not a functionReference"));
        }
        functionAddress = {
            reference: referencePath
        };
    }
    return functionAddress;
} //# sourceMappingURL=paths.js.map
}),
"[project]/node_modules/convex/dist/esm/server/api.js [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "anyApi",
    ()=>anyApi,
    "filterApi",
    ()=>filterApi,
    "getFunctionName",
    ()=>getFunctionName,
    "justActions",
    ()=>justActions,
    "justInternal",
    ()=>justInternal,
    "justMutations",
    ()=>justMutations,
    "justPaginatedQueries",
    ()=>justPaginatedQueries,
    "justPublic",
    ()=>justPublic,
    "justQueries",
    ()=>justQueries,
    "justSchedulable",
    ()=>justSchedulable,
    "makeFunctionReference",
    ()=>makeFunctionReference
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$server$2f$functionName$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/server/functionName.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$server$2f$components$2f$paths$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/server/components/paths.js [app-client] (ecmascript)");
"use strict";
;
;
function getFunctionName(functionReference) {
    const address = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$server$2f$components$2f$paths$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getFunctionAddress"])(functionReference);
    if (address.name === void 0) {
        if (address.functionHandle !== void 0) {
            throw new Error('Expected function reference like "api.file.func" or "internal.file.func", but received function handle '.concat(address.functionHandle));
        } else if (address.reference !== void 0) {
            throw new Error('Expected function reference in the current component like "api.file.func" or "internal.file.func", but received reference '.concat(address.reference));
        }
        throw new Error('Expected function reference like "api.file.func" or "internal.file.func", but received '.concat(JSON.stringify(address)));
    }
    if (typeof functionReference === "string") return functionReference;
    const name = functionReference[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$server$2f$functionName$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["functionName"]];
    if (!name) {
        throw new Error("".concat(functionReference, " is not a functionReference"));
    }
    return name;
}
function makeFunctionReference(name) {
    return {
        [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$server$2f$functionName$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["functionName"]]: name
    };
}
function createApi() {
    let pathParts = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : [];
    const handler = {
        get (_, prop) {
            if (typeof prop === "string") {
                const newParts = [
                    ...pathParts,
                    prop
                ];
                return createApi(newParts);
            } else if (prop === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$server$2f$functionName$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["functionName"]) {
                if (pathParts.length < 2) {
                    const found = [
                        "api",
                        ...pathParts
                    ].join(".");
                    throw new Error("API path is expected to be of the form `api.moduleName.functionName`. Found: `".concat(found, "`"));
                }
                const path = pathParts.slice(0, -1).join("/");
                const exportName = pathParts[pathParts.length - 1];
                if (exportName === "default") {
                    return path;
                } else {
                    return path + ":" + exportName;
                }
            } else if (prop === Symbol.toStringTag) {
                return "FunctionReference";
            } else {
                return void 0;
            }
        }
    };
    return new Proxy({}, handler);
}
function filterApi(api) {
    return api;
}
function justInternal(api) {
    return api;
}
function justPublic(api) {
    return api;
}
function justQueries(api) {
    return api;
}
function justMutations(api) {
    return api;
}
function justActions(api) {
    return api;
}
function justPaginatedQueries(api) {
    return api;
}
function justSchedulable(api) {
    return api;
}
const anyApi = createApi(); //# sourceMappingURL=api.js.map
}),
"[project]/node_modules/convex/dist/esm/browser/sync/optimistic_updates_impl.js [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "OptimisticQueryResults",
    ()=>OptimisticQueryResults
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$server$2f$api$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/server/api.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$common$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/common/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$logging$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/browser/logging.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$udf_path_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/browser/sync/udf_path_utils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$errors$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/values/errors.js [app-client] (ecmascript)");
"use strict";
var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value)=>key in obj ? __defProp(obj, key, {
        enumerable: true,
        configurable: true,
        writable: true,
        value
    }) : obj[key] = value;
var __publicField = (obj, key, value)=>__defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
;
;
;
;
;
class OptimisticLocalStoreImpl {
    getQuery(query) {
        for(var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){
            args[_key - 1] = arguments[_key];
        }
        const queryArgs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$common$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseArgs"])(args[0]);
        const name = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$server$2f$api$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getFunctionName"])(query);
        const queryResult = this.queryResults.get((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$udf_path_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["serializePathAndArgs"])(name, queryArgs));
        if (queryResult === void 0) {
            return void 0;
        }
        return OptimisticLocalStoreImpl.queryValue(queryResult.result);
    }
    getAllQueries(query) {
        const queriesWithName = [];
        const name = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$server$2f$api$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getFunctionName"])(query);
        for (const queryResult of this.queryResults.values()){
            if (queryResult.udfPath === (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$udf_path_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["canonicalizeUdfPath"])(name)) {
                queriesWithName.push({
                    args: queryResult.args,
                    value: OptimisticLocalStoreImpl.queryValue(queryResult.result)
                });
            }
        }
        return queriesWithName;
    }
    setQuery(queryReference, args, value) {
        const queryArgs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$common$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseArgs"])(args);
        const name = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$server$2f$api$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getFunctionName"])(queryReference);
        const queryToken = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$udf_path_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["serializePathAndArgs"])(name, queryArgs);
        let result;
        if (value === void 0) {
            result = void 0;
        } else {
            result = {
                success: true,
                value,
                // It's an optimistic update, so there are no function logs to show.
                logLines: []
            };
        }
        const query = {
            udfPath: name,
            args: queryArgs,
            result
        };
        this.queryResults.set(queryToken, query);
        this.modifiedQueries.push(queryToken);
    }
    static queryValue(result) {
        if (result === void 0) {
            return void 0;
        } else if (result.success) {
            return result.value;
        } else {
            return void 0;
        }
    }
    constructor(queryResults){
        // A references of the query results in OptimisticQueryResults
        __publicField(this, "queryResults");
        // All of the queries modified by this class
        __publicField(this, "modifiedQueries");
        this.queryResults = queryResults;
        this.modifiedQueries = [];
    }
}
class OptimisticQueryResults {
    /**
   * Apply all optimistic updates on top of server query results
   */ ingestQueryResultsFromServer(serverQueryResults, optimisticUpdatesToDrop) {
        this.optimisticUpdates = this.optimisticUpdates.filter((updateAndId)=>{
            return !optimisticUpdatesToDrop.has(updateAndId.mutationId);
        });
        const oldQueryResults = this.queryResults;
        this.queryResults = new Map(serverQueryResults);
        const localStore = new OptimisticLocalStoreImpl(this.queryResults);
        for (const updateAndId of this.optimisticUpdates){
            updateAndId.update(localStore);
        }
        const changedQueries = [];
        for (const [queryToken, query] of this.queryResults){
            const oldQuery = oldQueryResults.get(queryToken);
            if (oldQuery === void 0 || oldQuery.result !== query.result) {
                changedQueries.push(queryToken);
            }
        }
        return changedQueries;
    }
    applyOptimisticUpdate(update, mutationId) {
        this.optimisticUpdates.push({
            update,
            mutationId
        });
        const localStore = new OptimisticLocalStoreImpl(this.queryResults);
        update(localStore);
        return localStore.modifiedQueries;
    }
    /**
   * @internal
   */ rawQueryResult(queryToken) {
        return this.queryResults.get(queryToken);
    }
    queryResult(queryToken) {
        const query = this.queryResults.get(queryToken);
        if (query === void 0) {
            return void 0;
        }
        const result = query.result;
        if (result === void 0) {
            return void 0;
        } else if (result.success) {
            return result.value;
        } else {
            if (result.errorData !== void 0) {
                throw (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$logging$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardData"])(result, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$errors$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ConvexError"]((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$logging$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createHybridErrorStacktrace"])("query", query.udfPath, result)));
            }
            throw new Error((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$logging$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createHybridErrorStacktrace"])("query", query.udfPath, result));
        }
    }
    hasQueryResult(queryToken) {
        return this.queryResults.get(queryToken) !== void 0;
    }
    /**
   * @internal
   */ queryLogs(queryToken) {
        var _query_result;
        const query = this.queryResults.get(queryToken);
        return query === null || query === void 0 ? void 0 : (_query_result = query.result) === null || _query_result === void 0 ? void 0 : _query_result.logLines;
    }
    constructor(){
        __publicField(this, "queryResults");
        __publicField(this, "optimisticUpdates");
        this.queryResults = /* @__PURE__ */ new Map();
        this.optimisticUpdates = [];
    }
} //# sourceMappingURL=optimistic_updates_impl.js.map
}),
"[project]/node_modules/convex/dist/esm/browser/long.js [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "Long",
    ()=>Long
]);
"use strict";
var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value)=>key in obj ? __defProp(obj, key, {
        enumerable: true,
        configurable: true,
        writable: true,
        value
    }) : obj[key] = value;
var __publicField = (obj, key, value)=>__defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
class Long {
    static isLong(obj) {
        return (obj && obj.__isUnsignedLong__) === true;
    }
    // prettier-ignore
    static fromBytesLE(bytes) {
        return new Long(bytes[0] | bytes[1] << 8 | bytes[2] << 16 | bytes[3] << 24, bytes[4] | bytes[5] << 8 | bytes[6] << 16 | bytes[7] << 24);
    }
    // prettier-ignore
    toBytesLE() {
        const hi = this.high;
        const lo = this.low;
        return [
            lo & 255,
            lo >>> 8 & 255,
            lo >>> 16 & 255,
            lo >>> 24,
            hi & 255,
            hi >>> 8 & 255,
            hi >>> 16 & 255,
            hi >>> 24
        ];
    }
    static fromNumber(value) {
        if (isNaN(value)) return UZERO;
        if (value < 0) return UZERO;
        if (value >= TWO_PWR_64_DBL) return MAX_UNSIGNED_VALUE;
        return new Long(value % TWO_PWR_32_DBL | 0, value / TWO_PWR_32_DBL | 0);
    }
    toString() {
        return (BigInt(this.high) * BigInt(TWO_PWR_32_DBL) + BigInt(this.low)).toString();
    }
    equals(other) {
        if (!Long.isLong(other)) other = Long.fromValue(other);
        if (this.high >>> 31 === 1 && other.high >>> 31 === 1) return false;
        return this.high === other.high && this.low === other.low;
    }
    notEquals(other) {
        return !this.equals(other);
    }
    comp(other) {
        if (!Long.isLong(other)) other = Long.fromValue(other);
        if (this.equals(other)) return 0;
        return other.high >>> 0 > this.high >>> 0 || other.high === this.high && other.low >>> 0 > this.low >>> 0 ? -1 : 1;
    }
    lessThanOrEqual(other) {
        return this.comp(/* validates */ other) <= 0;
    }
    static fromValue(val) {
        if (typeof val === "number") return Long.fromNumber(val);
        return new Long(val.low, val.high);
    }
    constructor(low, high){
        __publicField(this, "low");
        __publicField(this, "high");
        __publicField(this, "__isUnsignedLong__");
        this.low = low | 0;
        this.high = high | 0;
        this.__isUnsignedLong__ = true;
    }
}
const UZERO = new Long(0, 0);
const TWO_PWR_16_DBL = 1 << 16;
const TWO_PWR_32_DBL = TWO_PWR_16_DBL * TWO_PWR_16_DBL;
const TWO_PWR_64_DBL = TWO_PWR_32_DBL * TWO_PWR_32_DBL;
const MAX_UNSIGNED_VALUE = new Long(4294967295 | 0, 4294967295 | 0); //# sourceMappingURL=long.js.map
}),
"[project]/node_modules/convex/dist/esm/browser/sync/remote_query_set.js [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "RemoteQuerySet",
    ()=>RemoteQuerySet
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/values/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$value$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/values/value.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$long$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/browser/long.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$logging$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/browser/logging.js [app-client] (ecmascript)");
"use strict";
var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value)=>key in obj ? __defProp(obj, key, {
        enumerable: true,
        configurable: true,
        writable: true,
        value
    }) : obj[key] = value;
var __publicField = (obj, key, value)=>__defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
;
;
;
class RemoteQuerySet {
    transition(transition) {
        const start = transition.startVersion;
        if (this.version.querySet !== start.querySet || this.version.ts.notEquals(start.ts) || this.version.identity !== start.identity) {
            throw new Error("Invalid start version: ".concat(start.ts.toString(), ":").concat(start.querySet));
        }
        for (const modification of transition.modifications){
            switch(modification.type){
                case "QueryUpdated":
                    {
                        const queryPath = this.queryPath(modification.queryId);
                        if (queryPath) {
                            for (const line of modification.logLines){
                                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$logging$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logForFunction"])(this.logger, "info", "query", queryPath, line);
                            }
                        }
                        var _modification_value;
                        const value = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$value$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsonToConvex"])((_modification_value = modification.value) !== null && _modification_value !== void 0 ? _modification_value : null);
                        this.remoteQuerySet.set(modification.queryId, {
                            success: true,
                            value,
                            logLines: modification.logLines
                        });
                        break;
                    }
                case "QueryFailed":
                    {
                        const queryPath = this.queryPath(modification.queryId);
                        if (queryPath) {
                            for (const line of modification.logLines){
                                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$logging$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logForFunction"])(this.logger, "info", "query", queryPath, line);
                            }
                        }
                        const { errorData } = modification;
                        this.remoteQuerySet.set(modification.queryId, {
                            success: false,
                            errorMessage: modification.errorMessage,
                            errorData: errorData !== void 0 ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$value$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsonToConvex"])(errorData) : void 0,
                            logLines: modification.logLines
                        });
                        break;
                    }
                case "QueryRemoved":
                    {
                        this.remoteQuerySet.delete(modification.queryId);
                        break;
                    }
                default:
                    {
                        modification;
                        throw new Error("Invalid modification ".concat(modification.type));
                    }
            }
        }
        this.version = transition.endVersion;
    }
    remoteQueryResults() {
        return this.remoteQuerySet;
    }
    timestamp() {
        return this.version.ts;
    }
    constructor(queryPath, logger){
        __publicField(this, "version");
        __publicField(this, "remoteQuerySet");
        __publicField(this, "queryPath");
        __publicField(this, "logger");
        this.version = {
            querySet: 0,
            ts: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$long$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Long"].fromNumber(0),
            identity: 0
        };
        this.remoteQuerySet = /* @__PURE__ */ new Map();
        this.queryPath = queryPath;
        this.logger = logger;
    }
} //# sourceMappingURL=remote_query_set.js.map
}),
"[project]/node_modules/convex/dist/esm/values/base64.js [app-client] (ecmascript) <export * as Base64>", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "Base64",
    ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$base64$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$base64$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/values/base64.js [app-client] (ecmascript)");
}),
"[project]/node_modules/convex/dist/esm/browser/sync/protocol.js [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "encodeClientMessage",
    ()=>encodeClientMessage,
    "longToU64",
    ()=>longToU64,
    "parseServerMessage",
    ()=>parseServerMessage,
    "u64ToLong",
    ()=>u64ToLong
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/values/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$base64$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__Base64$3e$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/values/base64.js [app-client] (ecmascript) <export * as Base64>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$long$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/browser/long.js [app-client] (ecmascript)");
"use strict";
;
;
function u64ToLong(encoded) {
    const integerBytes = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$base64$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__Base64$3e$__["Base64"].toByteArray(encoded);
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$long$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Long"].fromBytesLE(Array.from(integerBytes));
}
function longToU64(raw) {
    const integerBytes = new Uint8Array(raw.toBytesLE());
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$base64$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__Base64$3e$__["Base64"].fromByteArray(integerBytes);
}
function parseServerMessage(encoded) {
    switch(encoded.type){
        case "FatalError":
        case "AuthError":
        case "ActionResponse":
        case "Ping":
            {
                return {
                    ...encoded
                };
            }
        case "MutationResponse":
            {
                if (encoded.success) {
                    return {
                        ...encoded,
                        ts: u64ToLong(encoded.ts)
                    };
                } else {
                    return {
                        ...encoded
                    };
                }
            }
        case "Transition":
            {
                return {
                    ...encoded,
                    startVersion: {
                        ...encoded.startVersion,
                        ts: u64ToLong(encoded.startVersion.ts)
                    },
                    endVersion: {
                        ...encoded.endVersion,
                        ts: u64ToLong(encoded.endVersion.ts)
                    }
                };
            }
        default:
            {
                encoded;
            }
    }
    return void 0;
}
function encodeClientMessage(message) {
    switch(message.type){
        case "Authenticate":
        case "ModifyQuerySet":
        case "Mutation":
        case "Action":
        case "Event":
            {
                return {
                    ...message
                };
            }
        case "Connect":
            {
                if (message.maxObservedTimestamp !== void 0) {
                    return {
                        ...message,
                        maxObservedTimestamp: longToU64(message.maxObservedTimestamp)
                    };
                } else {
                    return {
                        ...message,
                        maxObservedTimestamp: void 0
                    };
                }
            }
        default:
            {
                message;
            }
    }
    return void 0;
} //# sourceMappingURL=protocol.js.map
}),
"[project]/node_modules/convex/dist/esm/browser/sync/web_socket_manager.js [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "WebSocketManager",
    ()=>WebSocketManager
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$protocol$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/browser/sync/protocol.js [app-client] (ecmascript)");
"use strict";
var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value)=>key in obj ? __defProp(obj, key, {
        enumerable: true,
        configurable: true,
        writable: true,
        value
    }) : obj[key] = value;
var __publicField = (obj, key, value)=>__defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
;
const CLOSE_NORMAL = 1e3;
const CLOSE_GOING_AWAY = 1001;
const CLOSE_NO_STATUS = 1005;
const CLOSE_NOT_FOUND = 4040;
const serverDisconnectErrors = {
    // A known error, e.g. during a restart or push
    InternalServerError: {
        timeout: 1e3
    },
    // ErrorMetadata::overloaded() messages that we realy should back off
    SubscriptionsWorkerFullError: {
        timeout: 3e3
    },
    TooManyConcurrentRequests: {
        timeout: 3e3
    },
    CommitterFullError: {
        timeout: 3e3
    },
    AwsTooManyRequestsException: {
        timeout: 3e3
    },
    ExecuteFullError: {
        timeout: 3e3
    },
    SystemTimeoutError: {
        timeout: 3e3
    },
    ExpiredInQueue: {
        timeout: 3e3
    },
    // ErrorMetadata::feature_temporarily_unavailable() that typically indicate a deploy just happened
    VectorIndexesUnavailable: {
        timeout: 1e3
    },
    SearchIndexesUnavailable: {
        timeout: 1e3
    },
    TableSummariesUnavailable: {
        timeout: 1e3
    },
    // More ErrorMeatadata::overloaded()
    VectorIndexTooLarge: {
        timeout: 3e3
    },
    SearchIndexTooLarge: {
        timeout: 3e3
    },
    TooManyWritesInTimePeriod: {
        timeout: 3e3
    }
};
function classifyDisconnectError(s) {
    if (s === void 0) return "Unknown";
    for (const prefix of Object.keys(serverDisconnectErrors)){
        if (s.startsWith(prefix)) {
            return prefix;
        }
    }
    return "Unknown";
}
class WebSocketManager {
    setSocketState(state) {
        this.socket = state;
        this._logVerbose("socket state changed: ".concat(this.socket.state, ", paused: ").concat("paused" in this.socket ? this.socket.paused : void 0));
        this.markConnectionStateDirty();
    }
    connect() {
        if (this.socket.state === "terminated") {
            return;
        }
        if (this.socket.state !== "disconnected" && this.socket.state !== "stopped") {
            throw new Error("Didn't start connection from disconnected state: " + this.socket.state);
        }
        const ws = new this.webSocketConstructor(this.uri);
        this._logVerbose("constructed WebSocket");
        this.setSocketState({
            state: "connecting",
            ws,
            paused: "no"
        });
        this.resetServerInactivityTimeout();
        ws.onopen = ()=>{
            this.logger.logVerbose("begin ws.onopen");
            if (this.socket.state !== "connecting") {
                throw new Error("onopen called with socket not in connecting state");
            }
            this.setSocketState({
                state: "ready",
                ws,
                paused: this.socket.paused === "yes" ? "uninitialized" : "no"
            });
            this.resetServerInactivityTimeout();
            if (this.socket.paused === "no") {
                this._hasEverConnected = true;
                this.onOpen({
                    connectionCount: this.connectionCount,
                    lastCloseReason: this.lastCloseReason
                });
            }
            if (this.lastCloseReason !== "InitialConnect") {
                this.logger.log("WebSocket reconnected");
            }
            this.connectionCount += 1;
            this.lastCloseReason = null;
        };
        ws.onerror = (error)=>{
            const message = error.message;
            this.logger.log("WebSocket error: ".concat(message));
        };
        ws.onmessage = (message)=>{
            this.resetServerInactivityTimeout();
            const serverMessage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$protocol$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseServerMessage"])(JSON.parse(message.data));
            this._logVerbose("received ws message with type ".concat(serverMessage.type));
            const response = this.onMessage(serverMessage);
            if (response.hasSyncedPastLastReconnect) {
                this.retries = 0;
                this.markConnectionStateDirty();
            }
        };
        ws.onclose = (event)=>{
            this._logVerbose("begin ws.onclose");
            if (this.lastCloseReason === null) {
                var _event_reason;
                this.lastCloseReason = (_event_reason = event.reason) !== null && _event_reason !== void 0 ? _event_reason : "OnCloseInvoked";
            }
            if (event.code !== CLOSE_NORMAL && event.code !== CLOSE_GOING_AWAY && // This commonly gets fired on mobile apps when the app is backgrounded
            event.code !== CLOSE_NO_STATUS && event.code !== CLOSE_NOT_FOUND) {
                let msg = "WebSocket closed with code ".concat(event.code);
                if (event.reason) {
                    msg += ": ".concat(event.reason);
                }
                this.logger.log(msg);
                if (this.onServerDisconnectError && event.reason) {
                    this.onServerDisconnectError(msg);
                }
            }
            const reason = classifyDisconnectError(event.reason);
            this.scheduleReconnect(reason);
            return;
        };
    }
    /**
   * @returns The state of the {@link Socket}.
   */ socketState() {
        return this.socket.state;
    }
    /**
   * @param message - A ClientMessage to send.
   * @returns Whether the message (might have been) sent.
   */ sendMessage(message) {
        const messageForLog = {
            type: message.type,
            ...message.type === "Authenticate" && message.tokenType === "User" ? {
                value: "...".concat(message.value.slice(-7))
            } : {}
        };
        if (this.socket.state === "ready" && this.socket.paused === "no") {
            const encodedMessage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$protocol$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["encodeClientMessage"])(message);
            const request = JSON.stringify(encodedMessage);
            try {
                this.socket.ws.send(request);
            } catch (error) {
                this.logger.log("Failed to send message on WebSocket, reconnecting: ".concat(error));
                this.closeAndReconnect("FailedToSendMessage");
            }
            this._logVerbose("sent message with type ".concat(message.type, ": ").concat(JSON.stringify(messageForLog)));
            return true;
        }
        this._logVerbose("message not sent (socket state: ".concat(this.socket.state, ", paused: ").concat("paused" in this.socket ? this.socket.paused : void 0, "): ").concat(JSON.stringify(messageForLog)));
        return false;
    }
    resetServerInactivityTimeout() {
        if (this.socket.state === "terminated") {
            return;
        }
        if (this.reconnectDueToServerInactivityTimeout !== null) {
            clearTimeout(this.reconnectDueToServerInactivityTimeout);
            this.reconnectDueToServerInactivityTimeout = null;
        }
        this.reconnectDueToServerInactivityTimeout = setTimeout(()=>{
            this.closeAndReconnect("InactiveServer");
        }, this.serverInactivityThreshold);
    }
    scheduleReconnect(reason) {
        this.socket = {
            state: "disconnected"
        };
        const backoff = this.nextBackoff(reason);
        this.markConnectionStateDirty();
        this.logger.log("Attempting reconnect in ".concat(backoff, "ms"));
        setTimeout(()=>this.connect(), backoff);
    }
    /**
   * Close the WebSocket and schedule a reconnect.
   *
   * This should be used when we hit an error and would like to restart the session.
   */ closeAndReconnect(closeReason) {
        this._logVerbose("begin closeAndReconnect with reason ".concat(closeReason));
        switch(this.socket.state){
            case "disconnected":
            case "terminated":
            case "stopped":
                return;
            case "connecting":
            case "ready":
                {
                    this.lastCloseReason = closeReason;
                    void this.close();
                    this.scheduleReconnect("client");
                    return;
                }
            default:
                {
                    this.socket;
                }
        }
    }
    /**
   * Close the WebSocket, being careful to clear the onclose handler to avoid re-entrant
   * calls. Use this instead of directly calling `ws.close()`
   *
   * It is the callers responsibility to update the state after this method is called so that the
   * closed socket is not accessible or used again after this method is called
   */ close() {
        switch(this.socket.state){
            case "disconnected":
            case "terminated":
            case "stopped":
                return Promise.resolve();
            case "connecting":
                {
                    const ws = this.socket.ws;
                    return new Promise((r)=>{
                        ws.onclose = ()=>{
                            this._logVerbose("Closed after connecting");
                            r();
                        };
                        ws.onopen = ()=>{
                            this._logVerbose("Opened after connecting");
                            ws.close();
                        };
                    });
                }
            case "ready":
                {
                    this._logVerbose("ws.close called");
                    const ws = this.socket.ws;
                    const result = new Promise((r)=>{
                        ws.onclose = ()=>{
                            r();
                        };
                    });
                    ws.close();
                    return result;
                }
            default:
                {
                    this.socket;
                    return Promise.resolve();
                }
        }
    }
    /**
   * Close the WebSocket and do not reconnect.
   * @returns A Promise that resolves when the WebSocket `onClose` callback is called.
   */ terminate() {
        if (this.reconnectDueToServerInactivityTimeout) {
            clearTimeout(this.reconnectDueToServerInactivityTimeout);
        }
        switch(this.socket.state){
            case "terminated":
            case "stopped":
            case "disconnected":
            case "connecting":
            case "ready":
                {
                    const result = this.close();
                    this.setSocketState({
                        state: "terminated"
                    });
                    return result;
                }
            default:
                {
                    this.socket;
                    throw new Error("Invalid websocket state: ".concat(this.socket.state));
                }
        }
    }
    stop() {
        switch(this.socket.state){
            case "terminated":
                return Promise.resolve();
            case "connecting":
            case "stopped":
            case "disconnected":
            case "ready":
                {
                    const result = this.close();
                    this.socket = {
                        state: "stopped"
                    };
                    return result;
                }
            default:
                {
                    this.socket;
                    return Promise.resolve();
                }
        }
    }
    /**
   * Create a new WebSocket after a previous `stop()`, unless `terminate()` was
   * called before.
   */ tryRestart() {
        switch(this.socket.state){
            case "stopped":
                break;
            case "terminated":
            case "connecting":
            case "ready":
            case "disconnected":
                this.logger.logVerbose("Restart called without stopping first");
                return;
            default:
                {
                    this.socket;
                }
        }
        this.connect();
    }
    pause() {
        switch(this.socket.state){
            case "disconnected":
            case "stopped":
            case "terminated":
                return;
            case "connecting":
            case "ready":
                {
                    this.socket = {
                        ...this.socket,
                        paused: "yes"
                    };
                    return;
                }
            default:
                {
                    this.socket;
                    return;
                }
        }
    }
    /**
   * Resume the state machine if previously paused.
   */ resume() {
        switch(this.socket.state){
            case "connecting":
                this.socket = {
                    ...this.socket,
                    paused: "no"
                };
                return;
            case "ready":
                if (this.socket.paused === "uninitialized") {
                    this.socket = {
                        ...this.socket,
                        paused: "no"
                    };
                    this.onOpen({
                        connectionCount: this.connectionCount,
                        lastCloseReason: this.lastCloseReason
                    });
                } else if (this.socket.paused === "yes") {
                    this.socket = {
                        ...this.socket,
                        paused: "no"
                    };
                    this.onResume();
                }
                return;
            case "terminated":
            case "stopped":
            case "disconnected":
                return;
            default:
                {
                    this.socket;
                }
        }
        this.connect();
    }
    connectionState() {
        return {
            isConnected: this.socket.state === "ready",
            hasEverConnected: this._hasEverConnected,
            connectionCount: this.connectionCount,
            connectionRetries: this.retries
        };
    }
    _logVerbose(message) {
        this.logger.logVerbose(message);
    }
    nextBackoff(reason) {
        const initialBackoff = reason === "client" ? 100 : reason === "Unknown" ? this.defaultInitialBackoff : serverDisconnectErrors[reason].timeout;
        const baseBackoff = initialBackoff * Math.pow(2, this.retries);
        this.retries += 1;
        const actualBackoff = Math.min(baseBackoff, this.maxBackoff);
        const jitter = actualBackoff * (Math.random() - 0.5);
        return actualBackoff + jitter;
    }
    constructor(uri, callbacks, webSocketConstructor, logger, markConnectionStateDirty){
        this.markConnectionStateDirty = markConnectionStateDirty;
        __publicField(this, "socket");
        __publicField(this, "connectionCount");
        __publicField(this, "_hasEverConnected", false);
        __publicField(this, "lastCloseReason");
        /** Upon HTTPS/WSS failure, the first jittered backoff duration, in ms. */ __publicField(this, "defaultInitialBackoff");
        /** We backoff exponentially, but we need to cap that--this is the jittered max. */ __publicField(this, "maxBackoff");
        /** How many times have we failed consecutively? */ __publicField(this, "retries");
        /** How long before lack of server response causes us to initiate a reconnect,
     * in ms */ __publicField(this, "serverInactivityThreshold");
        __publicField(this, "reconnectDueToServerInactivityTimeout");
        __publicField(this, "uri");
        __publicField(this, "onOpen");
        __publicField(this, "onResume");
        __publicField(this, "onMessage");
        __publicField(this, "webSocketConstructor");
        __publicField(this, "logger");
        __publicField(this, "onServerDisconnectError");
        this.webSocketConstructor = webSocketConstructor;
        this.socket = {
            state: "disconnected"
        };
        this.connectionCount = 0;
        this.lastCloseReason = "InitialConnect";
        this.defaultInitialBackoff = 1e3;
        this.maxBackoff = 16e3;
        this.retries = 0;
        this.serverInactivityThreshold = 6e4;
        this.reconnectDueToServerInactivityTimeout = null;
        this.uri = uri;
        this.onOpen = callbacks.onOpen;
        this.onResume = callbacks.onResume;
        this.onMessage = callbacks.onMessage;
        this.onServerDisconnectError = callbacks.onServerDisconnectError;
        this.logger = logger;
        this.connect();
    }
} //# sourceMappingURL=web_socket_manager.js.map
}),
"[project]/node_modules/convex/dist/esm/browser/sync/session.js [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "newSessionId",
    ()=>newSessionId
]);
"use strict";
function newSessionId() {
    return uuidv4();
}
function uuidv4() {
    return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, (c)=>{
        const r = Math.random() * 16 | 0, v = c === "x" ? r : r & 3 | 8;
        return v.toString(16);
    });
} //# sourceMappingURL=session.js.map
}),
"[project]/node_modules/jwt-decode/build/esm/index.js [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "InvalidTokenError",
    ()=>InvalidTokenError,
    "jwtDecode",
    ()=>jwtDecode
]);
class InvalidTokenError extends Error {
}
InvalidTokenError.prototype.name = "InvalidTokenError";
function b64DecodeUnicode(str) {
    return decodeURIComponent(atob(str).replace(/(.)/g, (m, p)=>{
        let code = p.charCodeAt(0).toString(16).toUpperCase();
        if (code.length < 2) {
            code = "0" + code;
        }
        return "%" + code;
    }));
}
function base64UrlDecode(str) {
    let output = str.replace(/-/g, "+").replace(/_/g, "/");
    switch(output.length % 4){
        case 0:
            break;
        case 2:
            output += "==";
            break;
        case 3:
            output += "=";
            break;
        default:
            throw new Error("base64 string is not of the correct length");
    }
    try {
        return b64DecodeUnicode(output);
    } catch (err) {
        return atob(output);
    }
}
function jwtDecode(token, options) {
    if (typeof token !== "string") {
        throw new InvalidTokenError("Invalid token specified: must be a string");
    }
    options || (options = {});
    const pos = options.header === true ? 0 : 1;
    const part = token.split(".")[pos];
    if (typeof part !== "string") {
        throw new InvalidTokenError("Invalid token specified: missing part #".concat(pos + 1));
    }
    let decoded;
    try {
        decoded = base64UrlDecode(part);
    } catch (e) {
        throw new InvalidTokenError("Invalid token specified: invalid base64 for part #".concat(pos + 1, " (").concat(e.message, ")"));
    }
    try {
        return JSON.parse(decoded);
    } catch (e) {
        throw new InvalidTokenError("Invalid token specified: invalid json for part #".concat(pos + 1, " (").concat(e.message, ")"));
    }
}
}),
"[project]/node_modules/convex/dist/esm/browser/sync/authentication_manager.js [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "AuthenticationManager",
    ()=>AuthenticationManager
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jwt$2d$decode$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/jwt-decode/build/esm/index.js [app-client] (ecmascript)");
"use strict";
var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value)=>key in obj ? __defProp(obj, key, {
        enumerable: true,
        configurable: true,
        writable: true,
        value
    }) : obj[key] = value;
var __publicField = (obj, key, value)=>__defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
;
const MAXIMUM_REFRESH_DELAY = 20 * 24 * 60 * 60 * 1e3;
const MAX_TOKEN_CONFIRMATION_ATTEMPTS = 2;
class AuthenticationManager {
    async setConfig(fetchToken, onChange) {
        this.resetAuthState();
        this._logVerbose("pausing WS for auth token fetch");
        this.pauseSocket();
        const token = await this.fetchTokenAndGuardAgainstRace(fetchToken, {
            forceRefreshToken: false
        });
        if (token.isFromOutdatedConfig) {
            return;
        }
        if (token.value) {
            this.setAuthState({
                state: "waitingForServerConfirmationOfCachedToken",
                config: {
                    fetchToken,
                    onAuthChange: onChange
                },
                hasRetried: false
            });
            this.authenticate(token.value);
        } else {
            this.setAuthState({
                state: "initialRefetch",
                config: {
                    fetchToken,
                    onAuthChange: onChange
                }
            });
            await this.refetchToken();
        }
        this._logVerbose("resuming WS after auth token fetch");
        this.resumeSocket();
    }
    onTransition(serverMessage) {
        if (!this.syncState.isCurrentOrNewerAuthVersion(serverMessage.endVersion.identity)) {
            return;
        }
        if (serverMessage.endVersion.identity <= serverMessage.startVersion.identity) {
            return;
        }
        if (this.authState.state === "waitingForServerConfirmationOfCachedToken") {
            this._logVerbose("server confirmed auth token is valid");
            void this.refetchToken();
            this.authState.config.onAuthChange(true);
            return;
        }
        if (this.authState.state === "waitingForServerConfirmationOfFreshToken") {
            this._logVerbose("server confirmed new auth token is valid");
            this.scheduleTokenRefetch(this.authState.token);
            this.tokenConfirmationAttempts = 0;
            if (!this.authState.hadAuth) {
                this.authState.config.onAuthChange(true);
            }
        }
    }
    onAuthError(serverMessage) {
        if (serverMessage.authUpdateAttempted === false && (this.authState.state === "waitingForServerConfirmationOfFreshToken" || this.authState.state === "waitingForServerConfirmationOfCachedToken")) {
            this._logVerbose("ignoring non-auth token expired error");
            return;
        }
        const { baseVersion } = serverMessage;
        if (!this.syncState.isCurrentOrNewerAuthVersion(baseVersion + 1)) {
            this._logVerbose("ignoring auth error for previous auth attempt");
            return;
        }
        void this.tryToReauthenticate(serverMessage);
        return;
    }
    // This is similar to `refetchToken` defined below, in fact we
    // don't represent them as different states, but it is different
    // in that we pause the WebSocket so that mutations
    // don't retry with bad auth.
    async tryToReauthenticate(serverMessage) {
        this._logVerbose("attempting to reauthenticate: ".concat(serverMessage.error));
        if (// No way to fetch another token, kaboom
        this.authState.state === "noAuth" || // We failed on a fresh token. After a small number of retries, we give up
        // and clear the auth state to avoid infinite retries.
        this.authState.state === "waitingForServerConfirmationOfFreshToken" && this.tokenConfirmationAttempts >= MAX_TOKEN_CONFIRMATION_ATTEMPTS) {
            this.logger.error('Failed to authenticate: "'.concat(serverMessage.error, '", check your server auth config'));
            if (this.syncState.hasAuth()) {
                this.syncState.clearAuth();
            }
            if (this.authState.state !== "noAuth") {
                this.setAndReportAuthFailed(this.authState.config.onAuthChange);
            }
            return;
        }
        if (this.authState.state === "waitingForServerConfirmationOfFreshToken") {
            this.tokenConfirmationAttempts++;
            this._logVerbose("retrying reauthentication, ".concat(MAX_TOKEN_CONFIRMATION_ATTEMPTS - this.tokenConfirmationAttempts, " attempts remaining"));
        }
        await this.stopSocket();
        const token = await this.fetchTokenAndGuardAgainstRace(this.authState.config.fetchToken, {
            forceRefreshToken: true
        });
        if (token.isFromOutdatedConfig) {
            return;
        }
        if (token.value && this.syncState.isNewAuth(token.value)) {
            this.authenticate(token.value);
            this.setAuthState({
                state: "waitingForServerConfirmationOfFreshToken",
                config: this.authState.config,
                token: token.value,
                hadAuth: this.authState.state === "notRefetching" || this.authState.state === "waitingForScheduledRefetch"
            });
        } else {
            this._logVerbose("reauthentication failed, could not fetch a new token");
            if (this.syncState.hasAuth()) {
                this.syncState.clearAuth();
            }
            this.setAndReportAuthFailed(this.authState.config.onAuthChange);
        }
        this.tryRestartSocket();
    }
    // Force refetch the token and schedule another refetch
    // before the token expires - an active client should never
    // need to reauthenticate.
    async refetchToken() {
        if (this.authState.state === "noAuth") {
            return;
        }
        this._logVerbose("refetching auth token");
        const token = await this.fetchTokenAndGuardAgainstRace(this.authState.config.fetchToken, {
            forceRefreshToken: true
        });
        if (token.isFromOutdatedConfig) {
            return;
        }
        if (token.value) {
            if (this.syncState.isNewAuth(token.value)) {
                this.setAuthState({
                    state: "waitingForServerConfirmationOfFreshToken",
                    hadAuth: this.syncState.hasAuth(),
                    token: token.value,
                    config: this.authState.config
                });
                this.authenticate(token.value);
            } else {
                this.setAuthState({
                    state: "notRefetching",
                    config: this.authState.config
                });
            }
        } else {
            this._logVerbose("refetching token failed");
            if (this.syncState.hasAuth()) {
                this.clearAuth();
            }
            this.setAndReportAuthFailed(this.authState.config.onAuthChange);
        }
        this._logVerbose("restarting WS after auth token fetch (if currently stopped)");
        this.tryRestartSocket();
    }
    scheduleTokenRefetch(token) {
        if (this.authState.state === "noAuth") {
            return;
        }
        const decodedToken = this.decodeToken(token);
        if (!decodedToken) {
            this.logger.error("Auth token is not a valid JWT, cannot refetch the token");
            return;
        }
        const { iat, exp } = decodedToken;
        if (!iat || !exp) {
            this.logger.error("Auth token does not have required fields, cannot refetch the token");
            return;
        }
        const tokenValiditySeconds = exp - iat;
        if (tokenValiditySeconds <= 2) {
            this.logger.error("Auth token does not live long enough, cannot refetch the token");
            return;
        }
        let delay = Math.min(MAXIMUM_REFRESH_DELAY, (tokenValiditySeconds - this.refreshTokenLeewaySeconds) * 1e3);
        if (delay <= 0) {
            this.logger.warn("Refetching auth token immediately, configured leeway ".concat(this.refreshTokenLeewaySeconds, "s is larger than the token's lifetime ").concat(tokenValiditySeconds, "s"));
            delay = 0;
        }
        const refetchTokenTimeoutId = setTimeout(()=>{
            this._logVerbose("running scheduled token refetch");
            void this.refetchToken();
        }, delay);
        this.setAuthState({
            state: "waitingForScheduledRefetch",
            refetchTokenTimeoutId,
            config: this.authState.config
        });
        this._logVerbose("scheduled preemptive auth token refetching in ".concat(delay, "ms"));
    }
    // Protects against simultaneous calls to `setConfig`
    // while we're fetching a token
    async fetchTokenAndGuardAgainstRace(fetchToken, fetchArgs) {
        const originalConfigVersion = ++this.configVersion;
        this._logVerbose("fetching token with config version ".concat(originalConfigVersion));
        const token = await fetchToken(fetchArgs);
        if (this.configVersion !== originalConfigVersion) {
            this._logVerbose("stale config version, expected ".concat(originalConfigVersion, ", got ").concat(this.configVersion));
            return {
                isFromOutdatedConfig: true
            };
        }
        return {
            isFromOutdatedConfig: false,
            value: token
        };
    }
    stop() {
        this.resetAuthState();
        this.configVersion++;
        this._logVerbose("config version bumped to ".concat(this.configVersion));
    }
    setAndReportAuthFailed(onAuthChange) {
        onAuthChange(false);
        this.resetAuthState();
    }
    resetAuthState() {
        this.setAuthState({
            state: "noAuth"
        });
    }
    setAuthState(newAuth) {
        const authStateForLog = newAuth.state === "waitingForServerConfirmationOfFreshToken" ? {
            hadAuth: newAuth.hadAuth,
            state: newAuth.state,
            token: "...".concat(newAuth.token.slice(-7))
        } : {
            state: newAuth.state
        };
        this._logVerbose("setting auth state to ".concat(JSON.stringify(authStateForLog)));
        switch(newAuth.state){
            case "waitingForScheduledRefetch":
            case "notRefetching":
            case "noAuth":
                this.tokenConfirmationAttempts = 0;
                break;
            case "waitingForServerConfirmationOfFreshToken":
            case "waitingForServerConfirmationOfCachedToken":
            case "initialRefetch":
                break;
            default:
                {
                    newAuth;
                }
        }
        if (this.authState.state === "waitingForScheduledRefetch") {
            clearTimeout(this.authState.refetchTokenTimeoutId);
            this.syncState.markAuthCompletion();
        }
        this.authState = newAuth;
    }
    decodeToken(token) {
        try {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jwt$2d$decode$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jwtDecode"])(token);
        } catch (e) {
            this._logVerbose("Error decoding token: ".concat(e instanceof Error ? e.message : "Unknown error"));
            return null;
        }
    }
    _logVerbose(message) {
        this.logger.logVerbose("".concat(message, " [v").concat(this.configVersion, "]"));
    }
    constructor(syncState, callbacks, config){
        __publicField(this, "authState", {
            state: "noAuth"
        });
        // Used to detect races involving `setConfig` calls
        // while a token is being fetched.
        __publicField(this, "configVersion", 0);
        // Shared by the BaseClient so that the auth manager can easily inspect it
        __publicField(this, "syncState");
        // Passed down by BaseClient, sends a message to the server
        __publicField(this, "authenticate");
        __publicField(this, "stopSocket");
        __publicField(this, "tryRestartSocket");
        __publicField(this, "pauseSocket");
        __publicField(this, "resumeSocket");
        // Passed down by BaseClient, sends a message to the server
        __publicField(this, "clearAuth");
        __publicField(this, "logger");
        __publicField(this, "refreshTokenLeewaySeconds");
        // Number of times we have attempted to confirm the latest token. We retry up
        // to `MAX_TOKEN_CONFIRMATION_ATTEMPTS` times.
        __publicField(this, "tokenConfirmationAttempts", 0);
        this.syncState = syncState;
        this.authenticate = callbacks.authenticate;
        this.stopSocket = callbacks.stopSocket;
        this.tryRestartSocket = callbacks.tryRestartSocket;
        this.pauseSocket = callbacks.pauseSocket;
        this.resumeSocket = callbacks.resumeSocket;
        this.clearAuth = callbacks.clearAuth;
        this.logger = config.logger;
        this.refreshTokenLeewaySeconds = config.refreshTokenLeewaySeconds;
    }
} //# sourceMappingURL=authentication_manager.js.map
}),
"[project]/node_modules/convex/dist/esm/browser/sync/metrics.js [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "getMarksReport",
    ()=>getMarksReport,
    "mark",
    ()=>mark
]);
"use strict";
const markNames = [
    "convexClientConstructed",
    "convexWebSocketOpen",
    "convexFirstMessageReceived"
];
function mark(name, sessionId) {
    const detail = {
        sessionId
    };
    if (typeof performance === "undefined" || !performance.mark) return;
    performance.mark(name, {
        detail
    });
}
function performanceMarkToJson(mark2) {
    let name = mark2.name.slice("convex".length);
    name = name.charAt(0).toLowerCase() + name.slice(1);
    return {
        name,
        startTime: mark2.startTime
    };
}
function getMarksReport(sessionId) {
    if (typeof performance === "undefined" || !performance.getEntriesByName) {
        return [];
    }
    const allMarks = [];
    for (const name of markNames){
        const marks = performance.getEntriesByName(name).filter((entry)=>entry.entryType === "mark").filter((mark2)=>mark2.detail.sessionId === sessionId);
        allMarks.push(...marks);
    }
    return allMarks.map(performanceMarkToJson);
} //# sourceMappingURL=metrics.js.map
}),
"[project]/node_modules/convex/dist/esm/browser/sync/client.js [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "BaseConvexClient",
    ()=>BaseConvexClient
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/values/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$value$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/values/value.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$logging$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/browser/logging.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$local_state$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/browser/sync/local_state.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$request_manager$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/browser/sync/request_manager.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$optimistic_updates_impl$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/browser/sync/optimistic_updates_impl.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$remote_query_set$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/browser/sync/remote_query_set.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$udf_path_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/browser/sync/udf_path_utils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$web_socket_manager$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/browser/sync/web_socket_manager.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$session$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/browser/sync/session.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$authentication_manager$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/browser/sync/authentication_manager.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$metrics$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/browser/sync/metrics.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$common$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/common/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$errors$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/values/errors.js [app-client] (ecmascript)");
"use strict";
var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value)=>key in obj ? __defProp(obj, key, {
        enumerable: true,
        configurable: true,
        writable: true,
        value
    }) : obj[key] = value;
var __publicField = (obj, key, value)=>__defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
;
;
;
;
;
;
;
;
;
;
;
;
;
;
class BaseConvexClient {
    /**
   * Return true if there is outstanding work from prior to the time of the most recent restart.
   * This indicates that the client has not proven itself to have gotten past the issue that
   * potentially led to the restart. Use this to influence when to reset backoff after a failure.
   */ hasSyncedPastLastReconnect() {
        const hasSyncedPastLastReconnect = this.requestManager.hasSyncedPastLastReconnect() || this.state.hasSyncedPastLastReconnect();
        return hasSyncedPastLastReconnect;
    }
    observedTimestamp(observedTs) {
        if (this.maxObservedTimestamp === void 0 || this.maxObservedTimestamp.lessThanOrEqual(observedTs)) {
            this.maxObservedTimestamp = observedTs;
        }
    }
    getMaxObservedTimestamp() {
        return this.maxObservedTimestamp;
    }
    /**
   * Compute the current query results based on the remoteQuerySet and the
   * current optimistic updates and call `onTransition` for all the changed
   * queries.
   *
   * @param completedMutations - A set of mutation IDs whose optimistic updates
   * are no longer needed.
   */ notifyOnQueryResultChanges(completedRequests) {
        const remoteQueryResults = this.remoteQuerySet.remoteQueryResults();
        const queryTokenToValue = /* @__PURE__ */ new Map();
        for (const [queryId, result] of remoteQueryResults){
            const queryToken = this.state.queryToken(queryId);
            if (queryToken !== null) {
                const query = {
                    result,
                    udfPath: this.state.queryPath(queryId),
                    args: this.state.queryArgs(queryId)
                };
                queryTokenToValue.set(queryToken, query);
            }
        }
        const changedQueryTokens = this.optimisticQueryResults.ingestQueryResultsFromServer(queryTokenToValue, new Set(completedRequests.keys()));
        this.handleTransition({
            queries: changedQueryTokens.map((token)=>{
                const optimisticResult = this.optimisticQueryResults.rawQueryResult(token);
                return {
                    token,
                    modification: {
                        kind: "Updated",
                        result: optimisticResult.result
                    }
                };
            }),
            reflectedMutations: Array.from(completedRequests).map((param)=>{
                let [requestId, result] = param;
                return {
                    requestId,
                    result
                };
            }),
            timestamp: this.remoteQuerySet.timestamp()
        });
    }
    handleTransition(transition) {
        for (const fn of this._onTransitionFns.values()){
            fn(transition);
        }
    }
    /**
   * Add a handler that will be called on a transition.
   *
   * Any external side effects (e.g. setting React state) should be handled here.
   *
   * @param fn
   *
   * @returns
   */ addOnTransitionHandler(fn) {
        const id = this._transitionHandlerCounter++;
        this._onTransitionFns.set(id, fn);
        return ()=>this._onTransitionFns.delete(id);
    }
    /**
   * Set the authentication token to be used for subsequent queries and mutations.
   * `fetchToken` will be called automatically again if a token expires.
   * `fetchToken` should return `null` if the token cannot be retrieved, for example
   * when the user's rights were permanently revoked.
   * @param fetchToken - an async function returning the JWT-encoded OpenID Connect Identity Token
   * @param onChange - a callback that will be called when the authentication status changes
   */ setAuth(fetchToken, onChange) {
        void this.authenticationManager.setConfig(fetchToken, onChange);
    }
    hasAuth() {
        return this.state.hasAuth();
    }
    /** @internal */ setAdminAuth(value, fakeUserIdentity) {
        const message = this.state.setAdminAuth(value, fakeUserIdentity);
        this.webSocketManager.sendMessage(message);
    }
    clearAuth() {
        const message = this.state.clearAuth();
        this.webSocketManager.sendMessage(message);
    }
    /**
     * Subscribe to a query function.
     *
     * Whenever this query's result changes, the `onTransition` callback
     * passed into the constructor will be called.
     *
     * @param name - The name of the query.
     * @param args - An arguments object for the query. If this is omitted, the
     * arguments will be `{}`.
     * @param options - A {@link SubscribeOptions} options object for this query.
  
     * @returns An object containing a {@link QueryToken} corresponding to this
     * query and an `unsubscribe` callback.
     */ subscribe(name, args, options) {
        const argsObject = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$common$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseArgs"])(args);
        const { modification, queryToken, unsubscribe } = this.state.subscribe(name, argsObject, options === null || options === void 0 ? void 0 : options.journal, options === null || options === void 0 ? void 0 : options.componentPath);
        if (modification !== null) {
            this.webSocketManager.sendMessage(modification);
        }
        return {
            queryToken,
            unsubscribe: ()=>{
                const modification2 = unsubscribe();
                if (modification2) {
                    this.webSocketManager.sendMessage(modification2);
                }
            }
        };
    }
    /**
   * A query result based only on the current, local state.
   *
   * The only way this will return a value is if we're already subscribed to the
   * query or its value has been set optimistically.
   */ localQueryResult(udfPath, args) {
        const argsObject = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$common$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseArgs"])(args);
        const queryToken = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$udf_path_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["serializePathAndArgs"])(udfPath, argsObject);
        return this.optimisticQueryResults.queryResult(queryToken);
    }
    /**
   * Get query result by query token based on current, local state
   *
   * The only way this will return a value is if we're already subscribed to the
   * query or its value has been set optimistically.
   *
   * @internal
   */ localQueryResultByToken(queryToken) {
        return this.optimisticQueryResults.queryResult(queryToken);
    }
    /**
   * Whether local query result is available for a toke.
   *
   * This method does not throw if the result is an error.
   *
   * @internal
   */ hasLocalQueryResultByToken(queryToken) {
        return this.optimisticQueryResults.hasQueryResult(queryToken);
    }
    /**
   * @internal
   */ localQueryLogs(udfPath, args) {
        const argsObject = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$common$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseArgs"])(args);
        const queryToken = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$udf_path_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["serializePathAndArgs"])(udfPath, argsObject);
        return this.optimisticQueryResults.queryLogs(queryToken);
    }
    /**
   * Retrieve the current {@link QueryJournal} for this query function.
   *
   * If we have not yet received a result for this query, this will be `undefined`.
   *
   * @param name - The name of the query.
   * @param args - The arguments object for this query.
   * @returns The query's {@link QueryJournal} or `undefined`.
   */ queryJournal(name, args) {
        const argsObject = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$common$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseArgs"])(args);
        const queryToken = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$udf_path_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["serializePathAndArgs"])(name, argsObject);
        return this.state.queryJournal(queryToken);
    }
    /**
   * Get the current {@link ConnectionState} between the client and the Convex
   * backend.
   *
   * @returns The {@link ConnectionState} with the Convex backend.
   */ connectionState() {
        const wsConnectionState = this.webSocketManager.connectionState();
        return {
            hasInflightRequests: this.requestManager.hasInflightRequests(),
            isWebSocketConnected: wsConnectionState.isConnected,
            hasEverConnected: wsConnectionState.hasEverConnected,
            connectionCount: wsConnectionState.connectionCount,
            connectionRetries: wsConnectionState.connectionRetries,
            timeOfOldestInflightRequest: this.requestManager.timeOfOldestInflightRequest(),
            inflightMutations: this.requestManager.inflightMutations(),
            inflightActions: this.requestManager.inflightActions()
        };
    }
    /**
   * Subscribe to the {@link ConnectionState} between the client and the Convex
   * backend, calling a callback each time it changes.
   *
   * Subscribed callbacks will be called when any part of ConnectionState changes.
   * ConnectionState may grow in future versions (e.g. to provide a array of
   * inflight requests) in which case callbacks would be called more frequently.
   *
   * @returns An unsubscribe function to stop listening.
   */ subscribeToConnectionState(cb) {
        const id = this.nextConnectionStateSubscriberId++;
        this.connectionStateSubscribers.set(id, cb);
        return ()=>{
            this.connectionStateSubscribers.delete(id);
        };
    }
    /**
     * Execute a mutation function.
     *
     * @param name - The name of the mutation.
     * @param args - An arguments object for the mutation. If this is omitted,
     * the arguments will be `{}`.
     * @param options - A {@link MutationOptions} options object for this mutation.
  
     * @returns - A promise of the mutation's result.
     */ async mutation(name, args, options) {
        const result = await this.mutationInternal(name, args, options);
        if (!result.success) {
            if (result.errorData !== void 0) {
                throw (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$logging$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardData"])(result, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$errors$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ConvexError"]((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$logging$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createHybridErrorStacktrace"])("mutation", name, result)));
            }
            throw new Error((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$logging$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createHybridErrorStacktrace"])("mutation", name, result));
        }
        return result.value;
    }
    /**
   * @internal
   */ async mutationInternal(udfPath, args, options, componentPath) {
        const { mutationPromise } = this.enqueueMutation(udfPath, args, options, componentPath);
        return mutationPromise;
    }
    /**
   * @internal
   */ enqueueMutation(udfPath, args, options, componentPath) {
        const mutationArgs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$common$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseArgs"])(args);
        this.tryReportLongDisconnect();
        const requestId = this.nextRequestId;
        this._nextRequestId++;
        if (options !== void 0) {
            const optimisticUpdate = options.optimisticUpdate;
            if (optimisticUpdate !== void 0) {
                const wrappedUpdate = (localQueryStore)=>{
                    const result = optimisticUpdate(localQueryStore, mutationArgs);
                    if (result instanceof Promise) {
                        this.logger.warn("Optimistic update handler returned a Promise. Optimistic updates should be synchronous.");
                    }
                };
                const changedQueryTokens = this.optimisticQueryResults.applyOptimisticUpdate(wrappedUpdate, requestId);
                const changedQueries = changedQueryTokens.map((token)=>{
                    const localResult = this.localQueryResultByToken(token);
                    return {
                        token,
                        modification: {
                            kind: "Updated",
                            result: localResult === void 0 ? void 0 : {
                                success: true,
                                value: localResult,
                                logLines: []
                            }
                        }
                    };
                });
                this.handleTransition({
                    queries: changedQueries,
                    reflectedMutations: [],
                    timestamp: this.remoteQuerySet.timestamp()
                });
            }
        }
        const message = {
            type: "Mutation",
            requestId,
            udfPath,
            componentPath,
            args: [
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$value$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["convexToJson"])(mutationArgs)
            ]
        };
        const mightBeSent = this.webSocketManager.sendMessage(message);
        const mutationPromise = this.requestManager.request(message, mightBeSent);
        return {
            requestId,
            mutationPromise
        };
    }
    /**
   * Execute an action function.
   *
   * @param name - The name of the action.
   * @param args - An arguments object for the action. If this is omitted,
   * the arguments will be `{}`.
   * @returns A promise of the action's result.
   */ async action(name, args) {
        const result = await this.actionInternal(name, args);
        if (!result.success) {
            if (result.errorData !== void 0) {
                throw (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$logging$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardData"])(result, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$errors$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ConvexError"]((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$logging$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createHybridErrorStacktrace"])("action", name, result)));
            }
            throw new Error((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$logging$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createHybridErrorStacktrace"])("action", name, result));
        }
        return result.value;
    }
    /**
   * @internal
   */ async actionInternal(udfPath, args, componentPath) {
        const actionArgs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$common$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseArgs"])(args);
        const requestId = this.nextRequestId;
        this._nextRequestId++;
        this.tryReportLongDisconnect();
        const message = {
            type: "Action",
            requestId,
            udfPath,
            componentPath,
            args: [
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$value$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["convexToJson"])(actionArgs)
            ]
        };
        const mightBeSent = this.webSocketManager.sendMessage(message);
        return this.requestManager.request(message, mightBeSent);
    }
    /**
   * Close any network handles associated with this client and stop all subscriptions.
   *
   * Call this method when you're done with an {@link BaseConvexClient} to
   * dispose of its sockets and resources.
   *
   * @returns A `Promise` fulfilled when the connection has been completely closed.
   */ async close() {
        this.authenticationManager.stop();
        return this.webSocketManager.terminate();
    }
    /**
   * Return the address for this client, useful for creating a new client.
   *
   * Not guaranteed to match the address with which this client was constructed:
   * it may be canonicalized.
   */ get url() {
        return this.address;
    }
    /**
   * @internal
   */ get nextRequestId() {
        return this._nextRequestId;
    }
    /**
   * @internal
   */ get sessionId() {
        return this._sessionId;
    }
    /**
   * Reports performance marks to the server. This should only be called when
   * we have a functional websocket.
   */ reportMarks() {
        if (this.debug) {
            const report = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$metrics$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getMarksReport"])(this.sessionId);
            this.webSocketManager.sendMessage({
                type: "Event",
                eventType: "ClientConnect",
                event: report
            });
        }
    }
    tryReportLongDisconnect() {
        if (!this.debug) {
            return;
        }
        const timeOfOldestRequest = this.connectionState().timeOfOldestInflightRequest;
        if (timeOfOldestRequest === null || Date.now() - timeOfOldestRequest.getTime() <= 60 * 1e3) {
            return;
        }
        const endpoint = "".concat(this.address, "/api/debug_event");
        fetch(endpoint, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                "Convex-Client": "npm-".concat(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["version"])
            },
            body: JSON.stringify({
                event: "LongWebsocketDisconnect"
            })
        }).then((response)=>{
            if (!response.ok) {
                this.logger.warn("Analytics request failed with response:", response.body);
            }
        }).catch((error)=>{
            this.logger.warn("Analytics response failed with error:", error);
        });
    }
    /**
   * @param address - The url of your Convex deployment, often provided
   * by an environment variable. E.g. `https://small-mouse-123.convex.cloud`.
   * @param onTransition - A callback receiving an array of query tokens
   * corresponding to query results that have changed -- additional handlers
   * can be added via `addOnTransitionHandler`.
   * @param options - See {@link BaseConvexClientOptions} for a full description.
   */ constructor(address, onTransition, options){
        __publicField(this, "address");
        __publicField(this, "state");
        __publicField(this, "requestManager");
        __publicField(this, "webSocketManager");
        __publicField(this, "authenticationManager");
        __publicField(this, "remoteQuerySet");
        __publicField(this, "optimisticQueryResults");
        __publicField(this, "_transitionHandlerCounter", 0);
        __publicField(this, "_nextRequestId");
        __publicField(this, "_onTransitionFns", /* @__PURE__ */ new Map());
        __publicField(this, "_sessionId");
        __publicField(this, "firstMessageReceived", false);
        __publicField(this, "debug");
        __publicField(this, "logger");
        __publicField(this, "maxObservedTimestamp");
        __publicField(this, "connectionStateSubscribers", /* @__PURE__ */ new Map());
        __publicField(this, "nextConnectionStateSubscriberId", 0);
        __publicField(this, "_lastPublishedConnectionState");
        /**
     * Call this whenever the connection state may have changed in a way that could
     * require publishing it. Schedules a possibly update.
     */ __publicField(this, "markConnectionStateDirty", ()=>{
            void Promise.resolve().then(()=>{
                const curConnectionState = this.connectionState();
                if (JSON.stringify(curConnectionState) !== JSON.stringify(this._lastPublishedConnectionState)) {
                    this._lastPublishedConnectionState = curConnectionState;
                    for (const cb of this.connectionStateSubscribers.values()){
                        cb(curConnectionState);
                    }
                }
            });
        });
        // Instance property so that `mark()` doesn't need to be called as a method.
        __publicField(this, "mark", (name)=>{
            if (this.debug) {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$metrics$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mark"])(name, this.sessionId);
            }
        });
        if (typeof address === "object") {
            throw new Error("Passing a ClientConfig object is no longer supported. Pass the URL of the Convex deployment as a string directly.");
        }
        if ((options === null || options === void 0 ? void 0 : options.skipConvexDeploymentUrlCheck) !== true) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$common$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["validateDeploymentUrl"])(address);
        }
        options = {
            ...options
        };
        var _options_authRefreshTokenLeewaySeconds;
        const authRefreshTokenLeewaySeconds = (_options_authRefreshTokenLeewaySeconds = options.authRefreshTokenLeewaySeconds) !== null && _options_authRefreshTokenLeewaySeconds !== void 0 ? _options_authRefreshTokenLeewaySeconds : 2;
        let webSocketConstructor = options.webSocketConstructor;
        if (!webSocketConstructor && typeof WebSocket === "undefined") {
            throw new Error("No WebSocket global variable defined! To use Convex in an environment without WebSocket try the HTTP client: https://docs.convex.dev/api/classes/browser.ConvexHttpClient");
        }
        webSocketConstructor = webSocketConstructor || WebSocket;
        var _options_reportDebugInfoToConvex;
        this.debug = (_options_reportDebugInfoToConvex = options.reportDebugInfoToConvex) !== null && _options_reportDebugInfoToConvex !== void 0 ? _options_reportDebugInfoToConvex : false;
        this.address = address;
        var _options_verbose, _options_verbose1;
        this.logger = options.logger === false ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$logging$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["instantiateNoopLogger"])({
            verbose: (_options_verbose = options.verbose) !== null && _options_verbose !== void 0 ? _options_verbose : false
        }) : options.logger !== true && options.logger ? options.logger : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$logging$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["instantiateDefaultLogger"])({
            verbose: (_options_verbose1 = options.verbose) !== null && _options_verbose1 !== void 0 ? _options_verbose1 : false
        });
        const i = address.search("://");
        if (i === -1) {
            throw new Error("Provided address was not an absolute URL.");
        }
        const origin = address.substring(i + 3);
        const protocol = address.substring(0, i);
        let wsProtocol;
        if (protocol === "http") {
            wsProtocol = "ws";
        } else if (protocol === "https") {
            wsProtocol = "wss";
        } else {
            throw new Error("Unknown parent protocol ".concat(protocol));
        }
        const wsUri = "".concat(wsProtocol, "://").concat(origin, "/api/").concat(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["version"], "/sync");
        this.state = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$local_state$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LocalSyncState"]();
        this.remoteQuerySet = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$remote_query_set$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RemoteQuerySet"]((queryId)=>this.state.queryPath(queryId), this.logger);
        this.requestManager = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$request_manager$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestManager"](this.logger, this.markConnectionStateDirty);
        const pauseSocket = ()=>{
            this.webSocketManager.pause();
            this.state.pause();
        };
        this.authenticationManager = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$authentication_manager$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AuthenticationManager"](this.state, {
            authenticate: (token)=>{
                const message = this.state.setAuth(token);
                this.webSocketManager.sendMessage(message);
                return message.baseVersion;
            },
            stopSocket: ()=>this.webSocketManager.stop(),
            tryRestartSocket: ()=>this.webSocketManager.tryRestart(),
            pauseSocket,
            resumeSocket: ()=>this.webSocketManager.resume(),
            clearAuth: ()=>{
                this.clearAuth();
            }
        }, {
            logger: this.logger,
            refreshTokenLeewaySeconds: authRefreshTokenLeewaySeconds
        });
        this.optimisticQueryResults = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$optimistic_updates_impl$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["OptimisticQueryResults"]();
        this.addOnTransitionHandler((transition)=>{
            onTransition(transition.queries.map((q)=>q.token));
        });
        this._nextRequestId = 0;
        this._sessionId = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$session$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["newSessionId"])();
        const { unsavedChangesWarning } = options;
        if (typeof window === "undefined" || typeof window.addEventListener === "undefined") {
            if (unsavedChangesWarning === true) {
                throw new Error("unsavedChangesWarning requested, but window.addEventListener not found! Remove {unsavedChangesWarning: true} from Convex client options.");
            }
        } else if (unsavedChangesWarning !== false) {
            window.addEventListener("beforeunload", (e)=>{
                if (this.requestManager.hasIncompleteRequests()) {
                    e.preventDefault();
                    const confirmationMessage = "Are you sure you want to leave? Your changes may not be saved.";
                    (e || window.event).returnValue = confirmationMessage;
                    return confirmationMessage;
                }
            });
        }
        this.webSocketManager = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$web_socket_manager$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["WebSocketManager"](wsUri, {
            onOpen: (reconnectMetadata)=>{
                this.mark("convexWebSocketOpen");
                this.webSocketManager.sendMessage({
                    ...reconnectMetadata,
                    type: "Connect",
                    sessionId: this._sessionId,
                    maxObservedTimestamp: this.maxObservedTimestamp
                });
                const oldRemoteQueryResults = new Set(this.remoteQuerySet.remoteQueryResults().keys());
                this.remoteQuerySet = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$remote_query_set$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RemoteQuerySet"]((queryId)=>this.state.queryPath(queryId), this.logger);
                const [querySetModification, authModification] = this.state.restart(oldRemoteQueryResults);
                if (authModification) {
                    this.webSocketManager.sendMessage(authModification);
                }
                this.webSocketManager.sendMessage(querySetModification);
                for (const message of this.requestManager.restart()){
                    this.webSocketManager.sendMessage(message);
                }
            },
            onResume: ()=>{
                const [querySetModification, authModification] = this.state.resume();
                if (authModification) {
                    this.webSocketManager.sendMessage(authModification);
                }
                if (querySetModification) {
                    this.webSocketManager.sendMessage(querySetModification);
                }
                for (const message of this.requestManager.resume()){
                    this.webSocketManager.sendMessage(message);
                }
            },
            onMessage: (serverMessage)=>{
                if (!this.firstMessageReceived) {
                    this.firstMessageReceived = true;
                    this.mark("convexFirstMessageReceived");
                    this.reportMarks();
                }
                switch(serverMessage.type){
                    case "Transition":
                        {
                            this.observedTimestamp(serverMessage.endVersion.ts);
                            this.authenticationManager.onTransition(serverMessage);
                            this.remoteQuerySet.transition(serverMessage);
                            this.state.transition(serverMessage);
                            const completedRequests = this.requestManager.removeCompleted(this.remoteQuerySet.timestamp());
                            this.notifyOnQueryResultChanges(completedRequests);
                            break;
                        }
                    case "MutationResponse":
                        {
                            if (serverMessage.success) {
                                this.observedTimestamp(serverMessage.ts);
                            }
                            const completedMutationInfo = this.requestManager.onResponse(serverMessage);
                            if (completedMutationInfo !== null) {
                                this.notifyOnQueryResultChanges(/* @__PURE__ */ new Map([
                                    [
                                        completedMutationInfo.requestId,
                                        completedMutationInfo.result
                                    ]
                                ]));
                            }
                            break;
                        }
                    case "ActionResponse":
                        {
                            this.requestManager.onResponse(serverMessage);
                            break;
                        }
                    case "AuthError":
                        {
                            this.authenticationManager.onAuthError(serverMessage);
                            break;
                        }
                    case "FatalError":
                        {
                            const error = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$logging$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logFatalError"])(this.logger, serverMessage.error);
                            void this.webSocketManager.terminate();
                            throw error;
                        }
                    case "Ping":
                        break;
                    // do nothing
                    default:
                        {
                            serverMessage;
                        }
                }
                return {
                    hasSyncedPastLastReconnect: this.hasSyncedPastLastReconnect()
                };
            },
            onServerDisconnectError: options.onServerDisconnectError
        }, webSocketConstructor, this.logger, this.markConnectionStateDirty);
        this.mark("convexClientConstructed");
        if (options.expectAuth) {
            pauseSocket();
        }
    }
} //# sourceMappingURL=client.js.map
}),
"[project]/node_modules/convex/dist/esm/browser/simple_client.js [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "ConvexClient",
    ()=>ConvexClient,
    "setDefaultWebSocketConstructor",
    ()=>setDefaultWebSocketConstructor
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$common$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/common/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/browser/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$client$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/browser/sync/client.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$server$2f$api$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/server/api.js [app-client] (ecmascript)");
"use strict";
var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value)=>key in obj ? __defProp(obj, key, {
        enumerable: true,
        configurable: true,
        writable: true,
        value
    }) : obj[key] = value;
var __publicField = (obj, key, value)=>__defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
;
;
;
let defaultWebSocketConstructor;
function setDefaultWebSocketConstructor(ws) {
    defaultWebSocketConstructor = ws;
}
class ConvexClient {
    /**
   * Once closed no registered callbacks will fire again.
   */ get closed() {
        return this._closed;
    }
    get client() {
        if (this._client) return this._client;
        throw new Error("ConvexClient is disabled");
    }
    get disabled() {
        return this._disabled;
    }
    /**
   * Call a callback whenever a new result for a query is received. The callback
   * will run soon after being registered if a result for the query is already
   * in memory.
   *
   * The return value is an {@link Unsubscribe} object which is both a function
   * an an object with properties. Both of the patterns below work with this object:
   *
   *```ts
   * // call the return value as a function
   * const unsubscribe = client.onUpdate(api.messages.list, {}, (messages) => {
   *   console.log(messages);
   * });
   * unsubscribe();
   *
   * // unpack the return value into its properties
   * const {
   *   getCurrentValue,
   *   unsubscribe,
   * } = client.onUpdate(api.messages.list, {}, (messages) => {
   *   console.log(messages);
   * });
   *```
   *
   * @param query - A {@link server.FunctionReference} for the public query to run.
   * @param args - The arguments to run the query with.
   * @param callback - Function to call when the query result updates.
   * @param onError - Function to call when the query result updates with an error.
   * If not provided, errors will be thrown instead of calling the callback.
   *
   * @return an {@link Unsubscribe} function to stop calling the onUpdate function.
   */ onUpdate(query, args, callback, onError) {
        if (this.disabled) {
            const disabledUnsubscribe = ()=>{};
            const unsubscribeProps2 = {
                unsubscribe: disabledUnsubscribe,
                getCurrentValue: ()=>void 0,
                getQueryLogs: ()=>void 0
            };
            Object.assign(disabledUnsubscribe, unsubscribeProps2);
            return disabledUnsubscribe;
        }
        const { queryToken, unsubscribe } = this.client.subscribe((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$server$2f$api$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getFunctionName"])(query), args);
        const queryInfo = {
            queryToken,
            callback,
            onError,
            unsubscribe,
            hasEverRun: false,
            query,
            args
        };
        this.listeners.add(queryInfo);
        if (this.queryResultReady(queryToken) && this.callNewListenersWithCurrentValuesTimer === void 0) {
            this.callNewListenersWithCurrentValuesTimer = setTimeout(()=>this.callNewListenersWithCurrentValues(), 0);
        }
        const unsubscribeProps = {
            unsubscribe: ()=>{
                if (this.closed) {
                    return;
                }
                this.listeners.delete(queryInfo);
                unsubscribe();
            },
            getCurrentValue: ()=>this.client.localQueryResultByToken(queryToken),
            getQueryLogs: ()=>this.client.localQueryLogs(queryToken)
        };
        const ret = unsubscribeProps.unsubscribe;
        Object.assign(ret, unsubscribeProps);
        return ret;
    }
    // Run all callbacks that have never been run before if they have a query
    // result available now.
    callNewListenersWithCurrentValues() {
        this.callNewListenersWithCurrentValuesTimer = void 0;
        this._transition([], true);
    }
    queryResultReady(queryToken) {
        return this.client.hasLocalQueryResultByToken(queryToken);
    }
    async close() {
        if (this.disabled) return;
        this.listeners.clear();
        this._closed = true;
        return this.client.close();
    }
    /**
   * Set the authentication token to be used for subsequent queries and mutations.
   * `fetchToken` will be called automatically again if a token expires.
   * `fetchToken` should return `null` if the token cannot be retrieved, for example
   * when the user's rights were permanently revoked.
   * @param fetchToken - an async function returning the JWT (typically an OpenID Connect Identity Token)
   * @param onChange - a callback that will be called when the authentication status changes
   */ setAuth(fetchToken, onChange) {
        if (this.disabled) return;
        this.client.setAuth(fetchToken, onChange !== null && onChange !== void 0 ? onChange : ()=>{});
    }
    /**
   * @internal
   */ setAdminAuth(token, identity) {
        if (this.closed) {
            throw new Error("ConvexClient has already been closed.");
        }
        if (this.disabled) return;
        this.client.setAdminAuth(token, identity);
    }
    /**
   * @internal
   */ _transition(updatedQueries) {
        let callNewListeners = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;
        for (const queryInfo of this.listeners){
            const { callback, queryToken, onError, hasEverRun } = queryInfo;
            if (updatedQueries.includes(queryToken) || callNewListeners && !hasEverRun && this.client.hasLocalQueryResultByToken(queryToken)) {
                queryInfo.hasEverRun = true;
                let newValue;
                try {
                    newValue = this.client.localQueryResultByToken(queryToken);
                } catch (error) {
                    if (!(error instanceof Error)) throw error;
                    if (onError) {
                        onError(error, "Second argument to onUpdate onError is reserved for later use");
                    } else {
                        void Promise.reject(error);
                    }
                    continue;
                }
                callback(newValue, "Second argument to onUpdate callback is reserved for later use");
            }
        }
    }
    /**
   * Execute a mutation function.
   *
   * @param mutation - A {@link server.FunctionReference} for the public mutation
   * to run.
   * @param args - An arguments object for the mutation.
   * @param options - A {@link MutationOptions} options object for the mutation.
   * @returns A promise of the mutation's result.
   */ async mutation(mutation, args, options) {
        if (this.disabled) throw new Error("ConvexClient is disabled");
        return await this.client.mutation((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$server$2f$api$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getFunctionName"])(mutation), args, options);
    }
    /**
   * Execute an action function.
   *
   * @param action - A {@link server.FunctionReference} for the public action
   * to run.
   * @param args - An arguments object for the action.
   * @returns A promise of the action's result.
   */ async action(action, args) {
        if (this.disabled) throw new Error("ConvexClient is disabled");
        return await this.client.action((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$server$2f$api$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getFunctionName"])(action), args);
    }
    /**
   * Fetch a query result once.
   *
   * @param query - A {@link server.FunctionReference} for the public query
   * to run.
   * @param args - An arguments object for the query.
   * @returns A promise of the query's result.
   */ async query(query, args) {
        if (this.disabled) throw new Error("ConvexClient is disabled");
        const value = this.client.localQueryResult((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$server$2f$api$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getFunctionName"])(query), args);
        if (value !== void 0) return Promise.resolve(value);
        return new Promise((resolve, reject)=>{
            const { unsubscribe } = this.onUpdate(query, args, (value2)=>{
                unsubscribe();
                resolve(value2);
            }, (e)=>{
                unsubscribe();
                reject(e);
            });
        });
    }
    /**
   * Get the current {@link ConnectionState} between the client and the Convex
   * backend.
   *
   * @returns The {@link ConnectionState} with the Convex backend.
   */ connectionState() {
        if (this.disabled) throw new Error("ConvexClient is disabled");
        return this.client.connectionState();
    }
    /**
   * Subscribe to the {@link ConnectionState} between the client and the Convex
   * backend, calling a callback each time it changes.
   *
   * Subscribed callbacks will be called when any part of ConnectionState changes.
   * ConnectionState may grow in future versions (e.g. to provide a array of
   * inflight requests) in which case callbacks would be called more frequently.
   *
   * @returns An unsubscribe function to stop listening.
   */ subscribeToConnectionState(cb) {
        if (this.disabled) return ()=>{};
        return this.client.subscribeToConnectionState(cb);
    }
    /**
   * Construct a client and immediately initiate a WebSocket connection to the passed address.
   *
   * @public
   */ constructor(address, options = {}){
        __publicField(this, "listeners");
        __publicField(this, "_client");
        // A synthetic server event to run callbacks the first time
        __publicField(this, "callNewListenersWithCurrentValuesTimer");
        __publicField(this, "_closed");
        __publicField(this, "_disabled");
        if (options.skipConvexDeploymentUrlCheck !== true) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$common$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["validateDeploymentUrl"])(address);
        }
        const { disabled, ...baseOptions } = options;
        this._closed = false;
        this._disabled = !!disabled;
        if (defaultWebSocketConstructor && !("webSocketConstructor" in baseOptions) && typeof WebSocket === "undefined") {
            baseOptions.webSocketConstructor = defaultWebSocketConstructor;
        }
        if (typeof window === "undefined" && !("unsavedChangesWarning" in baseOptions)) {
            baseOptions.unsavedChangesWarning = false;
        }
        if (!this.disabled) {
            this._client = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$client$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BaseConvexClient"](address, (updatedQueries)=>this._transition(updatedQueries), baseOptions);
        }
        this.listeners = /* @__PURE__ */ new Set();
    }
} //# sourceMappingURL=simple_client.js.map
}),
"[project]/node_modules/convex/dist/esm/browser/http_client.js [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "ConvexHttpClient",
    ()=>ConvexHttpClient,
    "STATUS_CODE_BAD_REQUEST",
    ()=>STATUS_CODE_BAD_REQUEST,
    "STATUS_CODE_OK",
    ()=>STATUS_CODE_OK,
    "STATUS_CODE_UDF_FAILED",
    ()=>STATUS_CODE_UDF_FAILED,
    "setFetch",
    ()=>setFetch
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$server$2f$api$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/server/api.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$common$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/common/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/values/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$errors$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/values/errors.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$value$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/values/value.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$logging$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/browser/logging.js [app-client] (ecmascript)");
"use strict";
var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value)=>key in obj ? __defProp(obj, key, {
        enumerable: true,
        configurable: true,
        writable: true,
        value
    }) : obj[key] = value;
var __publicField = (obj, key, value)=>__defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
;
;
;
;
;
const STATUS_CODE_OK = 200;
const STATUS_CODE_BAD_REQUEST = 400;
const STATUS_CODE_UDF_FAILED = 560;
let specifiedFetch = void 0;
function setFetch(f) {
    specifiedFetch = f;
}
class ConvexHttpClient {
    /**
   * Obtain the {@link ConvexHttpClient}'s URL to its backend.
   * @deprecated Use url, which returns the url without /api at the end.
   *
   * @returns The URL to the Convex backend, including the client's API version.
   */ backendUrl() {
        return "".concat(this.address, "/api");
    }
    /**
   * Return the address for this client, useful for creating a new client.
   *
   * Not guaranteed to match the address with which this client was constructed:
   * it may be canonicalized.
   */ get url() {
        return this.address;
    }
    /**
   * Set the authentication token to be used for subsequent queries and mutations.
   *
   * Should be called whenever the token changes (i.e. due to expiration and refresh).
   *
   * @param value - JWT-encoded OpenID Connect identity token.
   */ setAuth(value) {
        this.clearAuth();
        this.auth = value;
    }
    /**
   * Set admin auth token to allow calling internal queries, mutations, and actions
   * and acting as an identity.
   *
   * @internal
   */ setAdminAuth(token, actingAsIdentity) {
        this.clearAuth();
        if (actingAsIdentity !== void 0) {
            const bytes = new TextEncoder().encode(JSON.stringify(actingAsIdentity));
            const actingAsIdentityEncoded = btoa(String.fromCodePoint(...bytes));
            this.adminAuth = "".concat(token, ":").concat(actingAsIdentityEncoded);
        } else {
            this.adminAuth = token;
        }
    }
    /**
   * Clear the current authentication token if set.
   */ clearAuth() {
        this.auth = void 0;
        this.adminAuth = void 0;
    }
    /**
   * Sets whether the result log lines should be printed on the console or not.
   *
   * @internal
   */ setDebug(debug) {
        this.debug = debug;
    }
    /**
   * Used to customize the fetch behavior in some runtimes.
   *
   * @internal
   */ setFetchOptions(fetchOptions) {
        this.fetchOptions = fetchOptions;
    }
    /**
   * This API is experimental: it may change or disappear.
   *
   * Execute a Convex query function at the same timestamp as every other
   * consistent query execution run by this HTTP client.
   *
   * This doesn't make sense for long-lived ConvexHttpClients as Convex
   * backends can read a limited amount into the past: beyond 30 seconds
   * in the past may not be available.
   *
   * Create a new client to use a consistent time.
   *
   * @param name - The name of the query.
   * @param args - The arguments object for the query. If this is omitted,
   * the arguments will be `{}`.
   * @returns A promise of the query's result.
   *
   * @deprecated This API is experimental: it may change or disappear.
   */ async consistentQuery(query) {
        for(var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){
            args[_key - 1] = arguments[_key];
        }
        const queryArgs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$common$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseArgs"])(args[0]);
        const timestampPromise = this.getTimestamp();
        return await this.queryInner(query, queryArgs, {
            timestampPromise
        });
    }
    async getTimestamp() {
        if (this.encodedTsPromise) {
            return this.encodedTsPromise;
        }
        return this.encodedTsPromise = this.getTimestampInner();
    }
    async getTimestampInner() {
        const localFetch = specifiedFetch || fetch;
        const headers = {
            "Content-Type": "application/json",
            "Convex-Client": "npm-".concat(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["version"])
        };
        const response = await localFetch("".concat(this.address, "/api/query_ts"), {
            ...this.fetchOptions,
            method: "POST",
            headers
        });
        if (!response.ok) {
            throw new Error(await response.text());
        }
        const { ts } = await response.json();
        return ts;
    }
    /**
   * Execute a Convex query function.
   *
   * @param name - The name of the query.
   * @param args - The arguments object for the query. If this is omitted,
   * the arguments will be `{}`.
   * @returns A promise of the query's result.
   */ async query(query) {
        for(var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){
            args[_key - 1] = arguments[_key];
        }
        const queryArgs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$common$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseArgs"])(args[0]);
        return await this.queryInner(query, queryArgs, {});
    }
    async queryInner(query, queryArgs, options) {
        const name = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$server$2f$api$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getFunctionName"])(query);
        const args = [
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$value$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["convexToJson"])(queryArgs)
        ];
        const headers = {
            "Content-Type": "application/json",
            "Convex-Client": "npm-".concat(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["version"])
        };
        if (this.adminAuth) {
            headers["Authorization"] = "Convex ".concat(this.adminAuth);
        } else if (this.auth) {
            headers["Authorization"] = "Bearer ".concat(this.auth);
        }
        const localFetch = specifiedFetch || fetch;
        const timestamp = options.timestampPromise ? await options.timestampPromise : void 0;
        const body = JSON.stringify({
            path: name,
            format: "convex_encoded_json",
            args,
            ...timestamp ? {
                ts: timestamp
            } : {}
        });
        const endpoint = timestamp ? "".concat(this.address, "/api/query_at_ts") : "".concat(this.address, "/api/query");
        const response = await localFetch(endpoint, {
            ...this.fetchOptions,
            body,
            method: "POST",
            headers
        });
        if (!response.ok && response.status !== STATUS_CODE_UDF_FAILED) {
            throw new Error(await response.text());
        }
        const respJSON = await response.json();
        if (this.debug) {
            var _respJSON_logLines;
            for (const line of (_respJSON_logLines = respJSON.logLines) !== null && _respJSON_logLines !== void 0 ? _respJSON_logLines : []){
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$logging$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logForFunction"])(this.logger, "info", "query", name, line);
            }
        }
        switch(respJSON.status){
            case "success":
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$value$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsonToConvex"])(respJSON.value);
            case "error":
                if (respJSON.errorData !== void 0) {
                    throw forwardErrorData(respJSON.errorData, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$errors$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ConvexError"](respJSON.errorMessage));
                }
                throw new Error(respJSON.errorMessage);
            default:
                throw new Error("Invalid response: ".concat(JSON.stringify(respJSON)));
        }
    }
    async mutationInner(mutation, mutationArgs) {
        const name = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$server$2f$api$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getFunctionName"])(mutation);
        const body = JSON.stringify({
            path: name,
            format: "convex_encoded_json",
            args: [
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$value$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["convexToJson"])(mutationArgs)
            ]
        });
        const headers = {
            "Content-Type": "application/json",
            "Convex-Client": "npm-".concat(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["version"])
        };
        if (this.adminAuth) {
            headers["Authorization"] = "Convex ".concat(this.adminAuth);
        } else if (this.auth) {
            headers["Authorization"] = "Bearer ".concat(this.auth);
        }
        const localFetch = specifiedFetch || fetch;
        const response = await localFetch("".concat(this.address, "/api/mutation"), {
            ...this.fetchOptions,
            body,
            method: "POST",
            headers
        });
        if (!response.ok && response.status !== STATUS_CODE_UDF_FAILED) {
            throw new Error(await response.text());
        }
        const respJSON = await response.json();
        if (this.debug) {
            var _respJSON_logLines;
            for (const line of (_respJSON_logLines = respJSON.logLines) !== null && _respJSON_logLines !== void 0 ? _respJSON_logLines : []){
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$logging$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logForFunction"])(this.logger, "info", "mutation", name, line);
            }
        }
        switch(respJSON.status){
            case "success":
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$value$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsonToConvex"])(respJSON.value);
            case "error":
                if (respJSON.errorData !== void 0) {
                    throw forwardErrorData(respJSON.errorData, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$errors$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ConvexError"](respJSON.errorMessage));
                }
                throw new Error(respJSON.errorMessage);
            default:
                throw new Error("Invalid response: ".concat(JSON.stringify(respJSON)));
        }
    }
    async processMutationQueue() {
        if (this.isProcessingQueue) {
            return;
        }
        this.isProcessingQueue = true;
        while(this.mutationQueue.length > 0){
            const { mutation, args, resolve, reject } = this.mutationQueue.shift();
            try {
                const result = await this.mutationInner(mutation, args);
                resolve(result);
            } catch (error) {
                reject(error);
            }
        }
        this.isProcessingQueue = false;
    }
    enqueueMutation(mutation, args) {
        return new Promise((resolve, reject)=>{
            this.mutationQueue.push({
                mutation,
                args,
                resolve,
                reject
            });
            void this.processMutationQueue();
        });
    }
    /**
   * Execute a Convex mutation function. Mutations are queued by default.
   *
   * @param name - The name of the mutation.
   * @param args - The arguments object for the mutation. If this is omitted,
   * the arguments will be `{}`.
   * @param options - An optional object containing
   * @returns A promise of the mutation's result.
   */ async mutation(mutation) {
        for(var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){
            args[_key - 1] = arguments[_key];
        }
        const [fnArgs, options] = args;
        const mutationArgs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$common$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseArgs"])(fnArgs);
        const queued = !(options === null || options === void 0 ? void 0 : options.skipQueue);
        if (queued) {
            return await this.enqueueMutation(mutation, mutationArgs);
        } else {
            return await this.mutationInner(mutation, mutationArgs);
        }
    }
    /**
   * Execute a Convex action function. Actions are not queued.
   *
   * @param name - The name of the action.
   * @param args - The arguments object for the action. If this is omitted,
   * the arguments will be `{}`.
   * @returns A promise of the action's result.
   */ async action(action) {
        for(var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){
            args[_key - 1] = arguments[_key];
        }
        const actionArgs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$common$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseArgs"])(args[0]);
        const name = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$server$2f$api$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getFunctionName"])(action);
        const body = JSON.stringify({
            path: name,
            format: "convex_encoded_json",
            args: [
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$value$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["convexToJson"])(actionArgs)
            ]
        });
        const headers = {
            "Content-Type": "application/json",
            "Convex-Client": "npm-".concat(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["version"])
        };
        if (this.adminAuth) {
            headers["Authorization"] = "Convex ".concat(this.adminAuth);
        } else if (this.auth) {
            headers["Authorization"] = "Bearer ".concat(this.auth);
        }
        const localFetch = specifiedFetch || fetch;
        const response = await localFetch("".concat(this.address, "/api/action"), {
            ...this.fetchOptions,
            body,
            method: "POST",
            headers
        });
        if (!response.ok && response.status !== STATUS_CODE_UDF_FAILED) {
            throw new Error(await response.text());
        }
        const respJSON = await response.json();
        if (this.debug) {
            var _respJSON_logLines;
            for (const line of (_respJSON_logLines = respJSON.logLines) !== null && _respJSON_logLines !== void 0 ? _respJSON_logLines : []){
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$logging$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logForFunction"])(this.logger, "info", "action", name, line);
            }
        }
        switch(respJSON.status){
            case "success":
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$value$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsonToConvex"])(respJSON.value);
            case "error":
                if (respJSON.errorData !== void 0) {
                    throw forwardErrorData(respJSON.errorData, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$errors$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ConvexError"](respJSON.errorMessage));
                }
                throw new Error(respJSON.errorMessage);
            default:
                throw new Error("Invalid response: ".concat(JSON.stringify(respJSON)));
        }
    }
    /**
   * Execute a Convex function of an unknown type. These function calls are not queued.
   *
   * @param name - The name of the function.
   * @param args - The arguments object for the function. If this is omitted,
   * the arguments will be `{}`.
   * @returns A promise of the function's result.
   *
   * @internal
   */ async function(anyFunction, componentPath) {
        for(var _len = arguments.length, args = new Array(_len > 2 ? _len - 2 : 0), _key = 2; _key < _len; _key++){
            args[_key - 2] = arguments[_key];
        }
        const functionArgs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$common$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseArgs"])(args[0]);
        const name = typeof anyFunction === "string" ? anyFunction : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$server$2f$api$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getFunctionName"])(anyFunction);
        const body = JSON.stringify({
            componentPath,
            path: name,
            format: "convex_encoded_json",
            args: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$value$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["convexToJson"])(functionArgs)
        });
        const headers = {
            "Content-Type": "application/json",
            "Convex-Client": "npm-".concat(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["version"])
        };
        if (this.adminAuth) {
            headers["Authorization"] = "Convex ".concat(this.adminAuth);
        } else if (this.auth) {
            headers["Authorization"] = "Bearer ".concat(this.auth);
        }
        const localFetch = specifiedFetch || fetch;
        const response = await localFetch("".concat(this.address, "/api/function"), {
            ...this.fetchOptions,
            body,
            method: "POST",
            headers
        });
        if (!response.ok && response.status !== STATUS_CODE_UDF_FAILED) {
            throw new Error(await response.text());
        }
        const respJSON = await response.json();
        if (this.debug) {
            var _respJSON_logLines;
            for (const line of (_respJSON_logLines = respJSON.logLines) !== null && _respJSON_logLines !== void 0 ? _respJSON_logLines : []){
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$logging$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logForFunction"])(this.logger, "info", "any", name, line);
            }
        }
        switch(respJSON.status){
            case "success":
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$value$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsonToConvex"])(respJSON.value);
            case "error":
                if (respJSON.errorData !== void 0) {
                    throw forwardErrorData(respJSON.errorData, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$errors$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ConvexError"](respJSON.errorMessage));
                }
                throw new Error(respJSON.errorMessage);
            default:
                throw new Error("Invalid response: ".concat(JSON.stringify(respJSON)));
        }
    }
    /**
   * Create a new {@link ConvexHttpClient}.
   *
   * @param address - The url of your Convex deployment, often provided
   * by an environment variable. E.g. `https://small-mouse-123.convex.cloud`.
   * @param options - An object of options.
   * - `skipConvexDeploymentUrlCheck` - Skip validating that the Convex deployment URL looks like
   * `https://happy-animal-123.convex.cloud` or localhost. This can be useful if running a self-hosted
   * Convex backend that uses a different URL.
   * - `logger` - A logger or a boolean. If not provided, logs to the console.
   * You can construct your own logger to customize logging to log elsewhere
   * or not log at all, or use `false` as a shorthand for a no-op logger.
   * A logger is an object with 4 methods: log(), warn(), error(), and logVerbose().
   * These methods can receive multiple arguments of any types, like console.log().
   * - `auth` - A JWT containing identity claims accessible in Convex functions.
   * This identity may expire so it may be necessary to call `setAuth()` later,
   * but for short-lived clients it's convenient to specify this value here.
   */ constructor(address, options){
        __publicField(this, "address");
        __publicField(this, "auth");
        __publicField(this, "adminAuth");
        __publicField(this, "encodedTsPromise");
        __publicField(this, "debug");
        __publicField(this, "fetchOptions");
        __publicField(this, "logger");
        __publicField(this, "mutationQueue", []);
        __publicField(this, "isProcessingQueue", false);
        if (typeof options === "boolean") {
            throw new Error("skipConvexDeploymentUrlCheck as the second argument is no longer supported. Please pass an options object, `{ skipConvexDeploymentUrlCheck: true }`.");
        }
        const opts = options !== null && options !== void 0 ? options : {};
        if (opts.skipConvexDeploymentUrlCheck !== true) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$common$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["validateDeploymentUrl"])(address);
        }
        this.logger = (options === null || options === void 0 ? void 0 : options.logger) === false ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$logging$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["instantiateNoopLogger"])({
            verbose: false
        }) : (options === null || options === void 0 ? void 0 : options.logger) !== true && (options === null || options === void 0 ? void 0 : options.logger) ? options.logger : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$logging$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["instantiateDefaultLogger"])({
            verbose: false
        });
        this.address = address;
        this.debug = true;
        if (options === null || options === void 0 ? void 0 : options.auth) {
            this.setAuth(options.auth);
        }
    }
}
function forwardErrorData(errorData, error) {
    error.data = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$values$2f$value$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsonToConvex"])(errorData);
    return error;
} //# sourceMappingURL=http_client.js.map
}),
"[project]/node_modules/convex/dist/esm/browser/index.js [app-client] (ecmascript) <locals>", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$sync$2f$client$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/browser/sync/client.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$simple_client$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/browser/simple_client.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$http_client$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/browser/http_client.js [app-client] (ecmascript)"); //# sourceMappingURL=index.js.map
"use strict";
;
;
;
}),
]);

//# sourceMappingURL=node_modules_eea132ca._.js.map