import React, { useState, useRef, useImperativeHandle, useEffect } from 'react';
import { CanvasElementData, FocusRequest, NavDirection, BlockType, BaseComponentProps, CanvasRef } from '../types';
import EditableText from './EditableText';
import VariableBlock from './blocks/VariableBlock';
import AIInstructionBlock from './blocks/AIInstructionBlock';
import CategoryBlock from './blocks/CategoryBlock';

type DropMode = 'split' | 'insertBefore' | 'insertAfter';

interface CanvasProps {
  parts: CanvasElementData[][];
  onUpdatePart: (partId: string, newProps: Partial<CanvasElementData>) => void;
  onDropPart: (payload: { draggedData: any, targetPartId: string, lineIndex: number, index?: number, mode: DropMode }) => void;
  onDeletePart: (partId: string) => void;
  onSplitPart: (partId: string, splitIndex: number, currentContent: string) => void;
  onMergePart: (partId: string) => void;
  focusRequest?: FocusRequest | null;
  selectedPartId?: string | null;
  onSelectPart: (partId: string) => void;
  onEnterAIBlock: (partId: string) => void;
  isNested?: boolean;
  onNavigateBoundary?: (direction: NavDirection) => void;
  ref?: React.Ref<CanvasRef>;
}

const Canvas = (props: CanvasProps) => {
  const { 
    parts, 
    onUpdatePart, 
    onDropPart, 
    onDeletePart, 
    onSplitPart, 
    onMergePart, 
    focusRequest,
    selectedPartId,
    onSelectPart,
    onEnterAIBlock,
    isNested = false, 
    onNavigateBoundary,
    ref,
  } = props;
  const [indicatorStyle, setIndicatorStyle] = useState<React.CSSProperties>({ display: 'none' });
  const canvasRef = useRef<HTMLDivElement>(null);
  const partRefs = useRef<Map<string, HTMLElement | CanvasRef>>(new Map());
  const dropTargetRef = useRef<{ targetPartId: string, lineIndex: number, index?: number, mode: DropMode } | null>(null);

  useImperativeHandle(ref, () => ({
      focus: (position: 'start' | 'end' = 'start') => {
          const lineIndex = position === 'start' ? 0 : parts.length - 1;
          if (!parts[lineIndex]) return;
          const partIndex = position === 'start' ? 0 : parts[lineIndex].length - 1;
          const partId = parts[lineIndex]?.[partIndex]?.id;
          if (partId) {
            onSelectPart(partId);
          }
      }
  }));
  
  useEffect(() => {
    if (selectedPartId) {
        const targetRef = partRefs.current.get(selectedPartId);
        if (targetRef) {
             if ((document.activeElement as HTMLElement)?.dataset.partId === selectedPartId) {
                return;
            }
            
            if (window.getSelection) {
                window.getSelection()?.removeAllRanges();
            }

            if (targetRef instanceof HTMLElement) {
                targetRef.focus();
            } else {
                targetRef.focus();
            }
        }
    }
  }, [selectedPartId]);

  useEffect(() => {
    if (focusRequest && canvasRef.current) {
      const { partId, position, selectAll } = focusRequest;
      const targetEl = canvasRef.current.querySelector(`[data-part-id="${partId}"]`) as HTMLElement;
      if (targetEl) {
          targetEl.focus();
          if (targetEl.hasAttribute('contenteditable')) {
              const selection = window.getSelection();
              const range = document.createRange();
              const textNode = targetEl.firstChild;
              if(selection && textNode) {
                  if (selectAll) {
                      range.selectNodeContents(targetEl);
                  } else {
                      const offset = position === 'start' ? 0 : position === 'end' ? textNode.textContent?.length ?? 0 : position;
                      try {
                        range.setStart(textNode, Math.min(offset, textNode.textContent?.length ?? 0));
                        range.collapse(true);
                      } catch(e) { /* ignore range errors */ }
                  }
                  selection.removeAllRanges();
                  selection.addRange(range);
              }
          }
      }
    }
  }, [focusRequest]);

  const showIndicator = (rect: DOMRect, type: 'caret' | 'line' = 'caret') => {
    if (canvasRef.current) {
      const parentRect = canvasRef.current.getBoundingClientRect();
      const style: React.CSSProperties = {
        display: 'block',
        position: 'absolute',
        backgroundColor: '#4f46e5',
        pointerEvents: 'none',
        zIndex: 10,
        top: `${rect.top - parentRect.top}px`,
        left: `${rect.left - parentRect.left}px`,
      };
      if (type === 'caret') {
        style.height = `${rect.height}px`;
        style.width = `1.5px`;
      } else {
        style.height = `2px`;
        style.width = `${rect.width}px`;
        style.top = `${rect.top - parentRect.top - 1}px`;
      }
      setIndicatorStyle(style);
    }
  };

  const hideIndicator = () => setIndicatorStyle({ display: 'none' });

  const handleDragStart = (e: React.DragEvent, part: CanvasElementData) => {
    e.stopPropagation();
    e.dataTransfer.effectAllowed = 'move';
    e.dataTransfer.setData('application/json', JSON.stringify({ movePartId: part.id }));
  };
  
  const handleCanvasDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    const target = e.target as HTMLElement;

    if (target.closest('[data-part-id]')) { return; } // Let part handlers take over

    const lines = Array.from(canvasRef.current?.querySelectorAll('.canvas-line') ?? []) as HTMLElement[];
    if (lines.length === 0) return;

    let closest = { el: null as HTMLElement | null, distance: Infinity };
    lines.forEach(el => {
      const rect = el.getBoundingClientRect();
      const distY = Math.abs(e.clientY - (rect.top + rect.height / 2));
      if (distY < closest.distance) {
        closest = { el, distance: distY };
      }
    });
    
    if (closest.el) {
      const rect = closest.el.getBoundingClientRect();
      const lineIndex = parseInt(closest.el.dataset.lineIndex!, 10);
      const isAfter = e.clientY > rect.top + rect.height / 2;
      const targetPart = parts[lineIndex]?.[0] || parts[lineIndex-1]?.[0];
      if (!targetPart) return;
      
      const mode = isAfter ? 'insertAfter' : 'insertBefore';
      const targetLineIndex = isAfter ? lineIndex : lineIndex;

      
      showIndicator(new DOMRect(rect.left, isAfter ? rect.bottom : rect.top, rect.width, 2), 'line');
      dropTargetRef.current = { targetPartId: targetPart.id, lineIndex: targetLineIndex, mode };
    }
  };

  const handleCanvasDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    hideIndicator();
    
    if (dropTargetRef.current) {
      const draggedData = JSON.parse(e.dataTransfer.getData('application/json'));
      onDropPart({ draggedData, ...dropTargetRef.current });
      dropTargetRef.current = null;
    }
  }

  const handleNavigate = (partId: string, direction: NavDirection) => {
    if (!canvasRef.current) {
        return;
    }
    
    const query = isNested 
        ? '[data-canvas-element="true"]'
        : '[data-canvas-element="true"]:not([data-canvas-id="nested"] [data-canvas-element="true"])';

    const allParts = Array.from(
        canvasRef.current.querySelectorAll(query)
    ) as HTMLElement[];

    const currentIndex = allParts.findIndex(el => el.dataset.partId === partId);
    if (currentIndex === -1) {
        return;
    }
    const currentElement = allParts[currentIndex];

    if (direction === 'left' || direction === 'right') {
        const nextIndex = direction === 'left' ? currentIndex - 1 : currentIndex + 1;
        if (nextIndex >= 0 && nextIndex < allParts.length) {
            const nextPartId = allParts[nextIndex].dataset.partId;
            if (nextPartId) {
                onSelectPart(nextPartId);
            }
        } else if (isNested && onNavigateBoundary) {
            onNavigateBoundary(direction);
        }
    } else { // up or down
        const currentRect = currentElement.getBoundingClientRect();
        const currentLineEl = currentElement.closest('.canvas-line') as HTMLElement | null;
        if (!currentLineEl) return;
        
        const targetLineEl = direction === 'up' ? currentLineEl?.previousElementSibling : currentLineEl?.nextElementSibling;
        
        if (targetLineEl) {
            const candidateElements = Array.from(targetLineEl.querySelectorAll('[data-canvas-element="true"]')) as HTMLElement[];

            if (candidateElements.length > 0) {
                 const currentLineElements = Array.from(currentLineEl.querySelectorAll('[data-canvas-element="true"]'));
                 // If the current line has only one element, navigate to the first element of the target line.
                 // This provides more predictable behavior for single-element lines.
                if (currentLineElements.length === 1) {
                    const targetPartId = candidateElements[0].dataset.partId;
                    if (targetPartId) {
                        onSelectPart(targetPartId);
                        return;
                    }
                }
            
                // Default behavior: Find the element on the target line that is horizontally closest.
                let closest = { el: null as HTMLElement | null, distance: Infinity };
                const targetX = currentRect.left + currentRect.width / 2;
                candidateElements.forEach(el => {
                    const rect = el.getBoundingClientRect();
                    const elX = rect.left + rect.width / 2;
                    const dist = Math.abs(elX - targetX);
                    if (dist < closest.distance) {
                        closest = { el, distance: dist };
                    }
                });
                if (closest.el?.dataset.partId) {
                    onSelectPart(closest.el.dataset.partId);
                }
            }
        } else if (isNested && onNavigateBoundary) {
            onNavigateBoundary(direction);
        }
    }
  };
  
  const bgClass = isNested ? '' : 'bg-white';
  const paddingClass = isNested ? '' : 'p-8';

  const commonProps: Omit<BaseComponentProps, 'isSelected'> = {
      onSelectPart,
      onNavigate: handleNavigate,
      onDragStart: handleDragStart,
      onUpdatePart,
      onDropPart,
      onDeletePart,
      onSplitPart,
      onMergePart,
      onEnterAIBlock,
      showIndicator,
      hideIndicator,
      onNavigateBoundary,
      selectedPartId,
      focusRequest,
  };

  return (
    <div 
        ref={canvasRef} 
        className={`relative text-gray-800 text-lg leading-relaxed focus:outline-none ${bgClass} ${paddingClass}`}
        onDragOver={handleCanvasDragOver}
        onDrop={handleCanvasDrop}
        onDragLeave={hideIndicator}
        data-canvas-id={isNested ? "nested" : "main"}
    >
        <div style={indicatorStyle}></div>
        {parts.map((line, lineIndex) => (
            <div key={line.map(p=>p.id).join('-')} className="canvas-line py-1" data-line-index={lineIndex}>
                {line.map((part) => {
                    const refCallback = (el: HTMLElement | CanvasRef | null) => {
                        if (el) {
                            partRefs.current.set(part.id, el);
                        } else {
                            partRefs.current.delete(part.id);
                        }
                    };

                    const isSelected = part.id === selectedPartId;
                    
                    switch (part.blockType) {
                        case BlockType.Text:
                            return <EditableText key={part.id} {...commonProps} {...part} isSelected={isSelected} ref={refCallback as any} />;
                        case BlockType.Variable:
                            return <VariableBlock key={part.id} {...commonProps} {...part} isSelected={isSelected} ref={refCallback as React.Ref<HTMLSpanElement>} />;
                        case BlockType.AIInstruction:
                            return <AIInstructionBlock key={part.id} {...commonProps} {...part} isSelected={isSelected} ref={refCallback as React.Ref<CanvasRef>} />;
                        case BlockType.Category:
                            return <CategoryBlock key={part.id} {...commonProps} {...part} isSelected={isSelected} ref={refCallback as React.Ref<HTMLSpanElement>} />;
                        default:
                            return null;
                    }
                })}
            </div>
        ))}
    </div>
  );
};

export default Canvas;