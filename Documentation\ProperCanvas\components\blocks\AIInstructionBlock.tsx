
import React, { useImperativeHandle, useRef } from 'react';
import { NavDirection, AIInstructionBlockProps, CanvasRef } from '../../types';
import { AIInstructionIcon, DeleteIcon } from '../icons/Icons';
import Canvas from '../Canvas';

const AIInstructionBlock = (props: AIInstructionBlockProps) => {
    const { 
        id,
        blockType,
        parts,
        isSelected,
        onUpdatePart,
        onDropPart,
        onDeletePart,
        onSplitPart,
        onMergePart,
        onNavigate,
        focusRequest,
        onSelectPart,
        onEnterAIBlock,
        selectedPartId,
        onDragStart,
        ref,
    } = props;
    
    const canvasRef = useRef<CanvasRef>(null);
    const localSpanRef = useRef<HTMLSpanElement>(null);

    useImperativeHandle(ref, () => ({
        focus: (position: 'start' | 'end' = 'start') => {
            canvasRef.current?.focus(position);
        }
    }));

    /**
     * Handles navigation requests from the boundary of the nested canvas.
     * When the user tries to navigate out of the inner canvas, this function
     * calls the `onNavigate` prop passed from the parent canvas, effectively
     * "bubbling up" the navigation event.
     */
    const handleNestedCanvasBoundary = (direction: NavDirection) => {
        // We use our own part `id` to tell the parent Canvas where the navigation is coming from.
        onNavigate(id, direction);
    };

    const handleKeyDown = (e: React.KeyboardEvent) => {
        // FIX: Only handle key events if they originate from the block's main container,
        // not from children like the nested canvas. This prevents the block from deleting
        // itself when the user presses Backspace inside the nested EditableText.
        if (e.target !== e.currentTarget) {
            return;
        }

        if (e.key === 'Enter') {
            e.preventDefault();
            e.stopPropagation();
            onEnterAIBlock(id);
        } else if (['ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown'].includes(e.key)) {
             e.preventDefault();
             onNavigate(id, e.key.replace('Arrow', '').toLowerCase() as NavDirection);
        } else if (e.key === 'Backspace' || e.key === 'Delete') {
            e.preventDefault();
            onDeletePart(id);
        }
    };

    const selectedClass = isSelected ? 'ring-2 ring-green-500 ring-offset-1' : '';
    
    return (
        <span 
            ref={localSpanRef}
            className={`group/ai-block relative inline-flex flex-row items-baseline bg-green-100 text-gray-800 text-lg rounded-md align-baseline focus:outline-none ${selectedClass}`}
            tabIndex={-1}
            onFocus={(e) => {
                if(e.target === e.currentTarget) {
                    onSelectPart(id)
                }
            }}
            onKeyDown={handleKeyDown}
            draggable={true}
            onDragStart={(e) => onDragStart(e, { id, blockType, parts })}
            data-canvas-element="true"
            data-part-id={id}
        >
            <button
              onClick={() => onDeletePart(id)}
              className="absolute top-0 right-0 -mt-1 -mr-1 p-0.5 bg-gray-500 text-white rounded-full opacity-0 group-hover/ai-block:opacity-100 transition-opacity duration-150 focus:opacity-100 focus:outline-none z-10"
              aria-label="Delete AI Instruction"
            >
              <DeleteIcon className="w-3 h-3" />
            </button>
            <span className="flex-shrink-0 cursor-grab active:cursor-grabbing px-2 py-1">
                <AIInstructionIcon className="w-5 h-5 text-green-600" />
            </span>
            <span className="font-mono text-xl text-green-600 select-none">[</span>
            <div className="mx-1">
                 <Canvas
                    ref={canvasRef}
                    parts={parts}
                    onUpdatePart={onUpdatePart}
                    onDropPart={onDropPart}
                    onDeletePart={onDeletePart}
                    onSplitPart={onSplitPart}
                    onMergePart={onMergePart}
                    focusRequest={focusRequest}
                    isNested={true}
                    onNavigateBoundary={handleNestedCanvasBoundary}
                    selectedPartId={selectedPartId}
                    onSelectPart={onSelectPart}
                    onEnterAIBlock={onEnterAIBlock}
                 />
            </div>
            <span className="font-mono text-xl text-green-600 select-none pr-2">]</span>
        </span>
    );
};

export default AIInstructionBlock;
