# Technical Sheet: AI Inline Component Canvas

## 1. Project Overview

This project is a React-based, block-style text editor designed to function like modern documentation tools (e.g., Notion, Coda). Its core feature is the ability to seamlessly embed rich, interactive "blocks" (like variables or AI instructions) inline with standard text content.

The architecture prioritizes a declarative, unified, and type-safe data structure, robust keyboard-first navigation, and a scalable, self-contained component model using modern React features. The ultimate goal is to provide a user-friendly interface for creating complex, structured templates that can be serialized into a machine-readable format for processing by systems like an LLM, as demonstrated by the "Rendered View".

---

## 2. Core Architecture & Methodology

### 2.1. State Management & Data Structure

The entire state of the canvas is managed by the root `App.tsx` component in a single state variable: `canvasParts`. The architecture is built around a single, unified type system where **every element, including text, is a block**.

- **Unified Data Model (Discriminated Union)**: The state is a two-dimensional array of a single union type: `CanvasElementData[][]`.
  - The `CanvasElementData` type (defined in `types.ts`) is a **discriminated union** of all possible element types (`TextElement`, `VariableElement`, etc.).
  - The `blockType` property, which exists on the `BaseElement` interface, acts as the **single discriminant** for the entire union. This simplifies the model by treating text just like any other block.
  - This approach creates a **single source of truth**: one set of interfaces defines both the application's state and the props for the components. Redundant data models have been completely eliminated.

- **Immutability**: All state modifications are immutable. The `performModification` helper function in `App.tsx` takes a modifier function, creates a deep copy (`JSON.parse(JSON.stringify(...))`) of the current state, applies the changes, and then runs a `cleanup` routine before setting the new state.

- **Cleanup Routine**: The `cleanup` function, called after every modification, is critical for maintaining data integrity. It performs two key actions:
  1.  **Merges adjacent text blocks** on the same line.
  2.  **Removes empty text blocks** unless it's the only block on a line.
  3.  **Removes empty lines** to prevent orphaned blank spaces.

- **Nested Structures**: The `AIInstructionBlock` can contain its own `CanvasElementData[][]` structure. State modifications in nested contexts are handled by a recursive utility, `recursivelyModifyParts`, which traverses the state tree to find and update the target element without needing to know its depth.

### 2.2. Component Hierarchy & Philosophy

The architecture follows a clear, hierarchical model designed to separate concerns. The data flow is direct from the canvas to each individual element.

`App` -> `Canvas` -> `(EditableText | *Block)`

- **`App.tsx` (The Brain)**: Manages all state (`CanvasElementData[][]`) and contains all data manipulation logic (splitting, merging, deleting elements). It passes handler functions down as props.

- **`Canvas.tsx` (The Layout Engine)**:
  - Renders the `CanvasElementData[][]` data by mapping over it. It uses a **single, type-safe `switch` statement on the `blockType` discriminant** to render the appropriate component (`EditableText`, `VariableBlock`, etc.) for each element.
  - Employs a **declarative, spread-props pattern** (`<Component {...commonProps} {...part} />`) for clean and direct data flow.
  - Contains the **DOM-aware navigation engine** (`handleNavigate` function). It doesn't navigate based on array indices, but on the rendered positions of elements.
  - Manages canvas-wide drag-and-drop listeners for empty areas.
  - **Modern React**: Uses the standard `ref` as a prop (`ref={...}`) instead of the deprecated `forwardRef` higher-order component, aligning with React 19+ best practices.

- **Leaf Components (`EditableText`, `*Block.tsx`) (Self-Contained Elements)**:
  - Each component is **fully self-contained and responsible for all its own interactions**.
  - **Responsibilities**:
    1.  **Rendering**: Responsible for their unique appearance based on their flat props.
    2.  **Selection & Focus**: Applies visual selection styling (`isSelected` prop) and manages its own focus behavior.
    3.  **Keyboard Navigation**: Implements an `onKeyDown` handler to capture arrow key events and call the `onNavigate` prop when appropriate.
    4.  **Drag & Drop**: Acts as the `draggable` source for itself. It reconstructs its full data object from its props when calling `onDragStart`.
    5.  **Robust Event Handling**: Prevents unwanted event bubbling from child elements. For example, the `Backspace` key event from a nested text input is stopped from triggering the delete action on its parent block by checking `if (e.target !== e.currentTarget) return;`.

### 2.3. Unified Component Interface (Props-as-Data)

A key to this simplified architecture is the type system defined in `types.ts`, which enforces a **fully unified props and data model**.

- **`BaseComponentProps`**: This interface defines the contract for any element that can live on the canvas. It includes only the shared, non-data properties:
    - The `isSelected` boolean for styling.
    - All necessary event handlers (`onNavigate`, `onSelectPart`, `onDeletePart`, etc.).
    - State values like `selectedPartId` and `focusRequest`.

- **Specific Component Props**: Each canvas component (`EditableText`, `VariableBlock`, etc.) has its own props type (e.g., `VariableBlockProps`) which is an **intersection** of `BaseComponentProps`, its specific data interface (e.g., `VariableElement`), and a `ref` prop.
  - Example: `type VariableBlockProps = BaseComponentProps & VariableElement & { ref?: React.Ref<HTMLSpanElement> };`.
  - **There is no nested `part` object.** All data (`id`, `blockType`, `name`, `content`, etc.) are passed as top-level props because the component's props *are* its data model.

- **Benefits**: This approach provides strong type safety, eliminates the need for any type-casting within components, removes an entire layer of abstract data types, and makes the `Canvas`'s rendering logic clean, declarative, and explicit.

### 2.4. Decoupling: The Editor Model vs. The Rendered Output

The architecture intentionally separates the data model required for the interactive editor from the final, machine-readable output.

- **`CanvasElementData[][]` as an Internal Model**: The state structure is designed to support a rich user experience with features like focus management, selection, and complex nested elements.
- **`RenderedView.tsx` as the Serializer**: This component demonstrates how the internal data model can be serialized into a different, often simpler, format. It traverses the state tree and converts each block into a specific string syntax (e.g., `{{Variable}}`, `$[Category]`, `%[Instruction]`).
- **Architectural Benefit**: This separation of concerns is crucial. It allows the user-facing editor to be complex and feature-rich without compromising the simplicity and stability of the output format consumed by other systems, such as an LLM.

---

## 3. Key Feature Implementation

### 3.1. Text Editing (`EditableText.tsx`)

- Uses a `contentEditable` span to provide a rich text editing experience.
- **Boundary-Aware Navigation**: Its `onKeyDown` handler is designed to feel natural.
  - It checks if the caret is at a text boundary (start/end, top/bottom).
  - If **not** at a boundary, it calls `e.stopPropagation()` to allow the default browser behavior (moving the cursor) and prevent canvas navigation.
  - If **at** a boundary, it `preventDefault()` and calls the `onNavigate` prop to trigger a canvas-level move to the next item.
- `Enter` and `Backspace` at boundaries trigger the `onSplitPart` and `onMergePart` handlers.

### 3.2. 2D Keyboard Navigation (`Canvas.tsx`)

The navigation engine is geometry-based and DOM-aware, not index-based.

- **`handleNavigate` function**:
  1.  When a component's `onKeyDown` calls `onNavigate`, this function in `Canvas.tsx` is executed.
  2.  It queries the DOM for all focusable canvas elements (`[data-canvas-element="true"]`). The key to supporting nested canvases (like in `AIInstructionBlock`) is a **conditional query**: when running in a nested context, it queries for its own children; otherwise, it queries for main-level elements and excludes nested ones.
  3.  **Left/Right**: Moves to the previous/next element in the flat DOM query list.
  4.  **Up/Down**: It gets the current element's position, finds all elements on the target line, and calculates the **closest element based on horizontal distance** between their center points. To improve predictability, if navigating from a line containing only a single element, it defaults to selecting the *first* element on the target line.
  5.  **Boundary Handling**: When navigation reaches the start or end of a nested canvas, it calls the `onNavigateBoundary` callback. This allows the parent `AIInstructionBlock` to "bubble up" the event, telling the main canvas to navigate away from the block, creating a seamless user experience.

### 3.3. Drag & Drop System

- **Source**: Each component is its own `draggable` source. `Toolbar.tsx` is the source for creating new items.
- **Target & Indicators**:
  - The main `Canvas` has an `onDragOver` handler to detect dragging over empty space between lines, showing a full-width line indicator.
  - `EditableText` has its own `onDragOver` handler that calculates the precise character index from mouse coordinates to show a caret indicator for splitting text.
- **Logic**: The `onDropPart` handler (`handleDrop` in `App.tsx`) receives drop information and performs the state update.

### 3.4. Special Case: The Imperative `CanvasRef`

The `AIInstructionBlock` is unique because it contains a nested `Canvas` instance. This creates a challenge: how can the parent `Canvas` programmatically focus an element *inside* the nested canvas? This is necessary, for example, after the user presses "Enter" on the `AIInstructionBlock` to begin editing its content.

- **The Solution (Imperative Handle with a Shared `ref` Type)**: To solve this, both `Canvas` and `AIInstructionBlock` use `useImperativeHandle` to expose a custom `focus` method.
- **`CanvasRef`**: A unified `ref` type is defined in `types.ts` (`{ focus: (...) => void }`). This serves as the contract for any component that needs to expose this imperative behavior.
- **The Flow**:
  1. User presses `Enter` on the AI block.
  2. `App.tsx`'s `handleEnterAIBlock` is called.
  3. This triggers a `focusRequest` targeting the first element *inside* the nested canvas.
  4. This focus request is passed down to the nested `Canvas`, which then handles the final focusing logic.

This is a deliberate and advanced React pattern. It breaks the "props-down" data flow for a very specific reason: managing focus and selection across complex, nested component boundaries, which is not possible with props alone.

---

## 4. Focus & Selection Model

Two state variables in `App.tsx` control focus:

- **`selectedPartId: string | null`**:
  - Represents the item that is "selected," visually indicated by a colored ring. This is passed down as the `isSelected` prop.
  - When an item is selected, `Canvas.tsx`'s `useEffect` hook calls `.focus()` on its DOM element.

- **`focusRequest: FocusRequest | null`**:
  - An object for imperative, one-time focus actions where simple selection is not enough (e.g., placing the cursor at a specific position after splitting a line).
  - It is consumed by a `useEffect` hook in `Canvas.tsx`.

---

## 5. Serialization & Persistence

The application design remains ideal for backend integration.

- The `canvasParts` (`CanvasElementData[][]`) data structure is JSON-serializable.
- `serializeCanvas` and `deserializeCanvas` in `App.tsx` are placeholders for this process. To save, one would call `serializeCanvas(canvasParts)` and store the resulting string. To load, one would fetch the string and use `deserializeCanvas(jsonString)` to hydrate the initial state.