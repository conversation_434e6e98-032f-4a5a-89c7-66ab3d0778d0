{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_a71539c9.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geist_a71539c9-module__T19VSG__className\",\n  \"variable\": \"geist_a71539c9-module__T19VSG__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 11, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_a71539c9.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Geist%22,%22arguments%22:[{%22variable%22:%22--font-geist-sans%22,%22subsets%22:[%22latin%22]}],%22variableName%22:%22geistSans%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist', 'Geist Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,gKAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,gKAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,gKAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_mono_8d43a2aa.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geist_mono_8d43a2aa-module__8Li5zG__className\",\n  \"variable\": \"geist_mono_8d43a2aa-module__8Li5zG__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_mono_8d43a2aa.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Geist_Mono%22,%22arguments%22:[{%22variable%22:%22--font-geist-mono%22,%22subsets%22:[%22latin%22]}],%22variableName%22:%22geistMono%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist Mono', 'Geist Mono Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qKAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qKAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qKAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///T:/Development/TranscriptionModule/transcription-demo/src/components/recording-widget.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const RecordingWidget = registerClientReference(\n    function() { throw new Error(\"Attempted to call RecordingWidget() from the server but RecordingWidget is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/recording-widget.tsx <module evaluation>\",\n    \"RecordingWidget\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,kBAAkB,IAAA,wQAAuB,EAClD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,qEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": ["file:///T:/Development/TranscriptionModule/transcription-demo/src/components/recording-widget.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const RecordingWidget = registerClientReference(\n    function() { throw new Error(\"Attempted to call RecordingWidget() from the server but RecordingWidget is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/recording-widget.tsx\",\n    \"RecordingWidget\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,kBAAkB,IAAA,wQAAuB,EAClD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,iDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 88, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 96, "column": 0}, "map": {"version": 3, "sources": ["file:///T:/Development/TranscriptionModule/transcription-demo/src/app/layout.tsx"], "sourcesContent": ["import type { <PERSON>ada<PERSON> } from \"next\";\nimport { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from \"next/font/google\";\nimport \"./globals.css\";\nimport { RecordingWidget } from \"@/components/recording-widget\";\n\nconst geistSans = Geist({\n  variable: \"--font-geist-sans\",\n  subsets: [\"latin\"],\n});\n\nconst geistMono = Geist_Mono({\n  variable: \"--font-geist-mono\",\n  subsets: [\"latin\"],\n});\n\nexport const metadata: Metadata = {\n  title: \"Create Next App\",\n  description: \"Generated by create next app\",\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"en\">\n      <body\n        className={`${geistSans.variable} ${geistMono.variable} antialiased`}\n      >\n        {children}\n        <RecordingWidget />\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;AAGA;;;;;;AAYO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;kBACT,cAAA,8OAAC;YACC,WAAW,GAAG,oJAAS,CAAC,QAAQ,CAAC,CAAC,EAAE,yJAAS,CAAC,QAAQ,CAAC,YAAY,CAAC;;gBAEnE;8BACD,8OAAC,4JAAe;;;;;;;;;;;;;;;;AAIxB", "debugId": null}}, {"offset": {"line": 143, "column": 0}, "map": {"version": 3, "sources": ["file:///T:/Development/TranscriptionModule/transcription-demo/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-rsc']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}]}