

export enum BlockType {
  Text = 'text',
  Variable = 'variable',
  AIInstruction = 'ai-instruction',
  Category = 'category',
}

// --- IMPERATIVE REF HANDLE ---

// A unified type for imperative handles, used by both Canvas and AIInstructionBlock
// to expose a focus method programmatically.
export interface CanvasRef {
    focus: (position?: 'start' | 'end') => void;
}


// --- UNIFIED DATA & PROPS MODEL (DISCRIMINATED UNION) ---

// Base for all elements, containing the universal ID and the single discriminant.
interface BaseElement {
    id: string;
    blockType: BlockType;
}

// Specific data shapes for each type of element on the canvas.
export interface TextElement extends BaseElement {
  blockType: BlockType.Text;
  content: string;
}

export interface VariableElement extends BaseElement {
    blockType: BlockType.Variable;
    name: string;
}

export interface AIInstructionElement extends BaseElement {
    blockType: BlockType.AIInstruction;
    parts: CanvasElementData[][];
}

export interface CategoryElement extends BaseElement {
    blockType: BlockType.Category;
    category: string;
    options: string[];
    selected: string[];
}

// The Discriminated Union: A single type that can represent any element, discriminated by `blockType`.
export type CanvasElementData = TextElement | VariableElement | AIInstructionElement | CategoryElement;

// --- COMPONENT-SPECIFIC PROPS ---

export interface FocusRequest {
    partId: string;
    position: 'start' | 'end' | number;
    selectAll?: boolean;
}

export type NavDirection = 'up' | 'down' | 'left' | 'right';
export type DropMode = 'split' | 'insertBefore' | 'insertAfter';

// Base props for all canvas components, containing only shared state and event handlers.
export interface BaseComponentProps {
    isSelected: boolean;
    focusRequest?: FocusRequest | null;
    selectedPartId?: string | null;
    
    // Event Handlers
    onUpdatePart: (partId: string, newProps: Partial<CanvasElementData>) => void;
    onDropPart: (payload: { draggedData: any, targetPartId: string, lineIndex: number, index?: number, mode: DropMode }) => void;
    onDeletePart: (partId: string) => void;
    onSplitPart: (partId: string, splitIndex: number, currentContent: string) => void;
    onMergePart: (partId: string) => void;
    onSelectPart: (partId: string) => void;
    onEnterAIBlock: (partId: string) => void;
    onNavigate: (partId: string, direction: NavDirection) => void;
    onDragStart: (e: React.DragEvent, part: CanvasElementData) => void;
    
    // For nested canvases
    onNavigateBoundary?: (direction: NavDirection) => void;

    // For drag-over indicators
    showIndicator: (rect: DOMRect, type?: 'caret' | 'line') => void;
    hideIndicator: () => void;
}

// Final component props are an intersection of the data model and the shared handlers,
// plus the modern `ref` prop.
export type EditableTextProps = BaseComponentProps & TextElement & { ref?: React.Ref<HTMLSpanElement> };
export type VariableBlockProps = BaseComponentProps & VariableElement & { ref?: React.Ref<HTMLSpanElement> };
export type AIInstructionBlockProps = BaseComponentProps & AIInstructionElement & { ref?: React.Ref<CanvasRef> };
export type CategoryBlockProps = BaseComponentProps & CategoryElement & { ref?: React.Ref<HTMLSpanElement> };