import React, { useState, useCallback, useEffect } from 'react';
import { CanvasElementData, BlockType, TextElement, FocusRequest, AIInstructionElement } from './types';
import { INITIAL_CLINICAL_LETTER_DATA } from './initialData';
import Canvas from './components/Canvas';
import Sidebar from './components/Toolbar';
import RenderedView from './components/RenderedView';
import { LogoIcon } from './components/icons/Icons';

// --- SERIALIZATION PLACEHOLDERS ---

/**
 * Serializes the canvas content into a JSON string for database storage.
 * @param parts The canvas content state.
 * @returns A JSON string representation of the canvas.
 */
const serializeCanvas = (parts: CanvasElementData[][]): string => {
  // In a real application, you might add more validation or transformation logic.
  return JSON.stringify(parts, null, 2);
};

/**
 * Deserializes a JSON string back into canvas content.
 * @param jsonString The JSON string from the database.
 * @returns The canvas content structure.
 */
const deserializeCanvas = (jsonString: string): CanvasElementData[][] => {
  // In a real application, you would add robust error handling and data validation (e.g., with a library like Zod).
  try {
    const parts = JSON.parse(jsonString);
    // TODO: Add schema validation to ensure the data structure is correct.
    return parts;
  } catch (error) {
    console.error("Failed to deserialize canvas state:", error);
    // Return a default state on failure to prevent the app from crashing.
    return [[{ id: `text-fallback-${Date.now()}`, blockType: BlockType.Text, content: 'Error loading content.' }]];
  }
};


// --- RECURSIVE UTILITY ---

const recursivelyModifyParts = (
    parts: CanvasElementData[][],
    logic: (
        currentParts: CanvasElementData[][],
        recursiveCall: (nestedParts: CanvasElementData[][]) => CanvasElementData[][] | null
    ) => CanvasElementData[][] | null
): CanvasElementData[][] => {
    const recursiveCall = (nestedParts: CanvasElementData[][]): CanvasElementData[][] | null => {
        return recursivelyModifyParts(nestedParts, logic);
    };

    const modified = logic(parts, recursiveCall);
    if (modified) {
        return modified;
    }

    // If no modification at this level, recurse into children
    for (const line of parts) {
        for (const part of line) {
            if (part.blockType === BlockType.AIInstruction) {
                const nestedParts = (part as AIInstructionElement).parts;
                const modifiedNested = recursivelyModifyParts(nestedParts, logic);
                if (modifiedNested !== nestedParts) { // Check for change
                    (part as AIInstructionElement).parts = modifiedNested;
                    return parts; // Return the whole structure with the nested change
                }
            }
        }
    }
    return parts; // No change found anywhere
};

const App: React.FC = () => {
  const [canvasParts, setCanvasParts] = useState<CanvasElementData[][]>(INITIAL_CLINICAL_LETTER_DATA);

  const [focusRequest, setFocusRequest] = useState<FocusRequest | null>(null);
  const [selectedPartId, setSelectedPartId] = useState<string | null>(null);
  
  const performModification = (modifier: (draft: CanvasElementData[][]) => CanvasElementData[][]) => {
      setCanvasParts(currentParts => {
          const newParts = modifier(JSON.parse(JSON.stringify(currentParts)));
          
          const cleanup = (parts: CanvasElementData[][]): CanvasElementData[][] => {
              const mergedLines = parts.map(line => {
                  if (line.length === 0) return [];
                  const mergedLine: CanvasElementData[] = [];
                  let currentTextPart: TextElement | null = null;

                  for (const part of line) {
                      if (part.blockType === BlockType.Text) {
                          if (currentTextPart) {
                              currentTextPart.content += (part as TextElement).content;
                          } else {
                              currentTextPart = { ...(part as TextElement) }; 
                          }
                      } else { // Block part
                          if (currentTextPart) {
                              mergedLine.push(currentTextPart);
                              currentTextPart = null;
                          }
                          if (part.blockType === BlockType.AIInstruction) {
                              (part as AIInstructionElement).parts = cleanup((part as AIInstructionElement).parts);
                          }
                          mergedLine.push(part);
                      }
                  }
                  if (currentTextPart) {
                      mergedLine.push(currentTextPart);
                  }
                  return mergedLine;
              });

              const finalLines = mergedLines.map(line => {
                  return line.filter(part => {
                      if (part.blockType === BlockType.Text) {
                          return (part as TextElement).content !== '' || line.length === 1;
                      }
                      return true; // Keep all other block types
                  });
              }).filter(line => line.length > 0 || mergedLines.length === 1); 
              
              if (finalLines.length === 0) {
                  return [[{ id: `text-fallback-${Date.now()}`, blockType: BlockType.Text, content: '' }]];
              }
              
              return finalLines;
          };

          return cleanup(newParts);
      });
  };

  const updatePart = useCallback((partId: string, newProps: Partial<CanvasElementData>) => {
      performModification(draft => recursivelyModifyParts(draft, (currentParts) => {
          for (const line of currentParts) {
              const partIndex = line.findIndex(p => p.id === partId);
              if (partIndex !== -1) {
                  const part = line[partIndex];
                  line[partIndex] = { ...part, ...newProps } as CanvasElementData;
                  return currentParts;
              }
          }
          return null; // Part not found at this level
      }));
  }, []);

  const handleDrop = useCallback(({ draggedData, targetPartId, lineIndex: dropLineIndex, index: dropCharIndex, mode }: { draggedData: any, targetPartId: string, lineIndex: number, index?: number, mode: 'split' | 'insertBefore' | 'insertAfter' }) => {
      // FIX: If a block is dragged onto itself, do nothing to prevent it from being deleted.
      if (draggedData.movePartId === targetPartId) {
        return;
      }

      performModification(draft => {
          let partToInsert: CanvasElementData;
          let partRemoved = false;

          // Remove the part from its original location if it's a move operation
          const cleanDraft = recursivelyModifyParts(JSON.parse(JSON.stringify(draft)), (currentParts) => {
              if (draggedData.movePartId && !partRemoved) {
                  for (let i = 0; i < currentParts.length; i++) {
                      const j = currentParts[i].findIndex(p => p.id === draggedData.movePartId);
                      if (j !== -1) {
                          [partToInsert] = currentParts[i].splice(j, 1);
                          partRemoved = true;
                          return currentParts;
                      }
                  }
              }
              return null;
          });

          if (draggedData.movePartId && !partRemoved) return draft; // Move source not found
          if (!draggedData.movePartId) {
             partToInsert = { ...draggedData.newPart, id: `${draggedData.newPart.blockType}-${Date.now()}-${Math.random()}` };
          }
          
          return recursivelyModifyParts(cleanDraft, (currentParts) => {
              for (let i = 0; i < currentParts.length; i++) {
                  const line = currentParts[i];
                  const partIndex = line.findIndex(p => p.id === targetPartId);
                  
                  if (i === dropLineIndex && partIndex !== -1) { // Dropped on a specific part or line
                      if (mode === 'insertBefore') {
                          // Horizontal indicator on top of a line
                          currentParts.splice(i, 0, [partToInsert]);
                      } else if (mode === 'insertAfter') {
                          // Horizontal indicator below a line
                          currentParts.splice(i + 1, 0, [partToInsert]);
                      } else if (mode === 'split' && typeof dropCharIndex === 'number') {
                           // Caret indicator inside text
                          const targetPart = line[partIndex] as TextElement;
                          const beforeContent = targetPart.content.substring(0, dropCharIndex);
                          const afterContent = targetPart.content.substring(dropCharIndex);
                          
                          targetPart.content = beforeContent;
                          const afterPart: TextElement = { id: `text-${Date.now()}`, blockType: BlockType.Text, content: afterContent };
                          line.splice(partIndex + 1, 0, partToInsert, afterPart);
                      }
                      return currentParts;
                  }
              }
              return null; // Target not found at this level
          });
      });
  }, []);

  const handleDeletePart = useCallback((partId: string) => {
      performModification(draft => recursivelyModifyParts(draft, (currentParts) => {
          for (const line of currentParts) {
              const partIndex = line.findIndex(p => p.id === partId);
              if (partIndex !== -1) {
                  line.splice(partIndex, 1);
                  return currentParts; // Found and deleted
              }
          }
          return null; // Not found at this level
      }));
  }, []);

  const handleSplitPart = useCallback((partId: string, splitIndex: number, currentContent: string) => {
      performModification(draft => recursivelyModifyParts(draft, (currentParts) => {
          for (let i = 0; i < currentParts.length; i++) {
              const line = currentParts[i];
              const partIndex = line.findIndex(p => p.id === partId);
              if (partIndex !== -1 && line[partIndex].blockType === BlockType.Text) {
                  const partToSplit = line[partIndex] as TextElement;
                  const beforeContent = currentContent.substring(0, splitIndex);
                  const afterContent = currentContent.substring(splitIndex);

                  partToSplit.content = beforeContent;

                  const remainingParts = line.splice(partIndex + 1);
                  const newPart: TextElement = { id: `text-${Date.now()}`, blockType: BlockType.Text, content: afterContent };
                  const newLine = [newPart, ...remainingParts];
                  
                  currentParts.splice(i + 1, 0, newLine);

                  setFocusRequest({ partId: newPart.id, position: 'start' });
                  return currentParts;
              }
          }
          return null;
      }));
  }, []);

  const handleMergePart = useCallback((partId: string) => {
      performModification(draft => recursivelyModifyParts(draft, (currentParts) => {
          for (let i = 0; i < currentParts.length; i++) {
              // Check if the partId is the first part of a line, and it's not the very first line of the current canvas.
              if (i > 0 && currentParts[i][0]?.id === partId) {
                  const line = currentParts[i];
                  const prevLine = currentParts[i-1];
                  
                  const focusPart = prevLine[prevLine.length - 1];
                  let focusPartId: string | null = null;
                  let focusOffset: number = 0;

                  if (focusPart && focusPart.blockType === BlockType.Text) {
                      focusPartId = focusPart.id;
                      focusOffset = (focusPart as TextElement).content.length;
                  }
                  
                  currentParts[i-1] = [...prevLine, ...line];
                  currentParts.splice(i, 1);
                  
                  if (focusPartId) {
                     setFocusRequest({ partId: focusPartId, position: focusOffset });
                  }
                  return currentParts;
              }
          }
          return null;
      }));
  }, []);

  const handleSelectPart = useCallback((partId: string) => {
    setSelectedPartId(partId);
    setFocusRequest(null); // Clear focus requests when explicitly selecting a block
  }, []);

  const handleEnterAIBlock = useCallback((partId: string) => {
      const findBlock = (parts: CanvasElementData[][]): AIInstructionElement | null => {
          for (const line of parts) {
              for (const part of line) {
                  if (part.id === partId && part.blockType === BlockType.AIInstruction) {
                      return part as AIInstructionElement;
                  }
                  if (part.blockType === BlockType.AIInstruction) {
                      const found = findBlock((part as AIInstructionElement).parts);
                      if (found) return found;
                  }
              }
          }
          return null;
      }
      const block = findBlock(canvasParts);
      if (block && block.parts[0]?.[0]) {
          const firstChildPartId = block.parts[0][0].id;
          setFocusRequest({ partId: firstChildPartId, position: 'end', selectAll: true });
          setSelectedPartId(null);
      }
  }, [canvasParts]);

  return (
    <div className="min-h-screen bg-gray-100 flex flex-col items-center p-4 sm:p-8 font-sans">
      <header className="w-full max-w-6xl mb-6 flex items-center justify-center">
        <LogoIcon className="h-10 w-10 text-indigo-600 mr-3"/>
        <h1 className="text-3xl font-bold text-gray-800 tracking-tight">
          AI Inline Component Canvas
        </h1>
      </header>
      <div className="w-full max-w-6xl flex-grow flex flex-row items-start space-x-6">
        <Sidebar />
        <main className="flex-grow bg-white rounded-lg shadow-lg border border-gray-200">
            <Canvas 
                parts={canvasParts} 
                onUpdatePart={updatePart} 
                onDropPart={handleDrop}
                onDeletePart={handleDeletePart}
                onSplitPart={handleSplitPart}
                onMergePart={handleMergePart}
                focusRequest={focusRequest}
                selectedPartId={selectedPartId}
                onSelectPart={handleSelectPart}
                onEnterAIBlock={handleEnterAIBlock}
            />
        </main>
      </div>

      <RenderedView parts={canvasParts} />
      
      <footer className="mt-8 text-center text-gray-500 text-sm">
        <p>A technical demo for embedding interactive components within a text-based editor.</p>
        <p>Built with React, TypeScript, and Tailwind CSS.</p>
      </footer>
    </div>
  );
};

export default App;