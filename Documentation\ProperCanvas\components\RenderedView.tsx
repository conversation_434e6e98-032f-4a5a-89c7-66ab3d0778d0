import React from 'react';
import { CanvasElementData, BlockType, AIInstructionElement, VariableElement, CategoryElement, TextElement } from '../types';

interface RenderedViewProps {
    parts: CanvasElementData[][];
}

const renderPartsToString = (parts: CanvasElementData[][]): string => {
    return parts.map(line => 
        line.map(part => {
            switch (part.blockType) {
                case BlockType.Text:
                    return (part as TextElement).content;
                case BlockType.Variable:
                    return `{{${(part as VariableElement).name}}}`;
                case BlockType.AIInstruction:
                    const aiPart = part as AIInstructionElement;
                    // Recursively render the parts inside the AI instruction
                    const innerContent = renderPartsToString(aiPart.parts).replace(/\n/g, ' '); // remove newlines from inner content
                    return `%[${innerContent}]`;
                case BlockType.Category:
                    const categoryPart = part as CategoryElement;
                    if (categoryPart.selected.length === 0) {
                        return `$[${categoryPart.category}]`;
                    }
                    return `$[${categoryPart.category}: ${categoryPart.selected.join(', ')}]`;
                default:
                    return '';
            }
        }).join('')
    ).join('\n');
};


const RenderedView: React.FC<RenderedViewProps> = ({ parts }) => {
    const renderedText = renderPartsToString(parts);

    return (
        <div className="w-full max-w-6xl mt-8">
            <h2 className="text-2xl font-bold text-gray-800 tracking-tight mb-4">
                Rendered Template (for LLM)
            </h2>
            <pre className="bg-gray-800 text-white p-6 rounded-lg shadow-lg border border-gray-700 text-sm whitespace-pre-wrap break-words">
                {renderedText}
            </pre>
        </div>
    );
};

export default RenderedView;
