
import React, { useState, useRef, useEffect, useCallback } from 'react';
import { CategoryIcon, ChevronDownIcon, SearchIcon, DeleteIcon } from '../icons/Icons';
import { NavDirection, CategoryBlockProps } from '../../types';

const CategoryBlock = (props: CategoryBlockProps) => {
  const {
    id,
    blockType,
    category,
    options,
    selected,
    isSelected,
    onUpdatePart, 
    onDeletePart,
    onSelectPart,
    onNavigate,
    onDragStart,
    ref,
  } = props;
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const componentRef = useRef<HTMLSpanElement | null>(null);

  // This callback ref pattern is a robust way to handle both a local ref
  // (for the click-outside handler) and a forwarded ref from props.
  const setRefs = useCallback((node: HTMLSpanElement | null) => {
    componentRef.current = node;
    if (typeof ref === 'function') {
      ref(node);
    } else if (ref) {
      (ref as React.MutableRefObject<HTMLSpanElement | null>).current = node;
    }
  }, [ref]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (componentRef.current && !componentRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);
  
  const handleSelect = (option: string) => {
    const newSelected = selected.includes(option)
      ? selected.filter((item: string) => item !== option)
      : [...selected, option];
    onUpdatePart(id, { selected: newSelected });
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    // FIX: Only handle key events if they originate from the block's main container,
    // not from children like the search input. This prevents the block from deleting
    // itself when the user presses Backspace inside the search field.
    if (e.target !== e.currentTarget) {
        return;
    }

     if (e.key === 'Enter' || e.key === ' ') {
        e.preventDefault();
        e.stopPropagation();
        setIsOpen(!isOpen);
    } else if (e.key === 'Escape' && isOpen) {
        e.preventDefault();
        e.stopPropagation();
        setIsOpen(false);
    } else if (['ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown'].includes(e.key)) {
        e.preventDefault();
        onNavigate(id, e.key.replace('Arrow', '').toLowerCase() as NavDirection);
    } else if (e.key === 'Backspace' || e.key === 'Delete') {
        e.preventDefault();
        onDeletePart(id);
    }
  }

  const filteredOptions = options.filter((option: string) => 
    option.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const buttonText = selected.length > 0 
    ? `${category} (${selected.length}/${options.length} selected)` 
    : category;

  const selectedClass = isSelected ? 'ring-2 ring-purple-500 ring-offset-1' : '';

  return (
    <span 
        ref={setRefs}
        className={`group relative inline-block text-left rounded-md focus:outline-none ${selectedClass}`}
        tabIndex={-1}
        onFocus={() => onSelectPart(id)}
        onKeyDown={handleKeyDown}
        draggable={true}
        onDragStart={(e) => onDragStart(e, { id, blockType, category, options, selected })}
        data-canvas-element="true"
        data-part-id={id}
    >
      <span className="inline-flex items-center">
        <button
          type="button"
          onClick={() => setIsOpen(!isOpen)}
          className="inline-flex items-center justify-center bg-purple-100 text-purple-800 text-base font-medium pl-3 pr-2 py-1 rounded-md hover:bg-purple-200 focus:outline-none transition-colors duration-150 cursor-pointer"
        >
          <CategoryIcon className="w-4 h-4 mr-2" />
          {buttonText}
          <ChevronDownIcon className={`w-5 h-5 ml-2 -mr-1 transition-transform duration-200 ${isOpen ? 'transform rotate-180' : ''}`} />
        </button>
      </span>

       <button
        onClick={() => onDeletePart(id)}
        className="absolute top-0 right-0 -mt-1 -mr-1 p-0.5 bg-gray-500 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-150 focus:opacity-100 focus:outline-none z-20"
        aria-label={`Delete ${category}`}
      >
        <DeleteIcon className="w-3 h-3" />
      </button>

      {isOpen && (
        <div className="origin-top-right absolute left-0 mt-2 w-72 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-10">
          <div className="p-2">
            <div className="relative">
                <span className="absolute inset-y-0 left-0 flex items-center pl-3">
                    <SearchIcon className="w-5 h-5 text-gray-400" />
                </span>
                <input
                    type="text"
                    placeholder="Search findings..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    autoFocus
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                />
            </div>
          </div>
          <div className="py-1 max-h-60 overflow-y-auto" role="menu" aria-orientation="vertical">
            {filteredOptions.map((option: string) => (
              <label key={option} className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 cursor-pointer">
                <input
                  type="checkbox"
                  checked={selected.includes(option)}
                  onChange={() => handleSelect(option)}
                  className="h-4 w-4 rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                />
                <span className="ml-3">{option}</span>
              </label>
            ))}
          </div>
        </div>
      )}
    </span>
  );
};

export default CategoryBlock;