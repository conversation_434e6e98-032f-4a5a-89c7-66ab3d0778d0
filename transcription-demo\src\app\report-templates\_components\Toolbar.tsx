"use client";

import { Bold, Italic, Underline, List, ListOrdered, AlignLeft, AlignCenter, AlignRight } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

export function Toolbar() {
  function exec(command: string, value?: string) {
    try {
      document.execCommand(command, false, value);
    } catch {}
  }

  return (
    <div className="flex flex-wrap items-center gap-2 p-2 rounded-lg border bg-muted/30">
      <Select defaultValue="Arial" onValueChange={(v) => exec("fontName", v)}>
        <SelectTrigger className="h-8 w-[160px]"><SelectValue placeholder="Font"/></SelectTrigger>
        <SelectContent>
          <SelectItem value="Arial">Arial</SelectItem>
          <SelectItem value="Georgia">Georgia</SelectItem>
          <SelectItem value="Verdana">Verdana</SelectItem>
        </SelectContent>
      </Select>
      <Select defaultValue="3" onValueChange={(v) => exec("fontSize", v)}>
        <SelectTrigger className="h-8 w-[140px]"><SelectValue placeholder="Size"/></SelectTrigger>
        <SelectContent>
          <SelectItem value="2">Small</SelectItem>
          <SelectItem value="3">Normal</SelectItem>
          <SelectItem value="5">Heading</SelectItem>
          <SelectItem value="6">Title</SelectItem>
        </SelectContent>
      </Select>

      <div className="w-px h-6 bg-border mx-1"/>
      <Button size="icon" variant="ghost" className="h-8 w-8" onMouseDown={(e) => e.preventDefault()} onClick={() => exec("bold")}>
        <Bold className="h-4 w-4" />
      </Button>
      <Button size="icon" variant="ghost" className="h-8 w-8" onMouseDown={(e) => e.preventDefault()} onClick={() => exec("italic")}>
        <Italic className="h-4 w-4" />
      </Button>
      <Button size="icon" variant="ghost" className="h-8 w-8" onMouseDown={(e) => e.preventDefault()} onClick={() => exec("underline")}>
        <Underline className="h-4 w-4" />
      </Button>

      <div className="w-px h-6 bg-border mx-1"/>
      <Button size="icon" variant="ghost" className="h-8 w-8" onMouseDown={(e) => e.preventDefault()} onClick={() => exec("justifyLeft")}>
        <AlignLeft className="h-4 w-4" />
      </Button>
      <Button size="icon" variant="ghost" className="h-8 w-8" onMouseDown={(e) => e.preventDefault()} onClick={() => exec("justifyCenter")}>
        <AlignCenter className="h-4 w-4" />
      </Button>
      <Button size="icon" variant="ghost" className="h-8 w-8" onMouseDown={(e) => e.preventDefault()} onClick={() => exec("justifyRight")}>
        <AlignRight className="h-4 w-4" />
      </Button>

      <div className="w-px h-6 bg-border mx-1"/>
      <Button size="icon" variant="ghost" className="h-8 w-8" onMouseDown={(e) => e.preventDefault()} onClick={() => { document.execCommand("formatBlock", false, "div"); exec("insertUnorderedList"); }}>
        <List className="h-4 w-4" />
      </Button>
      <Button size="icon" variant="ghost" className="h-8 w-8" onMouseDown={(e) => e.preventDefault()} onClick={() => { document.execCommand("formatBlock", false, "div"); exec("insertOrderedList"); }}>
        <ListOrdered className="h-4 w-4" />
      </Button>
    </div>
  );
}

