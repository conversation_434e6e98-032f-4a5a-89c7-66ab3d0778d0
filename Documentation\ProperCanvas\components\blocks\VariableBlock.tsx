
import React from 'react';
import { VariableIcon, DeleteIcon } from '../icons/Icons';
import { NavDirection, VariableBlockProps } from '../../types';

const VariableBlock = (props: VariableBlockProps) => {
  const { 
    id,
    blockType,
    name,
    isSelected,
    onDeletePart, 
    onSelectPart,
    onNavigate,
    onDragStart,
    ref,
  } = props;

  const handleKeyDown = (e: React.KeyboardEvent) => {
    switch (e.key) {
      case 'ArrowLeft':
      case 'ArrowRight':
      case 'ArrowUp':
      case 'ArrowDown':
        e.preventDefault();
        onNavigate(id, e.key.replace('Arrow', '').toLowerCase() as NavDirection);
        break;
      case 'Backspace':
      case 'Delete':
        e.preventDefault();
        onDeletePart(id);
        break;
    }
  };

  const selectedClass = isSelected ? 'ring-2 ring-blue-500 ring-offset-1' : '';

  return (
    <span 
      ref={ref}
      className={`group relative inline-flex items-center bg-blue-100 text-blue-800 text-base font-medium px-3 py-1 rounded-md cursor-grab active:cursor-grabbing transition-colors duration-150 focus:outline-none ${selectedClass}`}
      tabIndex={-1}
      onFocus={() => onSelectPart(id)}
      onKeyDown={handleKeyDown}
      draggable={true}
      onDragStart={(e) => onDragStart(e, { id, blockType, name })}
      data-canvas-element="true"
      data-part-id={id}
    >
      <VariableIcon className="w-4 h-4 mr-2" />
      {name}
      <button
        onClick={() => onDeletePart(id)}
        className="absolute top-0 right-0 -mt-1 -mr-1 p-0.5 bg-gray-500 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-150 focus:opacity-100 focus:outline-none"
        aria-label={`Delete ${name}`}
      >
        <DeleteIcon className="w-3 h-3" />
      </button>
    </span>
  );
};

export default VariableBlock;