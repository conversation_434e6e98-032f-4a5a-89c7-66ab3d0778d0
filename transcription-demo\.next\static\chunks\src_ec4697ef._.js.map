{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///T:/Development/TranscriptionModule/transcription-demo/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Tabs({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Root>) {\n  return (\n    <TabsPrimitive.Root\n      data-slot=\"tabs\"\n      className={cn(\"flex flex-col gap-2\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TabsList({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.List>) {\n  return (\n    <TabsPrimitive.List\n      data-slot=\"tabs-list\"\n      className={cn(\n        \"bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsTrigger({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\n  return (\n    <TabsPrimitive.Trigger\n      data-slot=\"tabs-trigger\"\n      className={cn(\n        \"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Content>) {\n  return (\n    <TabsPrimitive.Content\n      data-slot=\"tabs-content\"\n      className={cn(\"flex-1 outline-none\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,KAAK,KAGoC;QAHpC,EACZ,SAAS,EACT,GAAG,OAC6C,GAHpC;IAIZ,qBACE,6LAAC,2KAAkB;QACjB,aAAU;QACV,WAAW,IAAA,4HAAE,EAAC,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,SAAS,KAGgC;QAHhC,EAChB,SAAS,EACT,GAAG,OAC6C,GAHhC;IAIhB,qBACE,6LAAC,2KAAkB;QACjB,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,uGACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,YAAY,KAGgC;QAHhC,EACnB,SAAS,EACT,GAAG,OACgD,GAHhC;IAInB,qBACE,6LAAC,8KAAqB;QACpB,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,mqBACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,YAAY,KAGgC;QAHhC,EACnB,SAAS,EACT,GAAG,OACgD,GAHhC;IAInB,qBACE,6LAAC,8KAAqB;QACpB,aAAU;QACV,WAAW,IAAA,4HAAE,EAAC,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 86, "column": 0}, "map": {"version": 3, "sources": ["file:///T:/Development/TranscriptionModule/transcription-demo/src/components/workspace-nav.tsx"], "sourcesContent": ["\"use client\";\n\nimport Link from \"next/link\";\nimport { usePathname } from \"next/navigation\";\nimport { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from \"@/components/ui/tabs\";\n\nconst items = [\n  { href: \"/recording\", label: \"Recording\" },\n  { href: \"/ai/dashboard\", label: \"AI Dashboard\" },\n  { href: \"/report-templates\", label: \"Report Templates\" },\n  { href: \"/templates/editor\", label: \"Template Editor\" },\n];\n\nexport function WorkspaceNav() {\n  const pathname = usePathname();\n  const value = items.find((i) => pathname.startsWith(i.href))?.href ?? \"/recording\";\n  return (\n    <Tabs value={value} className=\"w-full\">\n      <TabsList className=\"mb-4\">\n        {items.map((i) => (\n          <TabsTrigger key={i.href} value={i.href} asChild>\n            <Link href={i.href}>{i.label}</Link>\n          </TabsTrigger>\n        ))}\n      </TabsList>\n    </Tabs>\n  );\n}\n\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;;;AAJA;;;;AAMA,MAAM,QAAQ;IACZ;QAAE,MAAM;QAAc,OAAO;IAAY;IACzC;QAAE,MAAM;QAAiB,OAAO;IAAe;IAC/C;QAAE,MAAM;QAAqB,OAAO;IAAmB;IACvD;QAAE,MAAM;QAAqB,OAAO;IAAkB;CACvD;AAEM,SAAS;QAEA;;IADd,MAAM,WAAW,IAAA,oJAAW;QACd;IAAd,MAAM,QAAQ,CAAA,oBAAA,cAAA,MAAM,IAAI,CAAC,CAAC,IAAM,SAAS,UAAU,CAAC,EAAE,IAAI,gBAA5C,kCAAA,YAAgD,IAAI,cAApD,8BAAA,mBAAwD;IACtE,qBACE,6LAAC,2IAAI;QAAC,OAAO;QAAO,WAAU;kBAC5B,cAAA,6LAAC,+IAAQ;YAAC,WAAU;sBACjB,MAAM,GAAG,CAAC,CAAC,kBACV,6LAAC,kJAAW;oBAAc,OAAO,EAAE,IAAI;oBAAE,OAAO;8BAC9C,cAAA,6LAAC,0KAAI;wBAAC,MAAM,EAAE,IAAI;kCAAG,EAAE,KAAK;;;;;;mBADZ,EAAE,IAAI;;;;;;;;;;;;;;;AAOlC;GAdgB;;QACG,oJAAW;;;KADd", "debugId": null}}, {"offset": {"line": 171, "column": 0}, "map": {"version": 3, "sources": ["file:///T:/Development/TranscriptionModule/transcription-demo/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;;AAEA;;;AAEA,SAAS,MAAM,KAA4D;QAA5D,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC,GAA5D;IACb,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 203, "column": 0}, "map": {"version": 3, "sources": ["file:///T:/Development/TranscriptionModule/transcription-demo/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,KAEoC;QAFpC,EACd,GAAG,OAC+C,GAFpC;IAGd,qBAAO,6LAAC,6KAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,YAAY,KAEgC;QAFhC,EACnB,GAAG,OACgD,GAFhC;IAGnB,qBAAO,6LAAC,8KAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,YAAY,KAEgC;QAFhC,EACnB,GAAG,OACgD,GAFhC;IAGnB,qBAAO,6LAAC,8KAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,KAOtB;QAPsB,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ,GAPsB;IAQrB,qBACE,6LAAC,gLAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,IAAA,4HAAE,EACX,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,6KAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,8OAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MAxBS;AA0BT,SAAS,cAAc,KAKgC;QALhC,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD,GALhC;IAMrB,qBACE,6LAAC,+KAAsB;kBACrB,cAAA,6LAAC,gLAAuB;YACtB,aAAU;YACV,WAAW,IAAA,4HAAE,EACX,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,iLAAwB;oBACvB,WAAW,IAAA,4HAAE,EACX,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,KAGgC;QAHhC,EACnB,SAAS,EACT,GAAG,OACgD,GAHhC;IAInB,qBACE,6LAAC,8KAAqB;QACpB,aAAU;QACV,WAAW,IAAA,4HAAE,EAAC,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,WAAW,KAIgC;QAJhC,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C,GAJhC;IAKlB,qBACE,6LAAC,6KAAoB;QACnB,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,6aACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,sLAA6B;8BAC5B,cAAA,6LAAC,wNAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,6LAAC,iLAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,KAGgC;QAHhC,EACvB,SAAS,EACT,GAAG,OACoD,GAHhC;IAIvB,qBACE,6LAAC,kLAAyB;QACxB,aAAU;QACV,WAAW,IAAA,4HAAE,EAAC,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,KAGgC;QAHhC,EAC5B,SAAS,EACT,GAAG,OACyD,GAHhC;IAI5B,qBACE,6LAAC,uLAA8B;QAC7B,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,wOAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAhBS;AAkBT,SAAS,uBAAuB,KAGgC;QAHhC,EAC9B,SAAS,EACT,GAAG,OAC2D,GAHhC;IAI9B,qBACE,6LAAC,yLAAgC;QAC/B,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,8OAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAhBS", "debugId": null}}, {"offset": {"line": 470, "column": 0}, "map": {"version": 3, "sources": ["file:///T:/Development/TranscriptionModule/transcription-demo/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  showCloseButton = true,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\n  showCloseButton?: boolean\n}) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        {showCloseButton && (\n          <DialogPrimitive.Close\n            data-slot=\"dialog-close\"\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\n          >\n            <XIcon />\n            <span className=\"sr-only\">Close</span>\n          </DialogPrimitive.Close>\n        )}\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,KAEoC;QAFpC,EACd,GAAG,OAC+C,GAFpC;IAGd,qBAAO,6LAAC,6KAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,cAAc,KAEgC;QAFhC,EACrB,GAAG,OACkD,GAFhC;IAGrB,qBAAO,6LAAC,gLAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAJS;AAMT,SAAS,aAAa,KAEgC;QAFhC,EACpB,GAAG,OACiD,GAFhC;IAGpB,qBAAO,6LAAC,+KAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,YAAY,KAEgC;QAFhC,EACnB,GAAG,OACgD,GAFhC;IAGnB,qBAAO,6LAAC,8KAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,KAGgC;QAHhC,EACrB,SAAS,EACT,GAAG,OACkD,GAHhC;IAIrB,qBACE,6LAAC,gLAAuB;QACtB,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,cAAc,KAOtB;QAPsB,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ,GAPsB;IAQrB,qBACE,6LAAC;QAAa,aAAU;;0BACtB,6LAAC;;;;;0BACD,6LAAC,gLAAuB;gBACtB,aAAU;gBACV,WAAW,IAAA,4HAAE,EACX,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,6LAAC,8KAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,6LAAC,4MAAK;;;;;0CACN,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;MAhCS;AAkCT,SAAS,aAAa,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACpB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EAAC,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,aAAa,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACpB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,KAGgC;QAHhC,EACnB,SAAS,EACT,GAAG,OACgD,GAHhC;IAInB,qBACE,6LAAC,8KAAqB;QACpB,aAAU;QACV,WAAW,IAAA,4HAAE,EAAC,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,KAGgC;QAHhC,EACzB,SAAS,EACT,GAAG,OACsD,GAHhC;IAIzB,qBACE,6LAAC,oLAA2B;QAC1B,aAAU;QACV,WAAW,IAAA,4HAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 686, "column": 0}, "map": {"version": 3, "sources": ["file:///T:/Development/TranscriptionModule/transcription-demo/src/lib/convexClient.ts"], "sourcesContent": ["\"use client\";\n\nimport { ConvexHttpClient } from \"convex/browser\";\n\nexport function getConvex(): ConvexHttpClient | null {\n  const url = process.env.NEXT_PUBLIC_CONVEX_URL;\n  if (!url) return null;\n  try {\n    return new ConvexHttpClient(url);\n  } catch {\n    return null;\n  }\n}\n\n"], "names": [], "mappings": ";;;;AAKc;AAHd;AAAA;AAFA;;AAIO,SAAS;IACd,MAAM;IACN;;IACA,IAAI;QACF,OAAO,IAAI,sLAAgB,CAAC;IAC9B,EAAE,UAAM;QACN,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 712, "column": 0}, "map": {"version": 3, "sources": ["file:///T:/Development/TranscriptionModule/transcription-demo/src/app/ai/dashboard/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useEffect, useMemo, useState } from \"react\";\nimport { WorkspaceNav } from \"@/components/workspace-nav\";\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Input } from \"@/components/ui/input\";\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from \"@/components/ui/dialog\";\nimport { Edit3, Plus, Trash2, FileText, History, Users, Stethoscope, Activity, Pill, Brain } from \"lucide-react\";\nimport type { LucideIcon } from \"lucide-react\";\n\nconst categories = [\n  { id: \"anamnese\", label: \"Anamnese\", color: \"blue\", icon: FileText },\n  { id: \"antecedents\", label: \"Antecedents\", color: \"purple\", icon: History },\n  { id: \"family_history\", label: \"Family History\", color: \"green\", icon: Users },\n  { id: \"clinical_exam\", label: \"Clinical Exam\", color: \"red\", icon: Stethoscope },\n  { id: \"technical_exams\", label: \"Technical Exams\", color: \"orange\", icon: Activity },\n  { id: \"medication\", label: \"Medication\", color: \"pink\", icon: Pill },\n  { id: \"other_therapies\", label: \"Other Therapies\", color: \"indigo\", icon: Brain },\n] as const;\n\nconst resultTypes = [\n  { id: \"free_text\", label: \"Free Text\" },\n  { id: \"dropdown\", label: \"Dropdown\" },\n  { id: \"numerical_score\", label: \"Numerical Score\" },\n  { id: \"boolean\", label: \"Yes/No\" },\n  { id: \"structured_list\", label: \"Structured List\" },\n  { id: \"date\", label: \"Date\" },\n  { id: \"measurement\", label: \"Measurement\" },\n];\n\ntype Item = {\n  id: string;\n  category: string;\n  name: string;\n  description: string;\n  resultType: string;\n  enabled: boolean;\n  scoringSystem?: string;\n  dropdownOptions?: string;\n  measurementUnits?: string;\n  structuredListFields?: string;\n};\n\nconst initialItems: Item[] = [\n  { id: crypto.randomUUID(), category: \"clinical_exam\", name: \"Muscle Strength\", description: \"Extract muscle strength assessments using 0-5 scale.\", resultType: \"numerical_score\", enabled: true, scoringSystem: \"0-5 MRC scale\" },\n  { id: crypto.randomUUID(), category: \"clinical_exam\", name: \"Deep Tendon Reflexes\", description: \"Identify reflex assessments with grades.\", resultType: \"numerical_score\", enabled: true, scoringSystem: \"0-4+ scale\" },\n  { id: crypto.randomUUID(), category: \"anamnese\", name: \"Chief Complaint\", description: \"Extract the main presenting symptoms and duration.\", resultType: \"free_text\", enabled: true },\n  { id: crypto.randomUUID(), category: \"medication\", name: \"Current Medications\", description: \"Identify medications, dosages, and frequencies.\", resultType: \"structured_list\", enabled: true, structuredListFields: \"medication, dosage, frequency\" },\n  { id: crypto.randomUUID(), category: \"clinical_exam\", name: \"Blood Pressure\", description: \"Detect blood pressure readings.\", resultType: \"measurement\", enabled: true, measurementUnits: \"mmHg\" },\n];\n\nimport { getConvex } from \"@/lib/convexClient\";\n\ntype RawDoc = {\n  _id?: string;\n  id?: string;\n  category?: string;\n  name?: string;\n  description?: string;\n  resultType?: string;\n  enabled?: boolean;\n  scoringSystem?: string;\n  dropdownOptions?: string;\n  measurementUnits?: string;\n  structuredListFields?: string;\n};\n\nfunction normalizeItem(raw: unknown): Item {\n  const r = raw as RawDoc;\n  const id = r.id ?? (r._id ? String(r._id) : undefined) ?? crypto.randomUUID();\n  return {\n    id,\n    category: r.category ?? \"\",\n    name: r.name ?? \"\",\n    description: r.description ?? \"\",\n    resultType: r.resultType ?? \"free_text\",\n    enabled: r.enabled ?? true,\n    scoringSystem: r.scoringSystem,\n    dropdownOptions: r.dropdownOptions,\n    measurementUnits: r.measurementUnits,\n    structuredListFields: r.structuredListFields,\n  };\n}\n\nexport default function AiDashboardPage() {\n  const [items, setItems] = useState<Item[]>(initialItems);\n  const [q, setQ] = useState(\"\");\n  const [cat, setCat] = useState<string>(\"all\");\n  const [editing, setEditing] = useState<Item | null>(null);\n  const [open, setOpen] = useState(false);\n  const convex = getConvex();\n  const loose = convex as unknown as {\n    query: (name: string, args?: unknown) => Promise<unknown>;\n    mutation: (name: string, args?: unknown) => Promise<unknown>;\n    action: (name: string, args?: unknown) => Promise<unknown>;\n  };\n\n  useEffect(() => {\n    let ignore = false;\n    async function load() {\n      if (!convex) return;\n      try {\n        const res = await loose.query(\"detections:listDefinitions\", { q, category: cat } as { q?: string; category?: string });\n        if (!ignore) setItems((res as unknown[]).map(normalizeItem));\n      } catch {\n        // keep local state fallback\n      } finally {\n        // no-op\n      }\n    }\n    load();\n    return () => { ignore = true; };\n  }, [q, cat, convex, loose]);\n\n  const filtered = useMemo(() => items.filter(i => (cat === \"all\" || i.category === cat) && i.name.toLowerCase().includes(q.toLowerCase())), [items, q, cat]);\n\n  async function remove(id: string) {\n    if (convex) {\n      await loose.mutation(\"detections:removeDefinition\", { id } as { id: string });\n      const res = await loose.query(\"detections:listDefinitions\", { q, category: cat } as { q?: string; category?: string });\n      setItems((res as unknown[]).map(normalizeItem));\n    } else {\n      setItems(prev => prev.filter(i => i.id !== id));\n    }\n  }\n\n  async function toggle(id: string) {\n    const target = items.find(i => i.id === id);\n    if (!target) return;\n    const next: Omit<Item, \"id\"> & { id?: string } = { ...target, enabled: !target.enabled };\n    if (convex) {\n      await loose.mutation(\"detections:upsertDefinition\", next as Omit<Item, \"id\"> & { id?: string });\n      const res = await loose.query(\"detections:listDefinitions\", { q, category: cat } as { q?: string; category?: string });\n      setItems((res as unknown[]).map(normalizeItem));\n    } else {\n      setItems(prev => prev.map(i => i.id === id ? (next as Item) : i));\n    }\n  }\n\n  async function save(item: Item) {\n    if (convex) {\n      const payload: Omit<Item, \"id\"> & { id?: string } = { ...item, id: item.id || undefined };\n      await loose.mutation(\"detections:upsertDefinition\", payload);\n      const res = await loose.query(\"detections:listDefinitions\", { q, category: cat } as { q?: string; category?: string });\n      setItems((res as unknown[]).map(normalizeItem));\n    } else {\n      if (item.id) setItems(prev => prev.map(i => i.id === item.id ? item : i));\n      else setItems(prev => [{ ...item, id: crypto.randomUUID(), enabled: true }, ...prev]);\n    }\n    setOpen(false); setEditing(null);\n  }\n\n  return (\n    <div className=\"container mx-auto p-6 space-y-4\">\n      <WorkspaceNav />\n\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-xl font-semibold\">AI Detection Dashboard</h1>\n          <p className=\"text-sm text-muted-foreground\">Configure and manage detection items</p>\n        </div>\n        <div className=\"flex gap-2\">\n          {convex && (\n            <Button variant=\"outline\" onClick={async () => { await loose.action(\"detections:seedMockData\", {} as Record<string, never>); const res = await loose.query(\"detections:listDefinitions\", { q, category: cat } as { q?: string; category?: string }); setItems((res as unknown[]).map(normalizeItem)); }}>Seed Mock Data</Button>\n          )}\n          <Dialog open={open} onOpenChange={setOpen}>\n            <DialogTrigger asChild>\n              <Button onClick={() => setEditing(null)}><Plus className=\"h-4 w-4 mr-1\"/>Add Item</Button>\n            </DialogTrigger>\n            <DetectionItemDialog initial={editing ?? undefined} onSave={save} />\n          </Dialog>\n        </div>\n      </div>\n\n      <Card>\n        <CardContent className=\"pt-6 flex gap-3 items-center\">\n          <Input placeholder=\"Search items...\" value={q} onChange={(e) => setQ(e.target.value)} />\n          <Select value={cat} onValueChange={setCat}>\n            <SelectTrigger className=\"w-56\"><SelectValue placeholder=\"All Categories\"/></SelectTrigger>\n            <SelectContent>\n              <SelectItem value=\"all\">All Categories</SelectItem>\n              {categories.map(c => (<SelectItem key={c.id} value={c.id}>{c.label}</SelectItem>))}\n            </SelectContent>\n          </Select>\n        </CardContent>\n      </Card>\n\n      <div className=\"grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-3\">\n        {filtered.map((i) => {\n          const catInfo = categories.find(c => c.id === i.category)!;\n          const colors: Record<string, { bg: string; text: string; badge: string }> = {\n            blue: { bg: \"bg-blue-50\", text: \"text-blue-600\", badge: \"bg-blue-100 text-blue-800\" },\n            purple: { bg: \"bg-purple-50\", text: \"text-purple-600\", badge: \"bg-purple-100 text-purple-800\" },\n            green: { bg: \"bg-green-50\", text: \"text-green-600\", badge: \"bg-green-100 text-green-800\" },\n            red: { bg: \"bg-red-50\", text: \"text-red-600\", badge: \"bg-red-100 text-red-800\" },\n            orange: { bg: \"bg-orange-50\", text: \"text-orange-600\", badge: \"bg-orange-100 text-orange-800\" },\n            pink: { bg: \"bg-pink-50\", text: \"text-pink-600\", badge: \"bg-pink-100 text-pink-800\" },\n            indigo: { bg: \"bg-indigo-50\", text: \"text-indigo-600\", badge: \"bg-indigo-100 text-indigo-800\" },\n          };\n          const c = colors[catInfo.color];\n          const Icon: LucideIcon = catInfo.icon as LucideIcon;\n          return (\n            <Card key={i.id} className={i.enabled ? \"\" : \"opacity-60\"}>\n              <CardHeader className=\"flex items-start justify-between\">\n                <div className=\"flex items-center gap-3\">\n                  <div className={`p-2 rounded-lg ${c.bg}`}><Icon className={`${c.text} h-4 w-4`} /></div>\n                  <CardTitle className=\"text-base\">{i.name}</CardTitle>\n                </div>\n                <div className=\"flex gap-1\">\n                  <Button size=\"icon\" variant=\"ghost\" onClick={() => { setEditing(i); setOpen(true); }}><Edit3 className=\"h-4 w-4\"/></Button>\n                  <Button size=\"icon\" variant=\"ghost\" onClick={() => remove(i.id)}><Trash2 className=\"h-4 w-4\"/></Button>\n                </div>\n              </CardHeader>\n              <CardContent className=\"space-y-3\">\n                <div className=\"text-sm text-muted-foreground min-h-12\">{i.description}</div>\n                <div className=\"flex items-center justify-between text-sm\">\n                  <span className=\"text-muted-foreground\">Result Type</span>\n                  <span className=\"font-medium\">{resultTypes.find(r => r.id === i.resultType)?.label}</span>\n                </div>\n                {i.scoringSystem && (<div className=\"text-xs text-muted-foreground\">Scoring: {i.scoringSystem}</div>)}\n                {i.measurementUnits && (<div className=\"text-xs text-muted-foreground\">Units: {i.measurementUnits}</div>)}\n                <div className=\"flex items-center justify-between\">\n                  <span className=\"text-sm text-muted-foreground\">Status</span>\n                  <Button size=\"sm\" variant={i.enabled ? \"secondary\" : \"outline\"} onClick={() => toggle(i.id)}>{i.enabled ? \"Enabled\" : \"Disabled\"}</Button>\n                </div>\n                <div>\n                  <span className={`px-2 py-1 rounded-full text-xs ${c.badge}`}>{catInfo.label}</span>\n                </div>\n              </CardContent>\n            </Card>\n          );\n        })}\n      </div>\n    </div>\n  );\n}\n\nfunction DetectionItemDialog({ initial, onSave }: { initial?: Item; onSave: (item: Item) => void }) {\n  const [draft, setDraft] = useState<Item>(initial ?? { id: \"\", category: \"clinical_exam\", name: \"\", description: \"\", resultType: \"free_text\", enabled: true });\n\n  return (\n    <DialogContent className=\"sm:max-w-[640px]\">\n      <DialogHeader>\n        <DialogTitle>{initial ? \"Edit Item\" : \"Add Item\"}</DialogTitle>\n      </DialogHeader>\n      <div className=\"grid gap-3\">\n        <Input placeholder=\"Name\" value={draft.name} onChange={(e) => setDraft({ ...draft, name: e.target.value })} />\n        <Input placeholder=\"AI Instructions / Description\" value={draft.description} onChange={(e) => setDraft({ ...draft, description: e.target.value })} />\n        <div className=\"grid grid-cols-2 gap-3\">\n          <Select value={draft.category} onValueChange={(v) => setDraft({ ...draft, category: v })}>\n            <SelectTrigger><SelectValue placeholder=\"Category\"/></SelectTrigger>\n            <SelectContent>\n              {categories.map(c => (<SelectItem key={c.id} value={c.id}>{c.label}</SelectItem>))}\n            </SelectContent>\n          </Select>\n          <Select value={draft.resultType} onValueChange={(v) => setDraft({ ...draft, resultType: v })}>\n            <SelectTrigger><SelectValue placeholder=\"Result Type\"/></SelectTrigger>\n            <SelectContent>\n              {resultTypes.map(rt => (<SelectItem key={rt.id} value={rt.id}>{rt.label}</SelectItem>))}\n            </SelectContent>\n          </Select>\n        </div>\n        {draft.resultType === \"numerical_score\" && (\n          <Input placeholder=\"Scoring System (e.g., 0-5 MRC)\" value={draft.scoringSystem ?? \"\"} onChange={(e) => setDraft({ ...draft, scoringSystem: e.target.value })} />\n        )}\n        {draft.resultType === \"measurement\" && (\n          <Input placeholder=\"Units (e.g., mmHg)\" value={draft.measurementUnits ?? \"\"} onChange={(e) => setDraft({ ...draft, measurementUnits: e.target.value })} />\n        )}\n        {draft.resultType === \"structured_list\" && (\n          <Input placeholder=\"Fields (comma-separated)\" value={draft.structuredListFields ?? \"\"} onChange={(e) => setDraft({ ...draft, structuredListFields: e.target.value })} />\n        )}\n        {draft.resultType === \"dropdown\" && (\n          <Input placeholder=\"Options (one per line)\" value={draft.dropdownOptions ?? \"\"} onChange={(e) => setDraft({ ...draft, dropdownOptions: e.target.value })} />\n        )}\n      </div>\n      <DialogFooter>\n        <Button onClick={() => onSave(draft)} disabled={!draft.name || !draft.description}>Save</Button>\n      </DialogFooter>\n    </DialogContent>\n  );\n}\n\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA4CA;;;AArDA;;;;;;;;;AAYA,MAAM,aAAa;IACjB;QAAE,IAAI;QAAY,OAAO;QAAY,OAAO;QAAQ,MAAM,6NAAQ;IAAC;IACnE;QAAE,IAAI;QAAe,OAAO;QAAe,OAAO;QAAU,MAAM,sNAAO;IAAC;IAC1E;QAAE,IAAI;QAAkB,OAAO;QAAkB,OAAO;QAAS,MAAM,gNAAK;IAAC;IAC7E;QAAE,IAAI;QAAiB,OAAO;QAAiB,OAAO;QAAO,MAAM,kOAAW;IAAC;IAC/E;QAAE,IAAI;QAAmB,OAAO;QAAmB,OAAO;QAAU,MAAM,yNAAQ;IAAC;IACnF;QAAE,IAAI;QAAc,OAAO;QAAc,OAAO;QAAQ,MAAM,6MAAI;IAAC;IACnE;QAAE,IAAI;QAAmB,OAAO;QAAmB,OAAO;QAAU,MAAM,gNAAK;IAAC;CACjF;AAED,MAAM,cAAc;IAClB;QAAE,IAAI;QAAa,OAAO;IAAY;IACtC;QAAE,IAAI;QAAY,OAAO;IAAW;IACpC;QAAE,IAAI;QAAmB,OAAO;IAAkB;IAClD;QAAE,IAAI;QAAW,OAAO;IAAS;IACjC;QAAE,IAAI;QAAmB,OAAO;IAAkB;IAClD;QAAE,IAAI;QAAQ,OAAO;IAAO;IAC5B;QAAE,IAAI;QAAe,OAAO;IAAc;CAC3C;AAeD,MAAM,eAAuB;IAC3B;QAAE,IAAI,OAAO,UAAU;QAAI,UAAU;QAAiB,MAAM;QAAmB,aAAa;QAAwD,YAAY;QAAmB,SAAS;QAAM,eAAe;IAAgB;IACjO;QAAE,IAAI,OAAO,UAAU;QAAI,UAAU;QAAiB,MAAM;QAAwB,aAAa;QAA4C,YAAY;QAAmB,SAAS;QAAM,eAAe;IAAa;IACvN;QAAE,IAAI,OAAO,UAAU;QAAI,UAAU;QAAY,MAAM;QAAmB,aAAa;QAAsD,YAAY;QAAa,SAAS;IAAK;IACpL;QAAE,IAAI,OAAO,UAAU;QAAI,UAAU;QAAc,MAAM;QAAuB,aAAa;QAAmD,YAAY;QAAmB,SAAS;QAAM,sBAAsB;IAAgC;IACpP;QAAE,IAAI,OAAO,UAAU;QAAI,UAAU;QAAiB,MAAM;QAAkB,aAAa;QAAmC,YAAY;QAAe,SAAS;QAAM,kBAAkB;IAAO;CAClM;;AAkBD,SAAS,cAAc,GAAY;IACjC,MAAM,IAAI;QACC,OAAA;IAAX,MAAM,KAAK,CAAA,OAAA,CAAA,QAAA,EAAE,EAAE,cAAJ,mBAAA,QAAS,EAAE,GAAG,GAAG,OAAO,EAAE,GAAG,IAAI,uBAAjC,kBAAA,OAA+C,OAAO,UAAU;QAG/D,aACJ,SACO,gBACD,eACH;IANX,OAAO;QACL;QACA,UAAU,CAAA,cAAA,EAAE,QAAQ,cAAV,yBAAA,cAAc;QACxB,MAAM,CAAA,UAAA,EAAE,IAAI,cAAN,qBAAA,UAAU;QAChB,aAAa,CAAA,iBAAA,EAAE,WAAW,cAAb,4BAAA,iBAAiB;QAC9B,YAAY,CAAA,gBAAA,EAAE,UAAU,cAAZ,2BAAA,gBAAgB;QAC5B,SAAS,CAAA,aAAA,EAAE,OAAO,cAAT,wBAAA,aAAa;QACtB,eAAe,EAAE,aAAa;QAC9B,iBAAiB,EAAE,eAAe;QAClC,kBAAkB,EAAE,gBAAgB;QACpC,sBAAsB,EAAE,oBAAoB;IAC9C;AACF;AAEe,SAAS;;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,yKAAQ,EAAS;IAC3C,MAAM,CAAC,GAAG,KAAK,GAAG,IAAA,yKAAQ,EAAC;IAC3B,MAAM,CAAC,KAAK,OAAO,GAAG,IAAA,yKAAQ,EAAS;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,yKAAQ,EAAc;IACpD,MAAM,CAAC,MAAM,QAAQ,GAAG,IAAA,yKAAQ,EAAC;IACjC,MAAM,SAAS,IAAA,0IAAS;IACxB,MAAM,QAAQ;IAMd,IAAA,0KAAS;qCAAC;YACR,IAAI,SAAS;YACb,eAAe;gBACb,IAAI,CAAC,QAAQ;gBACb,IAAI;oBACF,MAAM,MAAM,MAAM,MAAM,KAAK,CAAC,8BAA8B;wBAAE;wBAAG,UAAU;oBAAI;oBAC/E,IAAI,CAAC,QAAQ,SAAS,AAAC,IAAkB,GAAG,CAAC;gBAC/C,EAAE,UAAM;gBACN,4BAA4B;gBAC9B,SAAU;gBACR,QAAQ;gBACV;YACF;YACA;YACA;6CAAO;oBAAQ,SAAS;gBAAM;;QAChC;oCAAG;QAAC;QAAG;QAAK;QAAQ;KAAM;IAE1B,MAAM,WAAW,IAAA,wKAAO;6CAAC,IAAM,MAAM,MAAM;qDAAC,CAAA,IAAK,CAAC,QAAQ,SAAS,EAAE,QAAQ,KAAK,GAAG,KAAK,EAAE,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,EAAE,WAAW;;4CAAM;QAAC;QAAO;QAAG;KAAI;IAE1J,eAAe,OAAO,EAAU;QAC9B,IAAI,QAAQ;YACV,MAAM,MAAM,QAAQ,CAAC,+BAA+B;gBAAE;YAAG;YACzD,MAAM,MAAM,MAAM,MAAM,KAAK,CAAC,8BAA8B;gBAAE;gBAAG,UAAU;YAAI;YAC/E,SAAS,AAAC,IAAkB,GAAG,CAAC;QAClC,OAAO;YACL,SAAS,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC7C;IACF;IAEA,eAAe,OAAO,EAAU;QAC9B,MAAM,SAAS,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACxC,IAAI,CAAC,QAAQ;QACb,MAAM,OAA2C;YAAE,GAAG,MAAM;YAAE,SAAS,CAAC,OAAO,OAAO;QAAC;QACvF,IAAI,QAAQ;YACV,MAAM,MAAM,QAAQ,CAAC,+BAA+B;YACpD,MAAM,MAAM,MAAM,MAAM,KAAK,CAAC,8BAA8B;gBAAE;gBAAG,UAAU;YAAI;YAC/E,SAAS,AAAC,IAAkB,GAAG,CAAC;QAClC,OAAO;YACL,SAAS,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,KAAM,OAAgB;QAChE;IACF;IAEA,eAAe,KAAK,IAAU;QAC5B,IAAI,QAAQ;YACV,MAAM,UAA8C;gBAAE,GAAG,IAAI;gBAAE,IAAI,KAAK,EAAE,IAAI;YAAU;YACxF,MAAM,MAAM,QAAQ,CAAC,+BAA+B;YACpD,MAAM,MAAM,MAAM,MAAM,KAAK,CAAC,8BAA8B;gBAAE;gBAAG,UAAU;YAAI;YAC/E,SAAS,AAAC,IAAkB,GAAG,CAAC;QAClC,OAAO;YACL,IAAI,KAAK,EAAE,EAAE,SAAS,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,KAAK,EAAE,GAAG,OAAO;iBACjE,SAAS,CAAA,OAAQ;oBAAC;wBAAE,GAAG,IAAI;wBAAE,IAAI,OAAO,UAAU;wBAAI,SAAS;oBAAK;uBAAM;iBAAK;QACtF;QACA,QAAQ;QAAQ,WAAW;IAC7B;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,yJAAY;;;;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAwB;;;;;;0CACtC,6LAAC;gCAAE,WAAU;0CAAgC;;;;;;;;;;;;kCAE/C,6LAAC;wBAAI,WAAU;;4BACZ,wBACC,6LAAC,+IAAM;gCAAC,SAAQ;gCAAU,SAAS;oCAAc,MAAM,MAAM,MAAM,CAAC,2BAA2B,CAAC;oCAA6B,MAAM,MAAM,MAAM,MAAM,KAAK,CAAC,8BAA8B;wCAAE;wCAAG,UAAU;oCAAI;oCAAyC,SAAS,AAAC,IAAkB,GAAG,CAAC;gCAAiB;0CAAG;;;;;;0CAE3S,6LAAC,+IAAM;gCAAC,MAAM;gCAAM,cAAc;;kDAChC,6LAAC,sJAAa;wCAAC,OAAO;kDACpB,cAAA,6LAAC,+IAAM;4CAAC,SAAS,IAAM,WAAW;;8DAAO,6LAAC,6MAAI;oDAAC,WAAU;;;;;;gDAAgB;;;;;;;;;;;;kDAE3E,6LAAC;wCAAoB,SAAS,oBAAA,qBAAA,UAAW;wCAAW,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;0BAKlE,6LAAC,2IAAI;0BACH,cAAA,6LAAC,kJAAW;oBAAC,WAAU;;sCACrB,6LAAC,6IAAK;4BAAC,aAAY;4BAAkB,OAAO;4BAAG,UAAU,CAAC,IAAM,KAAK,EAAE,MAAM,CAAC,KAAK;;;;;;sCACnF,6LAAC,+IAAM;4BAAC,OAAO;4BAAK,eAAe;;8CACjC,6LAAC,sJAAa;oCAAC,WAAU;8CAAO,cAAA,6LAAC,oJAAW;wCAAC,aAAY;;;;;;;;;;;8CACzD,6LAAC,sJAAa;;sDACZ,6LAAC,mJAAU;4CAAC,OAAM;sDAAM;;;;;;wCACvB,WAAW,GAAG,CAAC,CAAA,kBAAM,6LAAC,mJAAU;gDAAY,OAAO,EAAE,EAAE;0DAAG,EAAE,KAAK;+CAA3B,EAAE,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAMnD,6LAAC;gBAAI,WAAU;0BACZ,SAAS,GAAG,CAAC,CAAC;wBA6B0B;oBA5BvC,MAAM,UAAU,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,EAAE,QAAQ;oBACxD,MAAM,SAAsE;wBAC1E,MAAM;4BAAE,IAAI;4BAAc,MAAM;4BAAiB,OAAO;wBAA4B;wBACpF,QAAQ;4BAAE,IAAI;4BAAgB,MAAM;4BAAmB,OAAO;wBAAgC;wBAC9F,OAAO;4BAAE,IAAI;4BAAe,MAAM;4BAAkB,OAAO;wBAA8B;wBACzF,KAAK;4BAAE,IAAI;4BAAa,MAAM;4BAAgB,OAAO;wBAA0B;wBAC/E,QAAQ;4BAAE,IAAI;4BAAgB,MAAM;4BAAmB,OAAO;wBAAgC;wBAC9F,MAAM;4BAAE,IAAI;4BAAc,MAAM;4BAAiB,OAAO;wBAA4B;wBACpF,QAAQ;4BAAE,IAAI;4BAAgB,MAAM;4BAAmB,OAAO;wBAAgC;oBAChG;oBACA,MAAM,IAAI,MAAM,CAAC,QAAQ,KAAK,CAAC;oBAC/B,MAAM,OAAmB,QAAQ,IAAI;oBACrC,qBACE,6LAAC,2IAAI;wBAAY,WAAW,EAAE,OAAO,GAAG,KAAK;;0CAC3C,6LAAC,iJAAU;gCAAC,WAAU;;kDACpB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAW,AAAC,kBAAsB,OAAL,EAAE,EAAE;0DAAI,cAAA,6LAAC;oDAAK,WAAW,AAAC,GAAS,OAAP,EAAE,IAAI,EAAC;;;;;;;;;;;0DACrE,6LAAC,gJAAS;gDAAC,WAAU;0DAAa,EAAE,IAAI;;;;;;;;;;;;kDAE1C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,+IAAM;gDAAC,MAAK;gDAAO,SAAQ;gDAAQ,SAAS;oDAAQ,WAAW;oDAAI,QAAQ;gDAAO;0DAAG,cAAA,6LAAC,sNAAK;oDAAC,WAAU;;;;;;;;;;;0DACvG,6LAAC,+IAAM;gDAAC,MAAK;gDAAO,SAAQ;gDAAQ,SAAS,IAAM,OAAO,EAAE,EAAE;0DAAG,cAAA,6LAAC,uNAAM;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0CAGvF,6LAAC,kJAAW;gCAAC,WAAU;;kDACrB,6LAAC;wCAAI,WAAU;kDAA0C,EAAE,WAAW;;;;;;kDACtE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAwB;;;;;;0DACxC,6LAAC;gDAAK,WAAU;2DAAe,oBAAA,YAAY,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,EAAE,UAAU,eAA3C,wCAAA,kBAA8C,KAAK;;;;;;;;;;;;oCAEnF,EAAE,aAAa,kBAAK,6LAAC;wCAAI,WAAU;;4CAAgC;4CAAU,EAAE,aAAa;;;;;;;oCAC5F,EAAE,gBAAgB,kBAAK,6LAAC;wCAAI,WAAU;;4CAAgC;4CAAQ,EAAE,gBAAgB;;;;;;;kDACjG,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAgC;;;;;;0DAChD,6LAAC,+IAAM;gDAAC,MAAK;gDAAK,SAAS,EAAE,OAAO,GAAG,cAAc;gDAAW,SAAS,IAAM,OAAO,EAAE,EAAE;0DAAI,EAAE,OAAO,GAAG,YAAY;;;;;;;;;;;;kDAExH,6LAAC;kDACC,cAAA,6LAAC;4CAAK,WAAW,AAAC,kCAAyC,OAAR,EAAE,KAAK;sDAAK,QAAQ,KAAK;;;;;;;;;;;;;;;;;;uBAxBvE,EAAE,EAAE;;;;;gBA6BnB;;;;;;;;;;;;AAIR;GAvJwB;KAAA;AAyJxB,SAAS,oBAAoB,KAAqE;QAArE,EAAE,OAAO,EAAE,MAAM,EAAoD,GAArE;;IAC3B,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,yKAAQ,EAAO,oBAAA,qBAAA,UAAW;QAAE,IAAI;QAAI,UAAU;QAAiB,MAAM;QAAI,aAAa;QAAI,YAAY;QAAa,SAAS;IAAK;QAyBxF,sBAGZ,yBAGM,6BAGF;IAhC3D,qBACE,6LAAC,sJAAa;QAAC,WAAU;;0BACvB,6LAAC,qJAAY;0BACX,cAAA,6LAAC,oJAAW;8BAAE,UAAU,cAAc;;;;;;;;;;;0BAExC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,6IAAK;wBAAC,aAAY;wBAAO,OAAO,MAAM,IAAI;wBAAE,UAAU,CAAC,IAAM,SAAS;gCAAE,GAAG,KAAK;gCAAE,MAAM,EAAE,MAAM,CAAC,KAAK;4BAAC;;;;;;kCACxG,6LAAC,6IAAK;wBAAC,aAAY;wBAAgC,OAAO,MAAM,WAAW;wBAAE,UAAU,CAAC,IAAM,SAAS;gCAAE,GAAG,KAAK;gCAAE,aAAa,EAAE,MAAM,CAAC,KAAK;4BAAC;;;;;;kCAC/I,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+IAAM;gCAAC,OAAO,MAAM,QAAQ;gCAAE,eAAe,CAAC,IAAM,SAAS;wCAAE,GAAG,KAAK;wCAAE,UAAU;oCAAE;;kDACpF,6LAAC,sJAAa;kDAAC,cAAA,6LAAC,oJAAW;4CAAC,aAAY;;;;;;;;;;;kDACxC,6LAAC,sJAAa;kDACX,WAAW,GAAG,CAAC,CAAA,kBAAM,6LAAC,mJAAU;gDAAY,OAAO,EAAE,EAAE;0DAAG,EAAE,KAAK;+CAA3B,EAAE,EAAE;;;;;;;;;;;;;;;;0CAG/C,6LAAC,+IAAM;gCAAC,OAAO,MAAM,UAAU;gCAAE,eAAe,CAAC,IAAM,SAAS;wCAAE,GAAG,KAAK;wCAAE,YAAY;oCAAE;;kDACxF,6LAAC,sJAAa;kDAAC,cAAA,6LAAC,oJAAW;4CAAC,aAAY;;;;;;;;;;;kDACxC,6LAAC,sJAAa;kDACX,YAAY,GAAG,CAAC,CAAA,mBAAO,6LAAC,mJAAU;gDAAa,OAAO,GAAG,EAAE;0DAAG,GAAG,KAAK;+CAA9B,GAAG,EAAE;;;;;;;;;;;;;;;;;;;;;;oBAInD,MAAM,UAAU,KAAK,mCACpB,6LAAC,6IAAK;wBAAC,aAAY;wBAAiC,OAAO,CAAA,uBAAA,MAAM,aAAa,cAAnB,kCAAA,uBAAuB;wBAAI,UAAU,CAAC,IAAM,SAAS;gCAAE,GAAG,KAAK;gCAAE,eAAe,EAAE,MAAM,CAAC,KAAK;4BAAC;;;;;;oBAE3J,MAAM,UAAU,KAAK,+BACpB,6LAAC,6IAAK;wBAAC,aAAY;wBAAqB,OAAO,CAAA,0BAAA,MAAM,gBAAgB,cAAtB,qCAAA,0BAA0B;wBAAI,UAAU,CAAC,IAAM,SAAS;gCAAE,GAAG,KAAK;gCAAE,kBAAkB,EAAE,MAAM,CAAC,KAAK;4BAAC;;;;;;oBAErJ,MAAM,UAAU,KAAK,mCACpB,6LAAC,6IAAK;wBAAC,aAAY;wBAA2B,OAAO,CAAA,8BAAA,MAAM,oBAAoB,cAA1B,yCAAA,8BAA8B;wBAAI,UAAU,CAAC,IAAM,SAAS;gCAAE,GAAG,KAAK;gCAAE,sBAAsB,EAAE,MAAM,CAAC,KAAK;4BAAC;;;;;;oBAEnK,MAAM,UAAU,KAAK,4BACpB,6LAAC,6IAAK;wBAAC,aAAY;wBAAyB,OAAO,CAAA,yBAAA,MAAM,eAAe,cAArB,oCAAA,yBAAyB;wBAAI,UAAU,CAAC,IAAM,SAAS;gCAAE,GAAG,KAAK;gCAAE,iBAAiB,EAAE,MAAM,CAAC,KAAK;4BAAC;;;;;;;;;;;;0BAG1J,6LAAC,qJAAY;0BACX,cAAA,6LAAC,+IAAM;oBAAC,SAAS,IAAM,OAAO;oBAAQ,UAAU,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,WAAW;8BAAE;;;;;;;;;;;;;;;;;AAI3F;IA3CS;MAAA", "debugId": null}}]}