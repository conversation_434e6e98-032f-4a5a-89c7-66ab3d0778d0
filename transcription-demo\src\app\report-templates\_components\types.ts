export type TextBlock = { id: string; type: "TEXT"; content: string };
export type AiBlock = { id: string; type: "AI_BLOCK"; innerContent: string };
export type FindingBlock = { id: string; type: "FINDING_BLOCK"; findingId: string; findingName: string; selectedValue: "value" | "name" };
export type CategoryBlock = { id: string; type: "CATEGORY_BLOCK"; category: string; categoryName: string; selectedFindingIds: string[] };
export type Block = TextBlock | AiBlock | FindingBlock | CategoryBlock;

export type DetectionItem = { id: string; name: string; category: string };
export type AutoDetectedFinding = { id: string; value: string };

