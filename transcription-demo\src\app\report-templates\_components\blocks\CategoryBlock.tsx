"use client";

import { useMemo, useRef, useState, useEffect } from "react";
import { GripVertical, ChevronDown, Search } from "lucide-react";
import type { CategoryBlock as CategoryBlockT, DetectionItem } from "../types";

export function CategoryBlock({ block, onDragStart, onDropOver, onDropOn, allFindings, onSelectionChange }: {
  block: CategoryBlockT;
  onDragStart?: (e: React.DragEvent, id: string) => void;
  onDropOver?: (e: React.DragEvent) => void;
  onDropOn?: (e: React.DragEvent, id: string) => void;
  allFindings: DetectionItem[];
  onSelectionChange?: (id: string, ids: string[]) => void;
}) {
  const [open, setOpen] = useState(false);
  const [q, setQ] = useState("");
  const wrapRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    const h = (ev: MouseEvent) => {
      if (wrapRef.current && !wrapRef.current.contains(ev.target as Node)) setOpen(false);
    };
    window.addEventListener("mousedown", h);
    return () => window.removeEventListener("mousedown", h);
  }, []);

  const items = useMemo(() => allFindings.filter((f) => f.category === block.category), [allFindings, block.category]);
  const filtered = useMemo(() => items.filter((f) => f.name.toLowerCase().includes(q.toLowerCase())), [items, q]);

  function toggle(id: string) {
    const next = block.selectedFindingIds.includes(id) ? block.selectedFindingIds.filter((x) => x !== id) : [...block.selectedFindingIds, id];
    onSelectionChange?.(block.id, next);
  }

  return (
    <div
      ref={wrapRef}
      contentEditable={false}
      draggable
      onDragStart={(e) => onDragStart?.(e, block.id)}
      onDragOver={onDropOver}
      onDrop={(e) => onDropOn?.(e, block.id)}
      className="my-2 p-2 rounded-lg border border-border bg-muted/30 flex items-start gap-2 cursor-grab"
      data-id={block.id}
    >
      <GripVertical size={16} className="text-muted-foreground mt-1" />
      <div className="flex-1">
        <div className="flex items-center justify-between">
          <span className="font-medium text-foreground">{block.categoryName}</span>
          <button type="button" className="flex items-center gap-1 text-sm text-primary" onClick={() => setOpen((v) => !v)}>
            {block.selectedFindingIds.length} / {items.length} selected <ChevronDown size={16} className={`transition-transform ${open ? "rotate-180" : ""}`} />
          </button>
        </div>
        {open && (
          <div className="mt-2 p-2 rounded-md bg-background border shadow-sm">
            <div className="relative mb-2">
              <Search size={14} className="absolute left-2 top-1/2 -translate-y-1/2 text-muted-foreground" />
              <input value={q} onChange={(e) => setQ(e.target.value)} placeholder="Search findings..." className="w-full pl-7 pr-2 py-1 border rounded-md text-sm"/>
            </div>
            <div className="max-h-40 overflow-y-auto">
              {filtered.map((f) => (
                <label key={f.id} className="flex items-center gap-2 p-1.5 rounded hover:bg-muted/50">
                  <input type="checkbox" className="h-4 w-4" checked={block.selectedFindingIds.includes(f.id)} onChange={() => toggle(f.id)} />
                  <span className="text-sm">{f.name}</span>
                </label>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

