{"version": 3, "sources": [], "sections": [{"offset": {"line": 10, "column": 0}, "map": {"version": 3, "sources": ["file:///T:/Development/TranscriptionModule/transcription-demo/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,IAAA,sKAAO,EAAC,IAAA,6IAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 25, "column": 0}, "map": {"version": 3, "sources": ["file:///T:/Development/TranscriptionModule/transcription-demo/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,IAAA,uKAAG,EACxB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,wKAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,yHAAE,EAAC,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///T:/Development/TranscriptionModule/transcription-demo/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,yHAAE,EACX,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,yHAAE,EACX,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,yHAAE,EAAC,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,yHAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,yHAAE,EACX,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,yHAAE,EAAC,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,yHAAE,EAAC,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 184, "column": 0}, "map": {"version": 3, "sources": ["file:///T:/Development/TranscriptionModule/transcription-demo/src/components/ui/progress.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Progress({\n  className,\n  value,\n  ...props\n}: React.ComponentProps<typeof ProgressPrimitive.Root>) {\n  return (\n    <ProgressPrimitive.Root\n      data-slot=\"progress\"\n      className={cn(\n        \"bg-primary/20 relative h-2 w-full overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    >\n      <ProgressPrimitive.Indicator\n        data-slot=\"progress-indicator\"\n        className=\"bg-primary h-full w-full flex-1 transition-all\"\n        style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\n      />\n    </ProgressPrimitive.Root>\n  )\n}\n\nexport { Progress }\n"], "names": [], "mappings": ";;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,SAAS,EAChB,SAAS,EACT,KAAK,EACL,GAAG,OACiD;IACpD,qBACE,8OAAC,4KAAsB;QACrB,aAAU;QACV,WAAW,IAAA,yHAAE,EACX,kEACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,iLAA2B;YAC1B,aAAU;YACV,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;AAIlE", "debugId": null}}, {"offset": {"line": 222, "column": 0}, "map": {"version": 3, "sources": ["file:///T:/Development/TranscriptionModule/transcription-demo/src/components/ui/tooltip.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction TooltipProvider({\n  delayDuration = 0,\n  ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Provider>) {\n  return (\n    <TooltipPrimitive.Provider\n      data-slot=\"tooltip-provider\"\n      delayDuration={delayDuration}\n      {...props}\n    />\n  )\n}\n\nfunction Tooltip({\n  ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Root>) {\n  return (\n    <TooltipProvider>\n      <TooltipPrimitive.Root data-slot=\"tooltip\" {...props} />\n    </TooltipProvider>\n  )\n}\n\nfunction TooltipTrigger({\n  ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Trigger>) {\n  return <TooltipPrimitive.Trigger data-slot=\"tooltip-trigger\" {...props} />\n}\n\nfunction TooltipContent({\n  className,\n  sideOffset = 0,\n  children,\n  ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Content>) {\n  return (\n    <TooltipPrimitive.Portal>\n      <TooltipPrimitive.Content\n        data-slot=\"tooltip-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        <TooltipPrimitive.Arrow className=\"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]\" />\n      </TooltipPrimitive.Content>\n    </TooltipPrimitive.Portal>\n  )\n}\n\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }\n"], "names": [], "mappings": ";;;;;;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,gBAAgB,EACvB,gBAAgB,CAAC,EACjB,GAAG,OACoD;IACvD,qBACE,8OAAC,+KAAyB;QACxB,aAAU;QACV,eAAe;QACd,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,QAAQ,EACf,GAAG,OACgD;IACnD,qBACE,8OAAC;kBACC,cAAA,8OAAC,2KAAqB;YAAC,aAAU;YAAW,GAAG,KAAK;;;;;;;;;;;AAG1D;AAEA,SAAS,eAAe,EACtB,GAAG,OACmD;IACtD,qBAAO,8OAAC,8KAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,aAAa,CAAC,EACd,QAAQ,EACR,GAAG,OACmD;IACtD,qBACE,8OAAC,6KAAuB;kBACtB,cAAA,8OAAC,8KAAwB;YACvB,aAAU;YACV,YAAY;YACZ,WAAW,IAAA,yHAAE,EACX,0aACA;YAED,GAAG,KAAK;;gBAER;8BACD,8OAAC,4KAAsB;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI1C", "debugId": null}}, {"offset": {"line": 309, "column": 0}, "map": {"version": 3, "sources": ["file:///T:/Development/TranscriptionModule/transcription-demo/src/components/recording-widget.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useEffect, useRef, useState } from \"react\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { <PERSON>, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Progress } from \"@/components/ui/progress\";\nimport { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from \"@/components/ui/tooltip\";\nimport { Mic, Square, Play } from \"lucide-react\";\n\nexport function RecordingWidget() {\n  const mediaRecorderRef = useRef<MediaRecorder | null>(null);\n  const [recording, setRecording] = useState(false);\n  const [chunks, setChunks] = useState<BlobPart[]>([]);\n  const [elapsed, setElapsed] = useState(0);\n  const timerRef = useRef<number | null>(null);\n\n  useEffect(() => {\n    return () => {\n      if (timerRef.current) window.clearInterval(timerRef.current);\n    };\n  }, []);\n\n  async function startRecording() {\n    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });\n    const mr = new MediaRecorder(stream);\n    mediaRecorderRef.current = mr;\n    setChunks([]);\n    mr.ondataavailable = (e) => setChunks((c) => (e.data ? [...c, e.data] : c));\n    mr.onstart = () => setRecording(true);\n    mr.onstop = () => setRecording(false);\n    mr.start(1000);\n    setElapsed(0);\n    timerRef.current = window.setInterval(() => setElapsed((e) => e + 1), 1000);\n  }\n\n  function stopRecording() {\n    mediaRecorderRef.current?.stop();\n    mediaRecorderRef.current?.stream.getTracks().forEach((t) => t.stop());\n    if (timerRef.current) window.clearInterval(timerRef.current);\n  }\n\n  async function playBack() {\n    const blob = new Blob(chunks, { type: \"audio/webm\" });\n    const url = URL.createObjectURL(blob);\n    const audio = new Audio(url);\n    audio.play();\n  }\n\n  return (\n    <Card className=\"fixed bottom-4 right-4 w-[360px] shadow-lg\">\n      <CardHeader className=\"pb-2\">\n        <CardTitle className=\"text-base\">Recording</CardTitle>\n      </CardHeader>\n      <CardContent className=\"space-y-3\">\n        <div className=\"flex items-center gap-2\">\n          <TooltipProvider>\n            <Tooltip>\n              <TooltipTrigger asChild>\n                <Button size=\"sm\" variant={recording ? \"destructive\" : \"default\"} onClick={recording ? stopRecording : startRecording}>\n                  {recording ? <Square className=\"h-4 w-4\" /> : <Mic className=\"h-4 w-4\" />}\n                </Button>\n              </TooltipTrigger>\n              <TooltipContent>{recording ? \"Stop\" : \"Start\"} recording</TooltipContent>\n            </Tooltip>\n          </TooltipProvider>\n\n          <Button size=\"sm\" variant=\"secondary\" onClick={playBack} disabled={!chunks.length}>\n            <Play className=\"h-4 w-4\" />\n          </Button>\n\n          <div className=\"ml-auto text-xs tabular-nums\">\n            {new Date(elapsed * 1000).toISOString().substring(14, 19)}\n          </div>\n        </div>\n        <Progress value={Math.min(100, (elapsed % 60) * 1.66)} />\n        <div className=\"text-[11px] text-muted-foreground\">\n          Captures audio locally. Wire the upload to Convex when starting a session.\n        </div>\n      </CardContent>\n    </Card>\n  );\n}\n\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAPA;;;;;;;;AASO,SAAS;IACd,MAAM,mBAAmB,IAAA,+MAAM,EAAuB;IACtD,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,iNAAQ,EAAC;IAC3C,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,iNAAQ,EAAa,EAAE;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,iNAAQ,EAAC;IACvC,MAAM,WAAW,IAAA,+MAAM,EAAgB;IAEvC,IAAA,kNAAS,EAAC;QACR,OAAO;YACL,IAAI,SAAS,OAAO,EAAE,OAAO,aAAa,CAAC,SAAS,OAAO;QAC7D;IACF,GAAG,EAAE;IAEL,eAAe;QACb,MAAM,SAAS,MAAM,UAAU,YAAY,CAAC,YAAY,CAAC;YAAE,OAAO;QAAK;QACvE,MAAM,KAAK,IAAI,cAAc;QAC7B,iBAAiB,OAAO,GAAG;QAC3B,UAAU,EAAE;QACZ,GAAG,eAAe,GAAG,CAAC,IAAM,UAAU,CAAC,IAAO,EAAE,IAAI,GAAG;uBAAI;oBAAG,EAAE,IAAI;iBAAC,GAAG;QACxE,GAAG,OAAO,GAAG,IAAM,aAAa;QAChC,GAAG,MAAM,GAAG,IAAM,aAAa;QAC/B,GAAG,KAAK,CAAC;QACT,WAAW;QACX,SAAS,OAAO,GAAG,OAAO,WAAW,CAAC,IAAM,WAAW,CAAC,IAAM,IAAI,IAAI;IACxE;IAEA,SAAS;QACP,iBAAiB,OAAO,EAAE;QAC1B,iBAAiB,OAAO,EAAE,OAAO,YAAY,QAAQ,CAAC,IAAM,EAAE,IAAI;QAClE,IAAI,SAAS,OAAO,EAAE,OAAO,aAAa,CAAC,SAAS,OAAO;IAC7D;IAEA,eAAe;QACb,MAAM,OAAO,IAAI,KAAK,QAAQ;YAAE,MAAM;QAAa;QACnD,MAAM,MAAM,IAAI,eAAe,CAAC;QAChC,MAAM,QAAQ,IAAI,MAAM;QACxB,MAAM,IAAI;IACZ;IAEA,qBACE,8OAAC,wIAAI;QAAC,WAAU;;0BACd,8OAAC,8IAAU;gBAAC,WAAU;0BACpB,cAAA,8OAAC,6IAAS;oBAAC,WAAU;8BAAY;;;;;;;;;;;0BAEnC,8OAAC,+IAAW;gBAAC,WAAU;;kCACrB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,sJAAe;0CACd,cAAA,8OAAC,8IAAO;;sDACN,8OAAC,qJAAc;4CAAC,OAAO;sDACrB,cAAA,8OAAC,4IAAM;gDAAC,MAAK;gDAAK,SAAS,YAAY,gBAAgB;gDAAW,SAAS,YAAY,gBAAgB;0DACpG,0BAAY,8OAAC,gNAAM;oDAAC,WAAU;;;;;yEAAe,8OAAC,uMAAG;oDAAC,WAAU;;;;;;;;;;;;;;;;sDAGjE,8OAAC,qJAAc;;gDAAE,YAAY,SAAS;gDAAQ;;;;;;;;;;;;;;;;;;0CAIlD,8OAAC,4IAAM;gCAAC,MAAK;gCAAK,SAAQ;gCAAY,SAAS;gCAAU,UAAU,CAAC,OAAO,MAAM;0CAC/E,cAAA,8OAAC,0MAAI;oCAAC,WAAU;;;;;;;;;;;0CAGlB,8OAAC;gCAAI,WAAU;0CACZ,IAAI,KAAK,UAAU,MAAM,WAAW,GAAG,SAAS,CAAC,IAAI;;;;;;;;;;;;kCAG1D,8OAAC,gJAAQ;wBAAC,OAAO,KAAK,GAAG,CAAC,KAAK,AAAC,UAAU,KAAM;;;;;;kCAChD,8OAAC;wBAAI,WAAU;kCAAoC;;;;;;;;;;;;;;;;;;AAM3D", "debugId": null}}]}