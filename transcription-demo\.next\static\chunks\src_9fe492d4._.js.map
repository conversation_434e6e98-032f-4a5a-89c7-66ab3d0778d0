{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///T:/Development/TranscriptionModule/transcription-demo/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Tabs({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Root>) {\n  return (\n    <TabsPrimitive.Root\n      data-slot=\"tabs\"\n      className={cn(\"flex flex-col gap-2\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TabsList({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.List>) {\n  return (\n    <TabsPrimitive.List\n      data-slot=\"tabs-list\"\n      className={cn(\n        \"bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsTrigger({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\n  return (\n    <TabsPrimitive.Trigger\n      data-slot=\"tabs-trigger\"\n      className={cn(\n        \"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Content>) {\n  return (\n    <TabsPrimitive.Content\n      data-slot=\"tabs-content\"\n      className={cn(\"flex-1 outline-none\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,KAAK,KAGoC;QAHpC,EACZ,SAAS,EACT,GAAG,OAC6C,GAHpC;IAIZ,qBACE,6LAAC,2KAAkB;QACjB,aAAU;QACV,WAAW,IAAA,4HAAE,EAAC,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,SAAS,KAGgC;QAHhC,EAChB,SAAS,EACT,GAAG,OAC6C,GAHhC;IAIhB,qBACE,6LAAC,2KAAkB;QACjB,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,uGACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,YAAY,KAGgC;QAHhC,EACnB,SAAS,EACT,GAAG,OACgD,GAHhC;IAInB,qBACE,6LAAC,8KAAqB;QACpB,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,mqBACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,YAAY,KAGgC;QAHhC,EACnB,SAAS,EACT,GAAG,OACgD,GAHhC;IAInB,qBACE,6LAAC,8KAAqB;QACpB,aAAU;QACV,WAAW,IAAA,4HAAE,EAAC,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 86, "column": 0}, "map": {"version": 3, "sources": ["file:///T:/Development/TranscriptionModule/transcription-demo/src/components/workspace-nav.tsx"], "sourcesContent": ["\"use client\";\n\nimport Link from \"next/link\";\nimport { usePathname } from \"next/navigation\";\nimport { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from \"@/components/ui/tabs\";\n\nconst items = [\n  { href: \"/recording\", label: \"Recording\" },\n  { href: \"/ai/dashboard\", label: \"AI Dashboard\" },\n  { href: \"/report-templates\", label: \"Report Templates\" },\n  { href: \"/templates/editor\", label: \"Template Editor\" },\n];\n\nexport function WorkspaceNav() {\n  const pathname = usePathname();\n  const value = items.find((i) => pathname.startsWith(i.href))?.href ?? \"/recording\";\n  return (\n    <Tabs value={value} className=\"w-full\">\n      <TabsList className=\"mb-4\">\n        {items.map((i) => (\n          <TabsTrigger key={i.href} value={i.href} asChild>\n            <Link href={i.href}>{i.label}</Link>\n          </TabsTrigger>\n        ))}\n      </TabsList>\n    </Tabs>\n  );\n}\n\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;;;AAJA;;;;AAMA,MAAM,QAAQ;IACZ;QAAE,MAAM;QAAc,OAAO;IAAY;IACzC;QAAE,MAAM;QAAiB,OAAO;IAAe;IAC/C;QAAE,MAAM;QAAqB,OAAO;IAAmB;IACvD;QAAE,MAAM;QAAqB,OAAO;IAAkB;CACvD;AAEM,SAAS;QAEA;;IADd,MAAM,WAAW,IAAA,oJAAW;QACd;IAAd,MAAM,QAAQ,CAAA,oBAAA,cAAA,MAAM,IAAI,CAAC,CAAC,IAAM,SAAS,UAAU,CAAC,EAAE,IAAI,gBAA5C,kCAAA,YAAgD,IAAI,cAApD,8BAAA,mBAAwD;IACtE,qBACE,6LAAC,2IAAI;QAAC,OAAO;QAAO,WAAU;kBAC5B,cAAA,6LAAC,+IAAQ;YAAC,WAAU;sBACjB,MAAM,GAAG,CAAC,CAAC,kBACV,6LAAC,kJAAW;oBAAc,OAAO,EAAE,IAAI;oBAAE,OAAO;8BAC9C,cAAA,6LAAC,0KAAI;wBAAC,MAAM,EAAE,IAAI;kCAAG,EAAE,KAAK;;;;;;mBADZ,EAAE,IAAI;;;;;;;;;;;;;;;AAOlC;GAdgB;;QACG,oJAAW;;;KADd", "debugId": null}}, {"offset": {"line": 171, "column": 0}, "map": {"version": 3, "sources": ["file:///T:/Development/TranscriptionModule/transcription-demo/src/lib/convexClient.ts"], "sourcesContent": ["\"use client\";\n\nimport { ConvexHttpClient } from \"convex/browser\";\n\nexport function getConvex(): ConvexHttpClient | null {\n  const url = process.env.NEXT_PUBLIC_CONVEX_URL;\n  if (!url) return null;\n  try {\n    return new ConvexHttpClient(url);\n  } catch {\n    return null;\n  }\n}\n\n"], "names": [], "mappings": ";;;;AAKc;AAHd;AAAA;AAFA;;AAIO,SAAS;IACd,MAAM;IACN;;IACA,IAAI;QACF,OAAO,IAAI,sLAAgB,CAAC;IAC9B,EAAE,UAAM;QACN,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 197, "column": 0}, "map": {"version": 3, "sources": ["file:///T:/Development/TranscriptionModule/transcription-demo/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;;AAEA;;;AAEA,SAAS,MAAM,KAA4D;QAA5D,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC,GAA5D;IACb,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 229, "column": 0}, "map": {"version": 3, "sources": ["file:///T:/Development/TranscriptionModule/transcription-demo/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;;AAEA;;;AAEA,SAAS,SAAS,KAAyD;QAAzD,EAAE,SAAS,EAAE,GAAG,OAAyC,GAAzD;IAChB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,ucACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS", "debugId": null}}, {"offset": {"line": 260, "column": 0}, "map": {"version": 3, "sources": ["file:///T:/Development/TranscriptionModule/transcription-demo/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  showCloseButton = true,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\n  showCloseButton?: boolean\n}) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        {showCloseButton && (\n          <DialogPrimitive.Close\n            data-slot=\"dialog-close\"\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\n          >\n            <XIcon />\n            <span className=\"sr-only\">Close</span>\n          </DialogPrimitive.Close>\n        )}\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,KAEoC;QAFpC,EACd,GAAG,OAC+C,GAFpC;IAGd,qBAAO,6LAAC,6KAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,cAAc,KAEgC;QAFhC,EACrB,GAAG,OACkD,GAFhC;IAGrB,qBAAO,6LAAC,gLAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAJS;AAMT,SAAS,aAAa,KAEgC;QAFhC,EACpB,GAAG,OACiD,GAFhC;IAGpB,qBAAO,6LAAC,+KAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,YAAY,KAEgC;QAFhC,EACnB,GAAG,OACgD,GAFhC;IAGnB,qBAAO,6LAAC,8KAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,KAGgC;QAHhC,EACrB,SAAS,EACT,GAAG,OACkD,GAHhC;IAIrB,qBACE,6LAAC,gLAAuB;QACtB,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,cAAc,KAOtB;QAPsB,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ,GAPsB;IAQrB,qBACE,6LAAC;QAAa,aAAU;;0BACtB,6LAAC;;;;;0BACD,6LAAC,gLAAuB;gBACtB,aAAU;gBACV,WAAW,IAAA,4HAAE,EACX,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,6LAAC,8KAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,6LAAC,4MAAK;;;;;0CACN,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;MAhCS;AAkCT,SAAS,aAAa,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACpB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EAAC,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,aAAa,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACpB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,KAGgC;QAHhC,EACnB,SAAS,EACT,GAAG,OACgD,GAHhC;IAInB,qBACE,6LAAC,8KAAqB;QACpB,aAAU;QACV,WAAW,IAAA,4HAAE,EAAC,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,KAGgC;QAHhC,EACzB,SAAS,EACT,GAAG,OACsD,GAHhC;IAIzB,qBACE,6LAAC,oLAA2B;QAC1B,aAAU;QACV,WAAW,IAAA,4HAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 476, "column": 0}, "map": {"version": 3, "sources": ["file:///T:/Development/TranscriptionModule/transcription-demo/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,KAEoC;QAFpC,EACd,GAAG,OAC+C,GAFpC;IAGd,qBAAO,6LAAC,6KAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,YAAY,KAEgC;QAFhC,EACnB,GAAG,OACgD,GAFhC;IAGnB,qBAAO,6LAAC,8KAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,YAAY,KAEgC;QAFhC,EACnB,GAAG,OACgD,GAFhC;IAGnB,qBAAO,6LAAC,8KAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,KAOtB;QAPsB,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ,GAPsB;IAQrB,qBACE,6LAAC,gLAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,IAAA,4HAAE,EACX,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,6KAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,8OAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MAxBS;AA0BT,SAAS,cAAc,KAKgC;QALhC,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD,GALhC;IAMrB,qBACE,6LAAC,+KAAsB;kBACrB,cAAA,6LAAC,gLAAuB;YACtB,aAAU;YACV,WAAW,IAAA,4HAAE,EACX,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,iLAAwB;oBACvB,WAAW,IAAA,4HAAE,EACX,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,KAGgC;QAHhC,EACnB,SAAS,EACT,GAAG,OACgD,GAHhC;IAInB,qBACE,6LAAC,8KAAqB;QACpB,aAAU;QACV,WAAW,IAAA,4HAAE,EAAC,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,WAAW,KAIgC;QAJhC,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C,GAJhC;IAKlB,qBACE,6LAAC,6KAAoB;QACnB,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,6aACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,sLAA6B;8BAC5B,cAAA,6LAAC,wNAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,6LAAC,iLAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,KAGgC;QAHhC,EACvB,SAAS,EACT,GAAG,OACoD,GAHhC;IAIvB,qBACE,6LAAC,kLAAyB;QACxB,aAAU;QACV,WAAW,IAAA,4HAAE,EAAC,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,KAGgC;QAHhC,EAC5B,SAAS,EACT,GAAG,OACyD,GAHhC;IAI5B,qBACE,6LAAC,uLAA8B;QAC7B,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,wOAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAhBS;AAkBT,SAAS,uBAAuB,KAGgC;QAHhC,EAC9B,SAAS,EACT,GAAG,OAC2D,GAHhC;IAI9B,qBACE,6LAAC,yLAAgC;QAC/B,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,8OAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAhBS", "debugId": null}}, {"offset": {"line": 743, "column": 0}, "map": {"version": 3, "sources": ["file:///T:/Development/TranscriptionModule/transcription-demo/src/app/templates/editor/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useEffect, useState } from \"react\";\nimport type { ReactNode } from \"react\";\n\nimport { WorkspaceNav } from \"@/components/workspace-nav\";\nimport { DndContext, closestCenter, DragEndEvent } from \"@dnd-kit/core\";\nimport { arrayMove, SortableContext, useSortable, verticalListSortingStrategy } from \"@dnd-kit/sortable\";\nimport { CSS } from \"@dnd-kit/utilities\";\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Button } from \"@/components/ui/button\";\nimport { getConvex } from \"@/lib/convexClient\";\nimport { Input } from \"@/components/ui/input\";\nimport { Textarea } from \"@/components/ui/textarea\";\nimport { Dialog, DialogContent, DialogHeader, DialogTitle } from \"@/components/ui/dialog\";\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\";\n\n\nfunction SortableBlock({ id, kind, value, label, onChange, onChangeLabel }: { id: string; kind: string; value: string; label?: string; onChange: (v: string) => void; onChangeLabel?: (v: string) => void }) {\n  const { attributes, listeners, setNodeRef, transform, transition } = useSortable({ id });\n  const style = { transform: CSS.Transform.toString(transform), transition };\n  return (\n    <div ref={setNodeRef} style={style} className=\"rounded border bg-card p-3\" {...attributes} {...listeners}>\n      <div className=\"text-xs mb-1 text-muted-foreground\">{kind}</div>\n      {kind === \"text\" && (\n        <Textarea rows={3} value={value} onChange={(e) => onChange(e.target.value)} />\n      )}\n      {kind === \"section\" && (\n        <Input value={value} onChange={(e) => onChange(e.target.value)} placeholder=\"Section title\" />\n      )}\n      {kind === \"field\" && (\n        <div className=\"grid gap-2\">\n          <Input value={label ?? \"\"} onChange={(e) => onChangeLabel?.(e.target.value)} placeholder=\"Field label\" />\n          <Input value={value} onChange={(e) => onChange(e.target.value)} placeholder=\"Placeholder / notes\" />\n        </div>\n      )}\n    </div>\n  );\n}\n\ntype Template = { id?: string; title: string; blocks: Array<{ id: string; kind: \"text\" | \"field\" | \"section\"; value: string; label?: string; fieldId?: string }>; published: boolean; ownerUserId: string; updatedAt?: number };\n\ntype RawDoc = { _id?: string; id?: string; title?: string; published?: boolean; ownerUserId?: string; updatedAt?: number; blocks?: Array<{ id: string; kind: \"text\" | \"field\" | \"section\"; value: string; label?: string; fieldId?: string }>; };\n\nfunction normalizeTemplate(raw: unknown): Template {\n  const r = raw as RawDoc;\n  const id = r.id ?? (r._id ? String(r._id) : undefined);\n  return {\n    id,\n    title: r.title ?? \"Untitled\",\n    blocks: r.blocks ?? [],\n    published: r.published ?? false,\n    ownerUserId: r.ownerUserId ?? \"demo-user\",\n    updatedAt: r.updatedAt,\n  };\n}\n\nexport default function TemplateEditorPage() {\n  const [title, setTitle] = useState(\"Default Template\");\n  const [blocks, setBlocks] = useState<Array<{ id: string; kind: \"text\" | \"field\" | \"section\"; value: string; label?: string; fieldId?: string }>>([\n    { id: crypto.randomUUID(), kind: \"text\", value: \"HPI:\" },\n    { id: crypto.randomUUID(), kind: \"field\", value: \"\", label: \"Chief Complaint\" },\n  ]);\n  const [templates, setTemplates] = useState<Template[]>([]);\n  const [currentId, setCurrentId] = useState<string | undefined>(undefined);\n  const [fieldOpen, setFieldOpen] = useState(false);\n  const [fieldQ, setFieldQ] = useState(\"\");\n  const [fieldCat, setFieldCat] = useState<string>(\"all\");\n  const [definitions, setDefinitions] = useState<Array<{ id: string; name: string; category: string }>>([]);\n\n  const convex = getConvex();\n  const loose = convex as unknown as {\n    query: (name: string, args?: unknown) => Promise<unknown>;\n    mutation: (name: string, args?: unknown) => Promise<unknown>;\n    action: (name: string, args?: unknown) => Promise<unknown>;\n  };\n\n  useEffect(() => {\n    let ignore = false;\n    async function load() {\n      if (!convex) return;\n      try {\n        const list = await (loose.query(\"templates:listTemplates\", {}) as Promise<unknown>);\n        const items = (list as unknown[]).map(normalizeTemplate);\n        if (!ignore) {\n          setTemplates(items);\n          const first = items[0];\n          if (first) {\n            setCurrentId(first.id);\n            setTitle(first.title);\n            setBlocks(first.blocks);\n          }\n        }\n      } catch {}\n    }\n    load();\n    return () => { ignore = true; };\n  }, [convex, loose]);\n\n  useEffect(() => {\n    let cancelled = false;\n    async function loadDefs() {\n      if (!convex || !fieldOpen) return;\n      try {\n        const res = await loose.query(\"detections:listDefinitions\", { q: fieldQ, category: fieldCat } as { q?: string; category?: string });\n        const arr = (res as unknown[]).map((r) => {\n          const doc = r as { id?: string; _id?: string; name?: string; category?: string };\n          const id = doc.id ?? (doc._id ? String(doc._id) : crypto.randomUUID());\n\n          return { id, name: doc.name ?? \"\", category: doc.category ?? \"\" };\n        });\n        if (!cancelled) setDefinitions(arr);\n      } catch {}\n    }\n    loadDefs();\n    return () => { cancelled = true; };\n  }, [convex, loose, fieldOpen, fieldQ, fieldCat]);\n\n  function onDragEnd(event: DragEndEvent) {\n    const { active, over } = event;\n    if (!over || active.id === over.id) return;\n    const oldIndex = blocks.findIndex((b) => b.id === active.id);\n    const newIndex = blocks.findIndex((b) => b.id === over.id);\n    setBlocks((items) => arrayMove(items, oldIndex, newIndex));\n  }\n\n  // Sample variables for preview rendering\n  const sampleVars = {\n    patient: { name: \"Jane Doe\", id: \"12345\" },\n    clinician: { name: \"Dr. Smith\" },\n    visit: { date: \"2025-09-14\" },\n  } as const;\n  const [varsText, setVarsText] = useState<string>(JSON.stringify(sampleVars, null, 2));\n  function getVars(): unknown { try { return JSON.parse(varsText); } catch { return sampleVars; } }\n  const [preset, setPreset] = useState<string>(\"default\");\n  function setByPath(root: unknown, path: string, value: unknown) {\n    if (!root || typeof root !== \"object\") return;\n    const parts = path.split(\".\");\n    let cur = root as Record<string, unknown>;\n    for (let i = 0; i < parts.length - 1; i++) {\n      const key = parts[i];\n      const next = cur[key];\n      if (!next || typeof next !== \"object\") cur[key] = {};\n      cur = cur[key] as Record<string, unknown>;\n    }\n    cur[parts[parts.length - 1]] = value as unknown;\n  }\n  function setVar(path: string, value: string) {\n    try {\n      const obj = JSON.parse(varsText) as unknown;\n      setByPath(obj, path, value);\n      setVarsText(JSON.stringify(obj, null, 2));\n    } catch {\n      const obj: Record<string, unknown> = JSON.parse(JSON.stringify(sampleVars));\n      setByPath(obj, path, value);\n      setVarsText(JSON.stringify(obj, null, 2));\n    }\n  }\n  function applyPreset(p: string) {\n    setPreset(p);\n    const presets: Record<string, unknown> = {\n      default: sampleVars,\n      pediatric: { patient: { name: \"Sam Lee\", id: \"P-9981\" }, clinician: { name: \"Dr. Rivera\" }, visit: { date: \"2025-10-01\" } },\n      cardiology: { patient: { name: \"Alex Kim\", id: \"C-2103\" }, clinician: { name: \"Dr. Patel\" }, visit: { date: \"2025-09-20\" } },\n    };\n    const obj = presets[p] ?? sampleVars;\n    setVarsText(JSON.stringify(obj, null, 2));\n  }\n  const [exportSpacing, setExportSpacing] = useState<\"spaced\" | \"compact\">(\"spaced\");\n\n\n  function renderRichText(text: string) {\n    const lines = text.split(/\\r?\\n/);\n    const els: ReactNode[] = [];\n    let para: string[] = [];\n    let bullets: string[] = [];\n    let numbers: string[] = [];\n    const flushPara = () => { if (para.length) { els.push(<p className=\"whitespace-pre-wrap\">{para.join(\"\\n\")}</p>); para = []; } };\n    const flushBullets = () => { if (bullets.length) { els.push(<ul className=\"list-disc pl-5\">{bullets.map((t, i) => <li key={`b-${i}`}>{t}</li>)}</ul>); bullets = []; } };\n    const flushNumbers = () => { if (numbers.length) { els.push(<ol className=\"list-decimal pl-5\">{numbers.map((t, i) => <li key={`n-${i}`}>{t}</li>)}</ol>); numbers = []; } };\n    for (const line of lines) {\n      if (/^\\s*-\\s+/.test(line)) { flushPara(); flushNumbers(); bullets.push(line.replace(/^\\s*-\\s+/, \"\")); }\n      else if (/^\\s*\\d+\\.\\s+/.test(line)) { flushPara(); flushBullets(); numbers.push(line.replace(/^\\s*\\d+\\.\\s+/, \"\")); }\n      else if (line.trim() === \"\") { flushPara(); flushBullets(); flushNumbers(); }\n      else { flushBullets(); flushNumbers(); para.push(line); }\n    }\n    flushPara(); flushBullets(); flushNumbers();\n    return <>{els}</>;\n  }\n\n  function buildPlainText(): string {\n    const parts: string[] = [];\n    for (const b of blocks) {\n      if (b.kind === \"section\") parts.push(replaceVars(b.value));\n      else if (b.kind === \"text\") parts.push(replaceVars(b.value));\n      else if (b.kind === \"field\") parts.push(`${b.label ?? b.value}: `);\n    }\n    return parts.join(exportSpacing === \"spaced\" ? \"\\n\\n\" : \"\\n\");\n  }\n\n  async function copyPreview() {\n    try { await navigator.clipboard.writeText(buildPlainText()); } catch {}\n  }\n\n  function downloadTxt() {\n    const blob = new Blob([buildPlainText()], { type: \"text/plain;charset=utf-8\" });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement(\"a\");\n    a.href = url; a.download = `${title || \"template\"}.txt`;\n    document.body.appendChild(a); a.click(); a.remove();\n    URL.revokeObjectURL(url);\n  }\n\n\n  function getByPath(obj: unknown, path: string): unknown {\n    let cur: unknown = obj;\n    for (const key of path.split(\".\")) {\n      if (cur && typeof cur === \"object\" && key in (cur as Record<string, unknown>)) {\n        cur = (cur as Record<string, unknown>)[key];\n      } else {\n        return undefined;\n      }\n    }\n    return cur;\n  }\n\n  function replaceVars(text: string): string {\n    return text.replace(/\\{\\{\\s*([a-zA-Z0-9_.]+)\\s*\\}\\}/g, (_m, p1) => {\n      const v = getByPath(getVars(), String(p1));\n      return typeof v === \"string\" || typeof v === \"number\" ? String(v) : \"\";\n    });\n  }\n\n  return (\n    <div className=\"container mx-auto p-6 space-y-4\">\n      <WorkspaceNav />\n      <div className=\"flex items-center justify-between\">\n        <h1 className=\"text-xl font-semibold\">Template Editor</h1>\n        <div className=\"flex gap-2 items-center\">\n          {convex && (\n            <Button variant=\"outline\" onClick={async () => {\n\n              await loose.action(\"templates:seedTemplates\", {});\n              const list = await (loose.query(\"templates:listTemplates\", {}) as Promise<unknown>);\n              const items = (list as unknown[]).map(normalizeTemplate);\n              setTemplates(items);\n              const first = items[0];\n              if (first) { setCurrentId(first.id); setTitle(first.title); setBlocks(first.blocks); }\n            }}>Seed Templates</Button>\n          )}\n\n          {convex && (\n            <div className=\"flex items-center gap-2\">\n              <span className=\"text-sm text-muted-foreground\">Template</span>\n              <Select value={currentId ?? \"\"} onValueChange={(id) => {\n                setCurrentId(id);\n                const t = templates.find((x) => x.id === id);\n                if (t) { setTitle(t.title); setBlocks(t.blocks); }\n              }}>\n                <SelectTrigger className=\"w-[260px]\"><SelectValue placeholder=\"Select template\" /></SelectTrigger>\n                <SelectContent>\n                  {templates.map((t) => (<SelectItem key={t.id} value={t.id!}>{t.title}</SelectItem>))}\n                </SelectContent>\n              </Select>\n              <Button variant=\"outline\" onClick={() => { setCurrentId(undefined); setTitle(\"Untitled Template\"); setBlocks([]); }}>New</Button>\n              <Button variant=\"outline\" onClick={async () => {\n                if (!currentId) return;\n                const base = templates.find((t) => t.id === currentId);\n                if (!base) return;\n                setCurrentId(undefined);\n                setTitle(`Copy of ${base.title}`);\n                setBlocks(base.blocks.map((b) => ({ ...b, id: crypto.randomUUID() })));\n              }}>Clone</Button>\n              <Button variant=\"destructive\" onClick={async () => {\n                if (convex && currentId) {\n                  await loose.mutation(\"templates:removeTemplate\", { id: currentId });\n                  const list = await (loose.query(\"templates:listTemplates\", {}) as Promise<unknown>);\n                  const items = (list as unknown[]).map(normalizeTemplate);\n                  setTemplates(items);\n                  const first = items[0];\n                  if (first) { setCurrentId(first.id); setTitle(first.title); setBlocks(first.blocks); } else { setCurrentId(undefined); setTitle(\"Untitled Template\"); setBlocks([]); }\n                }\n              }}>Delete</Button>\n            </div>\n          )}\n\n          <Button variant=\"secondary\" onClick={async () => {\n            if (convex) {\n              await loose.mutation(\"templates:upsertTemplate\", { id: currentId, title, blocks, published: false, ownerUserId: \"demo-user\" });\n              const list = await (loose.query(\"templates:listTemplates\", {}) as Promise<unknown>);\n              setTemplates((list as unknown[]).map(normalizeTemplate));\n            }\n          }}>Save</Button>\n          <Button onClick={async () => {\n            if (convex) {\n              await loose.mutation(\"templates:upsertTemplate\", { id: currentId, title, blocks, published: true, ownerUserId: \"demo-user\" });\n              const list = await (loose.query(\"templates:listTemplates\", {}) as Promise<unknown>);\n              setTemplates((list as unknown[]).map(normalizeTemplate));\n            }\n          }}>Publish</Button>\n        </div>\n      </div>\n\n      <Card>\n        <CardHeader className=\"flex items-center justify-between\">\n          <CardTitle className=\"w-full\">\n            <Input value={title} onChange={(e) => setTitle(e.target.value)} className=\"h-9\" />\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-3\">\n          <div className=\"flex gap-2\">\n            <Button variant=\"outline\" onClick={() => setBlocks((b) => [...b, { id: crypto.randomUUID(), kind: \"text\", value: \"\" }])}>+ Text</Button>\n            <Button variant=\"outline\" onClick={() => setBlocks((b) => [...b, { id: crypto.randomUUID(), kind: \"section\", value: \"Section\" }])}>+ Section</Button>\n            <Button variant=\"outline\" onClick={() => setFieldOpen(true)}>+ Field</Button>\n          </div>\n\n          <DndContext collisionDetection={closestCenter} onDragEnd={onDragEnd}>\n            <SortableContext items={blocks.map((b) => b.id)} strategy={verticalListSortingStrategy}>\n              <div className=\"grid gap-2\">\n                {blocks.map((b) => (\n                  <SortableBlock key={b.id} id={b.id} kind={b.kind} value={b.value} label={b.label}\n                    onChange={(v) => setBlocks((all) => all.map((x) => (x.id === b.id ? { ...x, value: v } : x)))}\n                    onChangeLabel={(v) => setBlocks((all) => all.map((x) => (x.id === b.id ? { ...x, label: v } : x)))}\n                  />\n                ))}\n              </div>\n            </SortableContext>\n          </DndContext>\n        </CardContent>\n      </Card>\n\n\n      <Card>\n        <CardHeader>\n          <CardTitle>Variables</CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-3\">\n          <div className=\"flex items-center gap-2\">\n            <span className=\"text-sm text-muted-foreground\">Preset</span>\n            <Select value={preset} onValueChange={(v) => applyPreset(v)}>\n              <SelectTrigger className=\"w-[220px]\"><SelectValue placeholder=\"Select preset\"/></SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"default\">Default</SelectItem>\n                <SelectItem value=\"pediatric\">Pediatric</SelectItem>\n                <SelectItem value=\"cardiology\">Cardiology</SelectItem>\n              </SelectContent>\n            </Select>\n            <Button variant=\"outline\" size=\"sm\" onClick={() => applyPreset(preset)}>Reset to preset</Button>\n          </div>\n\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-2\">\n            <div>\n              <div className=\"text-xs text-muted-foreground\">patient.name</div>\n              <Input value={String(getByPath(getVars(), \"patient.name\") ?? \"\")} onChange={(e) => setVar(\"patient.name\", e.target.value)} />\n            </div>\n            <div>\n              <div className=\"text-xs text-muted-foreground\">patient.id</div>\n              <Input value={String(getByPath(getVars(), \"patient.id\") ?? \"\")} onChange={(e) => setVar(\"patient.id\", e.target.value)} />\n            </div>\n            <div>\n              <div className=\"text-xs text-muted-foreground\">clinician.name</div>\n              <Input value={String(getByPath(getVars(), \"clinician.name\") ?? \"\")} onChange={(e) => setVar(\"clinician.name\", e.target.value)} />\n            </div>\n            <div>\n              <div className=\"text-xs text-muted-foreground\">visit.date</div>\n              <Input value={String(getByPath(getVars(), \"visit.date\") ?? \"\")} onChange={(e) => setVar(\"visit.date\", e.target.value)} />\n            </div>\n          </div>\n\n          <Textarea rows={6} value={varsText} onChange={(e) => setVarsText(e.target.value)} />\n          <div className=\"text-xs text-muted-foreground\">{'Hint: Use {{patient.name}}, {{clinician.name}}, {{visit.date}} in text blocks.'}</div>\n        </CardContent>\n      </Card>\n\n      <Card>\n        <CardHeader className=\"flex items-center justify-between\">\n          <CardTitle>Live Preview</CardTitle>\n          <div className=\"flex items-center gap-2\">\n            <div className=\"hidden sm:flex items-center gap-2\">\n              <span className=\"text-xs text-muted-foreground\">Export spacing</span>\n              <Select value={exportSpacing} onValueChange={(v) => setExportSpacing(v as \"spaced\" | \"compact\")}>\n                <SelectTrigger className=\"h-8 w-[140px]\"><SelectValue placeholder=\"Spacing\"/></SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"spaced\">Spaced</SelectItem>\n                  <SelectItem value=\"compact\">Compact</SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n            <Button variant=\"outline\" size=\"sm\" onClick={copyPreview}>Copy</Button>\n            <Button variant=\"outline\" size=\"sm\" onClick={downloadTxt}>Download .txt</Button>\n          </div>\n        </CardHeader>\n        <CardContent>\n          <div className=\"prose prose-sm max-w-none space-y-2\">\n            {blocks.map((b, i) => (\n              <div key={b.id} className=\"py-1\">\n                {b.kind === \"section\" && (\n                  <>\n                    {i > 0 && <hr className=\"my-3 border-t border-muted/40\" />}\n                    <h3 className=\"text-base font-semibold tracking-tight mt-2 mb-1\">{replaceVars(b.value)}</h3>\n                  </>\n                )}\n                {b.kind === \"text\" && (\n                  renderRichText(replaceVars(b.value))\n                )}\n                {b.kind === \"field\" && (\n                  <p className=\"text-sm\"><strong>{b.label ?? b.value}:</strong> <span className=\"text-muted-foreground\">______</span></p>\n                )}\n              </div>\n            ))}\n          </div>\n        </CardContent>\n      </Card>\n\n\n\n      {/* Field Picker Dialog */}\n      <Dialog open={fieldOpen} onOpenChange={setFieldOpen}>\n        <DialogContent className=\"sm:max-w-[720px]\">\n          <DialogHeader>\n            <DialogTitle>Select a Field</DialogTitle>\n          </DialogHeader>\n          <div className=\"flex gap-2 mb-3\">\n            <Input placeholder=\"Search\" value={fieldQ} onChange={(e) => setFieldQ(e.target.value)} />\n            <Select value={fieldCat} onValueChange={setFieldCat}>\n              <SelectTrigger className=\"w-56\"><SelectValue placeholder=\"All\"/></SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"all\">All Categories</SelectItem>\n                <SelectItem value=\"anamnese\">Anamnese</SelectItem>\n                <SelectItem value=\"antecedents\">Antecedents</SelectItem>\n                <SelectItem value=\"family_history\">Family History</SelectItem>\n                <SelectItem value=\"clinical_exam\">Clinical Exam</SelectItem>\n                <SelectItem value=\"technical_exams\">Technical Exams</SelectItem>\n                <SelectItem value=\"medication\">Medication</SelectItem>\n                <SelectItem value=\"other_therapies\">Other Therapies</SelectItem>\n              </SelectContent>\n            </Select>\n          </div>\n          <div className=\"grid gap-2 max-h-[360px] overflow-auto\">\n            {definitions.map((d) => (\n              <div key={d.id} className=\"flex items-center justify-between rounded border p-3\">\n                <div>\n                  <div className=\"font-medium\">{d.name}</div>\n                  <div className=\"text-xs text-muted-foreground\">{d.category}</div>\n                </div>\n                <Button size=\"sm\" onClick={() => { setBlocks((b) => [...b, { id: crypto.randomUUID(), kind: \"field\", value: \"\", label: d.name, fieldId: d.id }]); setFieldOpen(false); }}>Select</Button>\n              </div>\n            ))}\n          </div>\n        </DialogContent>\n      </Dialog>\n    </div>\n  );\n}\n\n"], "names": [], "mappings": ";;;;;AAEA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAfA;;;;;;;;;;;;;AAkBA,SAAS,cAAc,KAAoL;QAApL,EAAE,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAmI,GAApL;;IACrB,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU,EAAE,GAAG,IAAA,qLAAW,EAAC;QAAE;IAAG;IACtF,MAAM,QAAQ;QAAE,WAAW,+KAAG,CAAC,SAAS,CAAC,QAAQ,CAAC;QAAY;IAAW;IACzE,qBACE,6LAAC;QAAI,KAAK;QAAY,OAAO;QAAO,WAAU;QAA8B,GAAG,UAAU;QAAG,GAAG,SAAS;;0BACtG,6LAAC;gBAAI,WAAU;0BAAsC;;;;;;YACpD,SAAS,wBACR,6LAAC,mJAAQ;gBAAC,MAAM;gBAAG,OAAO;gBAAO,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;;;;;;YAE1E,SAAS,2BACR,6LAAC,6IAAK;gBAAC,OAAO;gBAAO,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;gBAAG,aAAY;;;;;;YAE7E,SAAS,yBACR,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,6IAAK;wBAAC,OAAO,kBAAA,mBAAA,QAAS;wBAAI,UAAU,CAAC,IAAM,0BAAA,oCAAA,cAAgB,EAAE,MAAM,CAAC,KAAK;wBAAG,aAAY;;;;;;kCACzF,6LAAC,6IAAK;wBAAC,OAAO;wBAAO,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;wBAAG,aAAY;;;;;;;;;;;;;;;;;;AAKtF;GApBS;;QAC8D,qLAAW;;;KADzE;AA0BT,SAAS,kBAAkB,GAAY;IACrC,MAAM,IAAI;QACC;IAAX,MAAM,KAAK,CAAA,QAAA,EAAE,EAAE,cAAJ,mBAAA,QAAS,EAAE,GAAG,GAAG,OAAO,EAAE,GAAG,IAAI;QAGnC,UACC,WACG,cACE;IALf,OAAO;QACL;QACA,OAAO,CAAA,WAAA,EAAE,KAAK,cAAP,sBAAA,WAAW;QAClB,QAAQ,CAAA,YAAA,EAAE,MAAM,cAAR,uBAAA,YAAY,EAAE;QACtB,WAAW,CAAA,eAAA,EAAE,SAAS,cAAX,0BAAA,eAAe;QAC1B,aAAa,CAAA,iBAAA,EAAE,WAAW,cAAb,4BAAA,iBAAiB;QAC9B,WAAW,EAAE,SAAS;IACxB;AACF;AAEe,SAAS;;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,yKAAQ,EAAC;IACnC,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,yKAAQ,EAA6G;QAC/I;YAAE,IAAI,OAAO,UAAU;YAAI,MAAM;YAAQ,OAAO;QAAO;QACvD;YAAE,IAAI,OAAO,UAAU;YAAI,MAAM;YAAS,OAAO;YAAI,OAAO;QAAkB;KAC/E;IACD,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,yKAAQ,EAAa,EAAE;IACzD,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,yKAAQ,EAAqB;IAC/D,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,yKAAQ,EAAC;IAC3C,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,yKAAQ,EAAC;IACrC,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,yKAAQ,EAAS;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,yKAAQ,EAAwD,EAAE;IAExG,MAAM,SAAS,IAAA,0IAAS;IACxB,MAAM,QAAQ;IAMd,IAAA,0KAAS;wCAAC;YACR,IAAI,SAAS;YACb,eAAe;gBACb,IAAI,CAAC,QAAQ;gBACb,IAAI;oBACF,MAAM,OAAO,MAAO,MAAM,KAAK,CAAC,2BAA2B,CAAC;oBAC5D,MAAM,QAAQ,AAAC,KAAmB,GAAG,CAAC;oBACtC,IAAI,CAAC,QAAQ;wBACX,aAAa;wBACb,MAAM,QAAQ,KAAK,CAAC,EAAE;wBACtB,IAAI,OAAO;4BACT,aAAa,MAAM,EAAE;4BACrB,SAAS,MAAM,KAAK;4BACpB,UAAU,MAAM,MAAM;wBACxB;oBACF;gBACF,EAAE,UAAM,CAAC;YACX;YACA;YACA;gDAAO;oBAAQ,SAAS;gBAAM;;QAChC;uCAAG;QAAC;QAAQ;KAAM;IAElB,IAAA,0KAAS;wCAAC;YACR,IAAI,YAAY;YAChB,eAAe;gBACb,IAAI,CAAC,UAAU,CAAC,WAAW;gBAC3B,IAAI;oBACF,MAAM,MAAM,MAAM,MAAM,KAAK,CAAC,8BAA8B;wBAAE,GAAG;wBAAQ,UAAU;oBAAS;oBAC5F,MAAM,MAAM,AAAC,IAAkB,GAAG;qEAAC,CAAC;4BAClC,MAAM,MAAM;gCACD;4BAAX,MAAM,KAAK,CAAA,UAAA,IAAI,EAAE,cAAN,qBAAA,UAAW,IAAI,GAAG,GAAG,OAAO,IAAI,GAAG,IAAI,OAAO,UAAU;gCAEhD,WAA0B;4BAA7C,OAAO;gCAAE;gCAAI,MAAM,CAAA,YAAA,IAAI,IAAI,cAAR,uBAAA,YAAY;gCAAI,UAAU,CAAA,gBAAA,IAAI,QAAQ,cAAZ,2BAAA,gBAAgB;4BAAG;wBAClE;;oBACA,IAAI,CAAC,WAAW,eAAe;gBACjC,EAAE,UAAM,CAAC;YACX;YACA;YACA;gDAAO;oBAAQ,YAAY;gBAAM;;QACnC;uCAAG;QAAC;QAAQ;QAAO;QAAW;QAAQ;KAAS;IAE/C,SAAS,UAAU,KAAmB;QACpC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG;QACzB,IAAI,CAAC,QAAQ,OAAO,EAAE,KAAK,KAAK,EAAE,EAAE;QACpC,MAAM,WAAW,OAAO,SAAS,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,OAAO,EAAE;QAC3D,MAAM,WAAW,OAAO,SAAS,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,KAAK,EAAE;QACzD,UAAU,CAAC,QAAU,IAAA,mLAAS,EAAC,OAAO,UAAU;IAClD;IAEA,yCAAyC;IACzC,MAAM,aAAa;QACjB,SAAS;YAAE,MAAM;YAAY,IAAI;QAAQ;QACzC,WAAW;YAAE,MAAM;QAAY;QAC/B,OAAO;YAAE,MAAM;QAAa;IAC9B;IACA,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,yKAAQ,EAAS,KAAK,SAAS,CAAC,YAAY,MAAM;IAClF,SAAS;QAAqB,IAAI;YAAE,OAAO,KAAK,KAAK,CAAC;QAAW,EAAE,UAAM;YAAE,OAAO;QAAY;IAAE;IAChG,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,yKAAQ,EAAS;IAC7C,SAAS,UAAU,IAAa,EAAE,IAAY,EAAE,KAAc;QAC5D,IAAI,CAAC,QAAQ,OAAO,SAAS,UAAU;QACvC,MAAM,QAAQ,KAAK,KAAK,CAAC;QACzB,IAAI,MAAM;QACV,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,GAAG,GAAG,IAAK;YACzC,MAAM,MAAM,KAAK,CAAC,EAAE;YACpB,MAAM,OAAO,GAAG,CAAC,IAAI;YACrB,IAAI,CAAC,QAAQ,OAAO,SAAS,UAAU,GAAG,CAAC,IAAI,GAAG,CAAC;YACnD,MAAM,GAAG,CAAC,IAAI;QAChB;QACA,GAAG,CAAC,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,CAAC,GAAG;IACjC;IACA,SAAS,OAAO,IAAY,EAAE,KAAa;QACzC,IAAI;YACF,MAAM,MAAM,KAAK,KAAK,CAAC;YACvB,UAAU,KAAK,MAAM;YACrB,YAAY,KAAK,SAAS,CAAC,KAAK,MAAM;QACxC,EAAE,UAAM;YACN,MAAM,MAA+B,KAAK,KAAK,CAAC,KAAK,SAAS,CAAC;YAC/D,UAAU,KAAK,MAAM;YACrB,YAAY,KAAK,SAAS,CAAC,KAAK,MAAM;QACxC;IACF;IACA,SAAS,YAAY,CAAS;QAC5B,UAAU;QACV,MAAM,UAAmC;YACvC,SAAS;YACT,WAAW;gBAAE,SAAS;oBAAE,MAAM;oBAAW,IAAI;gBAAS;gBAAG,WAAW;oBAAE,MAAM;gBAAa;gBAAG,OAAO;oBAAE,MAAM;gBAAa;YAAE;YAC1H,YAAY;gBAAE,SAAS;oBAAE,MAAM;oBAAY,IAAI;gBAAS;gBAAG,WAAW;oBAAE,MAAM;gBAAY;gBAAG,OAAO;oBAAE,MAAM;gBAAa;YAAE;QAC7H;YACY;QAAZ,MAAM,MAAM,CAAA,aAAA,OAAO,CAAC,EAAE,cAAV,wBAAA,aAAc;QAC1B,YAAY,KAAK,SAAS,CAAC,KAAK,MAAM;IACxC;IACA,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,yKAAQ,EAAuB;IAGzE,SAAS,eAAe,IAAY;QAClC,MAAM,QAAQ,KAAK,KAAK,CAAC;QACzB,MAAM,MAAmB,EAAE;QAC3B,IAAI,OAAiB,EAAE;QACvB,IAAI,UAAoB,EAAE;QAC1B,IAAI,UAAoB,EAAE;QAC1B,MAAM,YAAY;YAAQ,IAAI,KAAK,MAAM,EAAE;gBAAE,IAAI,IAAI,eAAC,6LAAC;oBAAE,WAAU;8BAAuB,KAAK,IAAI,CAAC;;;;;;gBAAa,OAAO,EAAE;YAAE;QAAE;QAC9H,MAAM,eAAe;YAAQ,IAAI,QAAQ,MAAM,EAAE;gBAAE,IAAI,IAAI,eAAC,6LAAC;oBAAG,WAAU;8BAAkB,QAAQ,GAAG,CAAC,CAAC,GAAG,kBAAM,6LAAC;sCAAmB;2BAAX,AAAC,KAAM,OAAF;;;;;;;;;;gBAAuB,UAAU,EAAE;YAAE;QAAE;QACvK,MAAM,eAAe;YAAQ,IAAI,QAAQ,MAAM,EAAE;gBAAE,IAAI,IAAI,eAAC,6LAAC;oBAAG,WAAU;8BAAqB,QAAQ,GAAG,CAAC,CAAC,GAAG,kBAAM,6LAAC;sCAAmB;2BAAX,AAAC,KAAM,OAAF;;;;;;;;;;gBAAuB,UAAU,EAAE;YAAE;QAAE;QAC1K,KAAK,MAAM,QAAQ,MAAO;YACxB,IAAI,WAAW,IAAI,CAAC,OAAO;gBAAE;gBAAa;gBAAgB,QAAQ,IAAI,CAAC,KAAK,OAAO,CAAC,YAAY;YAAM,OACjG,IAAI,eAAe,IAAI,CAAC,OAAO;gBAAE;gBAAa;gBAAgB,QAAQ,IAAI,CAAC,KAAK,OAAO,CAAC,gBAAgB;YAAM,OAC9G,IAAI,KAAK,IAAI,OAAO,IAAI;gBAAE;gBAAa;gBAAgB;YAAgB,OACvE;gBAAE;gBAAgB;gBAAgB,KAAK,IAAI,CAAC;YAAO;QAC1D;QACA;QAAa;QAAgB;QAC7B,qBAAO;sBAAG;;IACZ;IAEA,SAAS;QACP,MAAM,QAAkB,EAAE;QAC1B,KAAK,MAAM,KAAK,OAAQ;gBAGqB;YAF3C,IAAI,EAAE,IAAI,KAAK,WAAW,MAAM,IAAI,CAAC,YAAY,EAAE,KAAK;iBACnD,IAAI,EAAE,IAAI,KAAK,QAAQ,MAAM,IAAI,CAAC,YAAY,EAAE,KAAK;iBACrD,IAAI,EAAE,IAAI,KAAK,SAAS,MAAM,IAAI,CAAC,AAAC,GAAqB,OAAnB,CAAA,WAAA,EAAE,KAAK,cAAP,sBAAA,WAAW,EAAE,KAAK,EAAC;QAChE;QACA,OAAO,MAAM,IAAI,CAAC,kBAAkB,WAAW,SAAS;IAC1D;IAEA,eAAe;QACb,IAAI;YAAE,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;QAAmB,EAAE,UAAM,CAAC;IACxE;IAEA,SAAS;QACP,MAAM,OAAO,IAAI,KAAK;YAAC;SAAiB,EAAE;YAAE,MAAM;QAA2B;QAC7E,MAAM,MAAM,IAAI,eAAe,CAAC;QAChC,MAAM,IAAI,SAAS,aAAa,CAAC;QACjC,EAAE,IAAI,GAAG;QAAK,EAAE,QAAQ,GAAG,AAAC,GAAsB,OAApB,SAAS,YAAW;QAClD,SAAS,IAAI,CAAC,WAAW,CAAC;QAAI,EAAE,KAAK;QAAI,EAAE,MAAM;QACjD,IAAI,eAAe,CAAC;IACtB;IAGA,SAAS,UAAU,GAAY,EAAE,IAAY;QAC3C,IAAI,MAAe;QACnB,KAAK,MAAM,OAAO,KAAK,KAAK,CAAC,KAAM;YACjC,IAAI,OAAO,OAAO,QAAQ,YAAY,OAAQ,KAAiC;gBAC7E,MAAM,AAAC,GAA+B,CAAC,IAAI;YAC7C,OAAO;gBACL,OAAO;YACT;QACF;QACA,OAAO;IACT;IAEA,SAAS,YAAY,IAAY;QAC/B,OAAO,KAAK,OAAO,CAAC,mCAAmC,CAAC,IAAI;YAC1D,MAAM,IAAI,UAAU,WAAW,OAAO;YACtC,OAAO,OAAO,MAAM,YAAY,OAAO,MAAM,WAAW,OAAO,KAAK;QACtE;IACF;QA0HiC,YAIA,aAIA,aAIA;IApIjC,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,yJAAY;;;;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAwB;;;;;;kCACtC,6LAAC;wBAAI,WAAU;;4BACZ,wBACC,6LAAC,+IAAM;gCAAC,SAAQ;gCAAU,SAAS;oCAEjC,MAAM,MAAM,MAAM,CAAC,2BAA2B,CAAC;oCAC/C,MAAM,OAAO,MAAO,MAAM,KAAK,CAAC,2BAA2B,CAAC;oCAC5D,MAAM,QAAQ,AAAC,KAAmB,GAAG,CAAC;oCACtC,aAAa;oCACb,MAAM,QAAQ,KAAK,CAAC,EAAE;oCACtB,IAAI,OAAO;wCAAE,aAAa,MAAM,EAAE;wCAAG,SAAS,MAAM,KAAK;wCAAG,UAAU,MAAM,MAAM;oCAAG;gCACvF;0CAAG;;;;;;4BAGJ,wBACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAgC;;;;;;kDAChD,6LAAC,+IAAM;wCAAC,OAAO,sBAAA,uBAAA,YAAa;wCAAI,eAAe,CAAC;4CAC9C,aAAa;4CACb,MAAM,IAAI,UAAU,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;4CACzC,IAAI,GAAG;gDAAE,SAAS,EAAE,KAAK;gDAAG,UAAU,EAAE,MAAM;4CAAG;wCACnD;;0DACE,6LAAC,sJAAa;gDAAC,WAAU;0DAAY,cAAA,6LAAC,oJAAW;oDAAC,aAAY;;;;;;;;;;;0DAC9D,6LAAC,sJAAa;0DACX,UAAU,GAAG,CAAC,CAAC,kBAAO,6LAAC,mJAAU;wDAAY,OAAO,EAAE,EAAE;kEAAI,EAAE,KAAK;uDAA5B,EAAE,EAAE;;;;;;;;;;;;;;;;kDAGhD,6LAAC,+IAAM;wCAAC,SAAQ;wCAAU,SAAS;4CAAQ,aAAa;4CAAY,SAAS;4CAAsB,UAAU,EAAE;wCAAG;kDAAG;;;;;;kDACrH,6LAAC,+IAAM;wCAAC,SAAQ;wCAAU,SAAS;4CACjC,IAAI,CAAC,WAAW;4CAChB,MAAM,OAAO,UAAU,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;4CAC5C,IAAI,CAAC,MAAM;4CACX,aAAa;4CACb,SAAS,AAAC,WAAqB,OAAX,KAAK,KAAK;4CAC9B,UAAU,KAAK,MAAM,CAAC,GAAG,CAAC,CAAC,IAAM,CAAC;oDAAE,GAAG,CAAC;oDAAE,IAAI,OAAO,UAAU;gDAAG,CAAC;wCACrE;kDAAG;;;;;;kDACH,6LAAC,+IAAM;wCAAC,SAAQ;wCAAc,SAAS;4CACrC,IAAI,UAAU,WAAW;gDACvB,MAAM,MAAM,QAAQ,CAAC,4BAA4B;oDAAE,IAAI;gDAAU;gDACjE,MAAM,OAAO,MAAO,MAAM,KAAK,CAAC,2BAA2B,CAAC;gDAC5D,MAAM,QAAQ,AAAC,KAAmB,GAAG,CAAC;gDACtC,aAAa;gDACb,MAAM,QAAQ,KAAK,CAAC,EAAE;gDACtB,IAAI,OAAO;oDAAE,aAAa,MAAM,EAAE;oDAAG,SAAS,MAAM,KAAK;oDAAG,UAAU,MAAM,MAAM;gDAAG,OAAO;oDAAE,aAAa;oDAAY,SAAS;oDAAsB,UAAU,EAAE;gDAAG;4CACvK;wCACF;kDAAG;;;;;;;;;;;;0CAIP,6LAAC,+IAAM;gCAAC,SAAQ;gCAAY,SAAS;oCACnC,IAAI,QAAQ;wCACV,MAAM,MAAM,QAAQ,CAAC,4BAA4B;4CAAE,IAAI;4CAAW;4CAAO;4CAAQ,WAAW;4CAAO,aAAa;wCAAY;wCAC5H,MAAM,OAAO,MAAO,MAAM,KAAK,CAAC,2BAA2B,CAAC;wCAC5D,aAAa,AAAC,KAAmB,GAAG,CAAC;oCACvC;gCACF;0CAAG;;;;;;0CACH,6LAAC,+IAAM;gCAAC,SAAS;oCACf,IAAI,QAAQ;wCACV,MAAM,MAAM,QAAQ,CAAC,4BAA4B;4CAAE,IAAI;4CAAW;4CAAO;4CAAQ,WAAW;4CAAM,aAAa;wCAAY;wCAC3H,MAAM,OAAO,MAAO,MAAM,KAAK,CAAC,2BAA2B,CAAC;wCAC5D,aAAa,AAAC,KAAmB,GAAG,CAAC;oCACvC;gCACF;0CAAG;;;;;;;;;;;;;;;;;;0BAIP,6LAAC,2IAAI;;kCACH,6LAAC,iJAAU;wBAAC,WAAU;kCACpB,cAAA,6LAAC,gJAAS;4BAAC,WAAU;sCACnB,cAAA,6LAAC,6IAAK;gCAAC,OAAO;gCAAO,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;gCAAG,WAAU;;;;;;;;;;;;;;;;kCAG9E,6LAAC,kJAAW;wBAAC,WAAU;;0CACrB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+IAAM;wCAAC,SAAQ;wCAAU,SAAS,IAAM,UAAU,CAAC,IAAM;uDAAI;oDAAG;wDAAE,IAAI,OAAO,UAAU;wDAAI,MAAM;wDAAQ,OAAO;oDAAG;iDAAE;kDAAG;;;;;;kDACzH,6LAAC,+IAAM;wCAAC,SAAQ;wCAAU,SAAS,IAAM,UAAU,CAAC,IAAM;uDAAI;oDAAG;wDAAE,IAAI,OAAO,UAAU;wDAAI,MAAM;wDAAW,OAAO;oDAAU;iDAAE;kDAAG;;;;;;kDACnI,6LAAC,+IAAM;wCAAC,SAAQ;wCAAU,SAAS,IAAM,aAAa;kDAAO;;;;;;;;;;;;0CAG/D,6LAAC,4KAAU;gCAAC,oBAAoB,+KAAa;gCAAE,WAAW;0CACxD,cAAA,6LAAC,yLAAe;oCAAC,OAAO,OAAO,GAAG,CAAC,CAAC,IAAM,EAAE,EAAE;oCAAG,UAAU,qMAA2B;8CACpF,cAAA,6LAAC;wCAAI,WAAU;kDACZ,OAAO,GAAG,CAAC,CAAC,kBACX,6LAAC;gDAAyB,IAAI,EAAE,EAAE;gDAAE,MAAM,EAAE,IAAI;gDAAE,OAAO,EAAE,KAAK;gDAAE,OAAO,EAAE,KAAK;gDAC9E,UAAU,CAAC,IAAM,UAAU,CAAC,MAAQ,IAAI,GAAG,CAAC,CAAC,IAAO,EAAE,EAAE,KAAK,EAAE,EAAE,GAAG;gEAAE,GAAG,CAAC;gEAAE,OAAO;4DAAE,IAAI;gDACzF,eAAe,CAAC,IAAM,UAAU,CAAC,MAAQ,IAAI,GAAG,CAAC,CAAC,IAAO,EAAE,EAAE,KAAK,EAAE,EAAE,GAAG;gEAAE,GAAG,CAAC;gEAAE,OAAO;4DAAE,IAAI;+CAF5E,EAAE,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAYpC,6LAAC,2IAAI;;kCACH,6LAAC,iJAAU;kCACT,cAAA,6LAAC,gJAAS;sCAAC;;;;;;;;;;;kCAEb,6LAAC,kJAAW;wBAAC,WAAU;;0CACrB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAgC;;;;;;kDAChD,6LAAC,+IAAM;wCAAC,OAAO;wCAAQ,eAAe,CAAC,IAAM,YAAY;;0DACvD,6LAAC,sJAAa;gDAAC,WAAU;0DAAY,cAAA,6LAAC,oJAAW;oDAAC,aAAY;;;;;;;;;;;0DAC9D,6LAAC,sJAAa;;kEACZ,6LAAC,mJAAU;wDAAC,OAAM;kEAAU;;;;;;kEAC5B,6LAAC,mJAAU;wDAAC,OAAM;kEAAY;;;;;;kEAC9B,6LAAC,mJAAU;wDAAC,OAAM;kEAAa;;;;;;;;;;;;;;;;;;kDAGnC,6LAAC,+IAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,SAAS,IAAM,YAAY;kDAAS;;;;;;;;;;;;0CAG1E,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAI,WAAU;0DAAgC;;;;;;0DAC/C,6LAAC,6IAAK;gDAAC,OAAO,OAAO,CAAA,aAAA,UAAU,WAAW,6BAArB,wBAAA,aAAwC;gDAAK,UAAU,CAAC,IAAM,OAAO,gBAAgB,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;kDAE1H,6LAAC;;0DACC,6LAAC;gDAAI,WAAU;0DAAgC;;;;;;0DAC/C,6LAAC,6IAAK;gDAAC,OAAO,OAAO,CAAA,cAAA,UAAU,WAAW,2BAArB,yBAAA,cAAsC;gDAAK,UAAU,CAAC,IAAM,OAAO,cAAc,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;kDAEtH,6LAAC;;0DACC,6LAAC;gDAAI,WAAU;0DAAgC;;;;;;0DAC/C,6LAAC,6IAAK;gDAAC,OAAO,OAAO,CAAA,cAAA,UAAU,WAAW,+BAArB,yBAAA,cAA0C;gDAAK,UAAU,CAAC,IAAM,OAAO,kBAAkB,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;kDAE9H,6LAAC;;0DACC,6LAAC;gDAAI,WAAU;0DAAgC;;;;;;0DAC/C,6LAAC,6IAAK;gDAAC,OAAO,OAAO,CAAA,cAAA,UAAU,WAAW,2BAArB,yBAAA,cAAsC;gDAAK,UAAU,CAAC,IAAM,OAAO,cAAc,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;;0CAIxH,6LAAC,mJAAQ;gCAAC,MAAM;gCAAG,OAAO;gCAAU,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;;;;;;0CAC/E,6LAAC;gCAAI,WAAU;0CAAiC;;;;;;;;;;;;;;;;;;0BAIpD,6LAAC,2IAAI;;kCACH,6LAAC,iJAAU;wBAAC,WAAU;;0CACpB,6LAAC,gJAAS;0CAAC;;;;;;0CACX,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAgC;;;;;;0DAChD,6LAAC,+IAAM;gDAAC,OAAO;gDAAe,eAAe,CAAC,IAAM,iBAAiB;;kEACnE,6LAAC,sJAAa;wDAAC,WAAU;kEAAgB,cAAA,6LAAC,oJAAW;4DAAC,aAAY;;;;;;;;;;;kEAClE,6LAAC,sJAAa;;0EACZ,6LAAC,mJAAU;gEAAC,OAAM;0EAAS;;;;;;0EAC3B,6LAAC,mJAAU;gEAAC,OAAM;0EAAU;;;;;;;;;;;;;;;;;;;;;;;;kDAIlC,6LAAC,+IAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,SAAS;kDAAa;;;;;;kDAC1D,6LAAC,+IAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,SAAS;kDAAa;;;;;;;;;;;;;;;;;;kCAG9D,6LAAC,kJAAW;kCACV,cAAA,6LAAC;4BAAI,WAAU;sCACZ,OAAO,GAAG,CAAC,CAAC,GAAG;oCAYsB;qDAXpC,6LAAC;oCAAe,WAAU;;wCACvB,EAAE,IAAI,KAAK,2BACV;;gDACG,IAAI,mBAAK,6LAAC;oDAAG,WAAU;;;;;;8DACxB,6LAAC;oDAAG,WAAU;8DAAoD,YAAY,EAAE,KAAK;;;;;;;;wCAGxF,EAAE,IAAI,KAAK,UACV,eAAe,YAAY,EAAE,KAAK;wCAEnC,EAAE,IAAI,KAAK,yBACV,6LAAC;4CAAE,WAAU;;8DAAU,6LAAC;;wDAAQ,CAAA,WAAA,EAAE,KAAK,cAAP,sBAAA,WAAW,EAAE,KAAK;wDAAC;;;;;;;gDAAU;8DAAC,6LAAC;oDAAK,WAAU;8DAAwB;;;;;;;;;;;;;mCAXhG,EAAE,EAAE;;;;;;;;;;;;;;;;;;;;;;0BAsBtB,6LAAC,+IAAM;gBAAC,MAAM;gBAAW,cAAc;0BACrC,cAAA,6LAAC,sJAAa;oBAAC,WAAU;;sCACvB,6LAAC,qJAAY;sCACX,cAAA,6LAAC,oJAAW;0CAAC;;;;;;;;;;;sCAEf,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6IAAK;oCAAC,aAAY;oCAAS,OAAO;oCAAQ,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;;;;;;8CACpF,6LAAC,+IAAM;oCAAC,OAAO;oCAAU,eAAe;;sDACtC,6LAAC,sJAAa;4CAAC,WAAU;sDAAO,cAAA,6LAAC,oJAAW;gDAAC,aAAY;;;;;;;;;;;sDACzD,6LAAC,sJAAa;;8DACZ,6LAAC,mJAAU;oDAAC,OAAM;8DAAM;;;;;;8DACxB,6LAAC,mJAAU;oDAAC,OAAM;8DAAW;;;;;;8DAC7B,6LAAC,mJAAU;oDAAC,OAAM;8DAAc;;;;;;8DAChC,6LAAC,mJAAU;oDAAC,OAAM;8DAAiB;;;;;;8DACnC,6LAAC,mJAAU;oDAAC,OAAM;8DAAgB;;;;;;8DAClC,6LAAC,mJAAU;oDAAC,OAAM;8DAAkB;;;;;;8DACpC,6LAAC,mJAAU;oDAAC,OAAM;8DAAa;;;;;;8DAC/B,6LAAC,mJAAU;oDAAC,OAAM;8DAAkB;;;;;;;;;;;;;;;;;;;;;;;;sCAI1C,6LAAC;4BAAI,WAAU;sCACZ,YAAY,GAAG,CAAC,CAAC,kBAChB,6LAAC;oCAAe,WAAU;;sDACxB,6LAAC;;8DACC,6LAAC;oDAAI,WAAU;8DAAe,EAAE,IAAI;;;;;;8DACpC,6LAAC;oDAAI,WAAU;8DAAiC,EAAE,QAAQ;;;;;;;;;;;;sDAE5D,6LAAC,+IAAM;4CAAC,MAAK;4CAAK,SAAS;gDAAQ,UAAU,CAAC,IAAM;2DAAI;wDAAG;4DAAE,IAAI,OAAO,UAAU;4DAAI,MAAM;4DAAS,OAAO;4DAAI,OAAO,EAAE,IAAI;4DAAE,SAAS,EAAE,EAAE;wDAAC;qDAAE;gDAAG,aAAa;4CAAQ;sDAAG;;;;;;;mCALlK,EAAE,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AAa5B;IA5YwB;MAAA", "debugId": null}}]}