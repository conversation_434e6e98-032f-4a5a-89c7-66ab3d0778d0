import { CanvasElementData, BlockType } from './types';
import { MOTOR_STRENGTH_OPTIONS, REFLEX_OPTIONS } from './constants';

export const INITIAL_CLINICAL_LETTER_DATA: CanvasElementData[][] = [
  [
    { id: 'text-date', blockType: BlockType.Text, content: 'Date: ' },
    { id: 'var-date', blockType: BlockType.Variable, name: 'Consultation Date' },
  ],
  [
    { id: 'text-re', blockType: BlockType.Text, content: 'RE: ' },
    { id: 'var-patient-name', blockType: BlockType.Variable, name: 'Patient Name' },
  ],
  [
    { id: 'text-dob', blockType: BlockType.Text, content: 'DOB: ' },
    { id: 'var-dob', blockType: BlockType.Variable, name: 'Patient DOB' },
  ],
  [{ id: 'text-blank-1', blockType: BlockType.Text, content: '' }],
  [
    { id: 'text-dear', blockType: BlockType.Text, content: 'Dear Dr. ' },
    { id: 'var-referring-md', blockType: BlockType.Variable, name: '<PERSON>fer<PERSON> Physician' },
    { id: 'text-comma', blockType: BlockType.Text, content: ',' },
  ],
  [{ id: 'text-blank-2', blockType: BlockType.Text, content: '' }],
  [
    { id: 'text-intro-1', blockType: BlockType.Text, content: 'Thank you for referring ' },
    { id: 'var-patient-name-2', blockType: BlockType.Variable, name: 'Patient Name' },
    { id: 'text-intro-2', blockType: BlockType.Text, content: ', a ' },
    { id: 'var-patient-age', blockType: BlockType.Variable, name: 'Patient Age' },
    { id: 'text-intro-3', blockType: BlockType.Text, content: '-year-old, for evaluation of ' },
    { id: 'var-chief-complaint', blockType: BlockType.Variable, name: 'Chief Complaint' },
    { id: 'text-intro-4', blockType: BlockType.Text, content: '.' },
  ],
  [{ id: 'text-blank-3', blockType: BlockType.Text, content: '' }],
  [
    { id: 'text-hpi-header', blockType: BlockType.Text, content: 'History of Present Illness:' },
  ],
  [
    {
      id: 'ai-hpi-summary',
      blockType: BlockType.AIInstruction,
      parts: [
        [
          { id: 'text-ai-hpi', blockType: BlockType.Text, content: 'Summarize the HPI section of the transcript, focusing on onset, duration, and character of the pain.' },
        ],
      ],
    },
  ],
  [{ id: 'text-blank-4', blockType: BlockType.Text, content: '' }],
  [
    { id: 'text-exam-header', blockType: BlockType.Text, content: 'Physical Examination:' },
  ],
  [
    { id: 'text-exam-intro', blockType: BlockType.Text, content: 'On examination, the patient was alert and oriented. Neurological exam revealed the following:' },
  ],
  [
    { id: 'text-motor', blockType: BlockType.Text, content: 'Motor Strength: ' },
    {
      id: 'cat-motor',
      blockType: BlockType.Category,
      category: 'Motor Strength',
      options: MOTOR_STRENGTH_OPTIONS,
      selected: [MOTOR_STRENGTH_OPTIONS[0]],
    },
    { id: 'text-motor-end', blockType: BlockType.Text, content: '.' },
  ],
  [
    { id: 'text-reflexes', blockType: BlockType.Text, content: 'Reflexes: ' },
    {
      id: 'cat-reflexes',
      blockType: BlockType.Category,
      category: 'Reflexes',
      options: REFLEX_OPTIONS,
      selected: [REFLEX_OPTIONS[0], REFLEX_OPTIONS[3]],
    },
    { id: 'text-reflexes-end', blockType: BlockType.Text, content: '.' },
  ],
  [{ id: 'text-blank-5', blockType: BlockType.Text, content: '' }],
  [
    { id: 'text-assessment-header', blockType: BlockType.Text, content: 'Assessment and Plan:' },
  ],
  [
    {
      id: 'ai-plan-summary',
      blockType: BlockType.AIInstruction,
      parts: [
        [
          { id: 'text-ai-plan-1', blockType: BlockType.Text, content: 'Based on the transcript, extract the primary diagnosis and list the treatment plan discussed. Include ' },
          { id: 'var-plan-details', blockType: BlockType.Variable, name: 'Specific Plan Details' },
          { id: 'text-ai-plan-2', blockType: BlockType.Text, content: '.' },
        ],
      ],
    },
  ],
  [{ id: 'text-blank-6', blockType: BlockType.Text, content: '' }],
  [
    { id: 'text-closing', blockType: BlockType.Text, content: 'Thank you again for this kind referral.' },
  ],
  [{ id: 'text-blank-7', blockType: BlockType.Text, content: '' }],
  [
    { id: 'text-sincerely', blockType: BlockType.Text, content: 'Sincerely,' },
  ],
  [{ id: 'text-blank-8', blockType: BlockType.Text, content: '' }],
  [
    { id: 'var-doctor-name', blockType: BlockType.Variable, name: 'Doctor Name' },
  ],
];
