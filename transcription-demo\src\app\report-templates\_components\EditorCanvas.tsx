"use client";

import { useEffect, useRef, useState } from "react";
import { createRoot, Root } from "react-dom/client";
import { Toolbar } from "./Toolbar";
import { CategoryBlock as CategoryBlockCmp } from "./blocks/CategoryBlock";
import { useDragInsertionCursor } from "./hooks/useDragInsertionCursor";
import type { Block, DetectionItem, AiBlock, CategoryBlock, TextBlock, FindingBlock } from "./types";

export function EditorCanvas({ blocks, setBlocks, detectionItems }: { blocks: Block[]; setBlocks: (updater: (prev: Block[]) => Block[]) => void; detectionItems: DetectionItem[]; }) {
  const editorRef = useRef<HTMLDivElement>(null);
  const [isDragging, setIsDragging] = useState(false);
  const rootsRef = useRef<Map<string, Root>>(new Map());

  // Use the drag insertion cursor hook
  const { insertionPoint, updateInsertionPoint, resetInsertionPoint } = useDragInsertionCursor(
    editorRef as React.RefObject<HTMLElement>,
    isDragging,
    { blinkInterval: 500, cooldownTime: 800 }
  );

  // Get text content from blocks for the single contentEditable area
  const textContent = blocks
    .filter(b => b.type === "TEXT")
    .map(b => (b as TextBlock).content)
    .join("");

  // Initialize editor with basic content and setup drag handlers
  useEffect(() => {
    const editor = editorRef.current;
    if (!editor) return;

    // Initialize with basic content if empty
    if (editor.innerHTML.trim() === "" && textContent.trim() === "") {
      editor.innerHTML = "<p>Start typing your report here...</p>";
    } else if (textContent && editor.innerHTML !== textContent) {
      editor.innerHTML = textContent;
    }

    // Make inline finding chips draggable with proper cleanup
    const findingChips = editor.querySelectorAll('[data-inline-id]');
    findingChips.forEach((chip) => {
      const element = chip as HTMLElement;
      element.draggable = true;

      // Remove any existing listeners first
      const existingHandler = (element as any)._dragHandler;
      if (existingHandler) {
        element.removeEventListener('dragstart', existingHandler);
      }

      // Create new handler and store reference
      const dragHandler = (e: DragEvent) => {
        const id = element.getAttribute('data-inline-id');
        if (id && e.dataTransfer) {
          e.dataTransfer.setData('application/x-source-id', id);
          e.dataTransfer.setData('text/plain', '');
          e.dataTransfer.effectAllowed = 'move';
        }
      };

      (element as any)._dragHandler = dragHandler;
      element.addEventListener('dragstart', dragHandler);
    });
    // Cleanup function
    return () => {
      // Clean up any React roots when component unmounts
      rootsRef.current.forEach((root) => {
        try { root.unmount(); } catch {}
      });
      rootsRef.current.clear();
    };
  }, [textContent]);

  // Event handlers
  function handleInput(e: React.FormEvent<HTMLDivElement>) {
    const editor = editorRef.current;
    if (!editor) return;
    
    const content = editor.innerHTML;
    
    // Update the first TEXT block or create one if none exists
    setBlocks((prev) => {
      const textBlocks = prev.filter(b => b.type === "TEXT");
      const otherBlocks = prev.filter(b => b.type !== "TEXT");
      
      if (textBlocks.length === 0) {
        return [{ id: crypto.randomUUID(), type: "TEXT", content } as TextBlock, ...otherBlocks];
      }
      
      // Update the first text block
      const updatedTextBlocks = textBlocks.map((b, i) => 
        i === 0 ? { ...b, content } as TextBlock : b
      );
      
      return [...updatedTextBlocks, ...otherBlocks];
    });
  }

  function handleDragOver(e: React.DragEvent) {
    e.preventDefault();
    e.stopPropagation();
    e.dataTransfer.dropEffect = "copy";
    setIsDragging(true);

    // Update insertion point for visual feedback
    updateInsertionPoint(e.clientX, e.clientY);
  }

  function insertNodeAtRange(range: Range, node: Node) {
    range.deleteContents();
    range.insertNode(node);
    range.setStartAfter(node);
    range.collapse(true);
  }

  function handleCanvasDrop(e: React.DragEvent<HTMLDivElement>) {
    e.preventDefault();
    setIsDragging(false);
    resetInsertionPoint();
    
    const payloadRaw = e.dataTransfer.getData("application/json");
    const fromId = e.dataTransfer.getData("application/x-source-id");
    const data = payloadRaw ? JSON.parse(payloadRaw) : undefined;
    
    // Use native browser caret position from drop event
    let range: Range | null = null;
    if ((document as any).caretRangeFromPoint) {
      range = (document as any).caretRangeFromPoint(e.clientX, e.clientY);
    } else if ((document as any).caretPositionFromPoint) {
      const pos = (document as any).caretPositionFromPoint(e.clientX, e.clientY);
      if (pos) {
        range = document.createRange();
        range.setStart(pos.offsetNode, pos.offset);
        range.collapse(true);
      }
    }
    
    if (!range) return;

    // Handle existing inline finding reordering
    if (!data && fromId) {
      const existingChip = editorRef.current?.querySelector(`[data-inline-id="${fromId}"]`);
      if (existingChip) {
        existingChip.remove();
        const newChip = existingChip.cloneNode(true) as HTMLElement;
        newChip.draggable = true;
        insertNodeAtRange(range, newChip);
      }
      return;
    }

    if (!data) return;

    const newId = `b_${Date.now()}_${Math.random().toString(36).slice(2)}`;

    if (data.type === "FINDING_BLOCK") {
      // Create inline finding chip
      const chip = document.createElement("span");
      chip.setAttribute("data-inline-id", newId);
      chip.setAttribute("contenteditable", "false");
      chip.className = "inline-flex items-center px-2 py-1 rounded-full bg-blue-100 text-blue-800 text-xs font-medium mx-1 cursor-grab";
      chip.textContent = data.findingName || "Finding";
      chip.draggable = true;
      
      chip.addEventListener('dragstart', (dragEvent) => {
        dragEvent.dataTransfer?.setData('application/x-source-id', newId);
        dragEvent.dataTransfer?.setData('text/plain', '');
      });
      
      insertNodeAtRange(range, chip);
      
      setBlocks((prev) => {
        const existing = prev.find(b => b.id === newId);
        if (existing) return prev;
        return [...prev, { id: newId, type: "FINDING_BLOCK", findingId: data.findingId, findingName: data.findingName, selectedValue: "value" } as FindingBlock];
      });
      
    } else if (data.type === "AI_BLOCK" || data.type === "CATEGORY_BLOCK") {
      // Create inline block placeholder that will be replaced with React component
      const blockPlaceholder = document.createElement("div");
      blockPlaceholder.setAttribute("data-block-id", newId);
      blockPlaceholder.setAttribute("contenteditable", "false");
      blockPlaceholder.className = "my-2 p-3 border rounded-lg bg-gray-50 not-prose";

      if (data.type === "AI_BLOCK") {
        blockPlaceholder.className = "my-2 p-3 border rounded-lg bg-orange-50 border-orange-200 not-prose";
        blockPlaceholder.innerHTML = `
          <div class="flex items-center gap-2 mb-2">
            <span class="text-orange-600">🧠</span>
            <span class="font-medium text-orange-700">AI Instruction Block</span>
          </div>
          <div class="text-sm bg-white border border-orange-200 rounded p-3" contenteditable="true" data-ai-content="${newId}" placeholder="Type your AI instructions here...">
            Type your AI instructions here...
          </div>
        `;
      } else {
        // Category block - create container for React component
        blockPlaceholder.className = "my-2 not-prose";
        blockPlaceholder.innerHTML = ""; // Will be populated by React component
      }

      // Insert with line breaks for proper text flow
      const br1 = document.createElement("br");
      const br2 = document.createElement("br");
      insertNodeAtRange(range, br1);
      range.setStartAfter(br1);
      range.insertNode(blockPlaceholder);
      range.setStartAfter(blockPlaceholder);
      range.insertNode(br2);

      // Position cursor after the block
      const sel = window.getSelection();
      if (sel) {
        sel.removeAllRanges();
        range.setStartAfter(br2);
        range.collapse(true);
        sel.addRange(range);
      }

      // Add to blocks array for data management
      const newBlock = data.type === "AI_BLOCK"
        ? ({ id: newId, type: "AI_BLOCK", innerContent: "" } as AiBlock)
        : ({ id: newId, type: "CATEGORY_BLOCK", category: data.category, categoryName: data.categoryName, selectedFindingIds: [] } as CategoryBlock);

      setBlocks((prev) => [...prev, newBlock]);

      // Setup component-specific functionality
      if (data.type === "AI_BLOCK") {
        const aiContentArea = blockPlaceholder.querySelector(`[data-ai-content="${newId}"]`) as HTMLElement;
        if (aiContentArea) {
          // Allow drops only in AI content area
          aiContentArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            e.stopPropagation();
          });

          aiContentArea.addEventListener('input', () => {
            setBlocks(prev => prev.map(b =>
              b.id === newId && b.type === "AI_BLOCK"
                ? { ...b, innerContent: aiContentArea.innerHTML } as AiBlock
                : b
            ));
          });
        }

        // Prevent drops on the AI block container (except content area)
        blockPlaceholder.addEventListener('dragover', (e) => {
          if (e.target !== aiContentArea && !aiContentArea?.contains(e.target as Node)) {
            e.dataTransfer!.dropEffect = 'none';
          }
        });

      } else if (data.type === "CATEGORY_BLOCK") {
        // Mount React component for category block
        const root = createRoot(blockPlaceholder);
        rootsRef.current.set(`block:${newId}`, root);

        root.render(
          <CategoryBlockCmp
            block={newBlock as CategoryBlock}
            onDragStart={() => {}}
            onDropOver={() => {}}
            onDropOn={() => {}}
            allFindings={detectionItems}
            onSelectionChange={(id, selected) => {
              setBlocks(prev => prev.map(b =>
                b.id === id && b.type === "CATEGORY_BLOCK"
                  ? { ...b, selectedFindingIds: selected } as CategoryBlock
                  : b
              ));
            }}
          />
        );

        // Prevent all drops on category blocks
        blockPlaceholder.addEventListener('dragover', (e) => {
          e.preventDefault();
          e.dataTransfer!.dropEffect = 'none';
        });
      }
    }
  }

  return (
    <div className="flex flex-col h-full bg-background rounded-md border p-3">
      <Toolbar />

      {/* Main text editor with inline blocks */}
      <div className="relative flex-1">
        <div
          ref={editorRef}
          contentEditable
          suppressContentEditableWarning
          className={`flex-1 p-4 min-h-[300px] overflow-auto focus:outline-none ${isDragging ? 'bg-blue-50' : ''}`}
          onInput={handleInput}
          onDrop={handleCanvasDrop}
          onDragOver={handleDragOver}
          onDragLeave={() => {
            setIsDragging(false);
            resetInsertionPoint();
          }}
        />

        {/* Visual drag insertion cursor */}
        {insertionPoint && (
          <div
            className="absolute w-0.5 h-5 bg-blue-600 pointer-events-none z-10 animate-pulse"
            style={{
              left: insertionPoint.x,
              top: insertionPoint.y,
            }}
          />
        )}
      </div>
    </div>
  );
}
