{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///T:/Development/TranscriptionModule/transcription-demo/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Tabs({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Root>) {\n  return (\n    <TabsPrimitive.Root\n      data-slot=\"tabs\"\n      className={cn(\"flex flex-col gap-2\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TabsList({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.List>) {\n  return (\n    <TabsPrimitive.List\n      data-slot=\"tabs-list\"\n      className={cn(\n        \"bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsTrigger({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\n  return (\n    <TabsPrimitive.Trigger\n      data-slot=\"tabs-trigger\"\n      className={cn(\n        \"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Content>) {\n  return (\n    <TabsPrimitive.Content\n      data-slot=\"tabs-content\"\n      className={cn(\"flex-1 outline-none\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,KAAK,KAGoC;QAHpC,EACZ,SAAS,EACT,GAAG,OAC6C,GAHpC;IAIZ,qBACE,6LAAC,2KAAkB;QACjB,aAAU;QACV,WAAW,IAAA,4HAAE,EAAC,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,SAAS,KAGgC;QAHhC,EAChB,SAAS,EACT,GAAG,OAC6C,GAHhC;IAIhB,qBACE,6LAAC,2KAAkB;QACjB,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,uGACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,YAAY,KAGgC;QAHhC,EACnB,SAAS,EACT,GAAG,OACgD,GAHhC;IAInB,qBACE,6LAAC,8KAAqB;QACpB,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,mqBACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,YAAY,KAGgC;QAHhC,EACnB,SAAS,EACT,GAAG,OACgD,GAHhC;IAInB,qBACE,6LAAC,8KAAqB;QACpB,aAAU;QACV,WAAW,IAAA,4HAAE,EAAC,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 86, "column": 0}, "map": {"version": 3, "sources": ["file:///T:/Development/TranscriptionModule/transcription-demo/src/components/workspace-nav.tsx"], "sourcesContent": ["\"use client\";\n\nimport Link from \"next/link\";\nimport { usePathname } from \"next/navigation\";\nimport { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from \"@/components/ui/tabs\";\n\nconst items = [\n  { href: \"/recording\", label: \"Recording\" },\n  { href: \"/ai/dashboard\", label: \"AI Dashboard\" },\n  { href: \"/report-templates\", label: \"Report Templates\" },\n  { href: \"/templates/editor\", label: \"Template Editor\" },\n];\n\nexport function WorkspaceNav() {\n  const pathname = usePathname();\n  const value = items.find((i) => pathname.startsWith(i.href))?.href ?? \"/recording\";\n  return (\n    <Tabs value={value} className=\"w-full\">\n      <TabsList className=\"mb-4\">\n        {items.map((i) => (\n          <TabsTrigger key={i.href} value={i.href} asChild>\n            <Link href={i.href}>{i.label}</Link>\n          </TabsTrigger>\n        ))}\n      </TabsList>\n    </Tabs>\n  );\n}\n\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;;;AAJA;;;;AAMA,MAAM,QAAQ;IACZ;QAAE,MAAM;QAAc,OAAO;IAAY;IACzC;QAAE,MAAM;QAAiB,OAAO;IAAe;IAC/C;QAAE,MAAM;QAAqB,OAAO;IAAmB;IACvD;QAAE,MAAM;QAAqB,OAAO;IAAkB;CACvD;AAEM,SAAS;QAEA;;IADd,MAAM,WAAW,IAAA,oJAAW;QACd;IAAd,MAAM,QAAQ,CAAA,oBAAA,cAAA,MAAM,IAAI,CAAC,CAAC,IAAM,SAAS,UAAU,CAAC,EAAE,IAAI,gBAA5C,kCAAA,YAAgD,IAAI,cAApD,8BAAA,mBAAwD;IACtE,qBACE,6LAAC,2IAAI;QAAC,OAAO;QAAO,WAAU;kBAC5B,cAAA,6LAAC,+IAAQ;YAAC,WAAU;sBACjB,MAAM,GAAG,CAAC,CAAC,kBACV,6LAAC,kJAAW;oBAAc,OAAO,EAAE,IAAI;oBAAE,OAAO;8BAC9C,cAAA,6LAAC,0KAAI;wBAAC,MAAM,EAAE,IAAI;kCAAG,EAAE,KAAK;;;;;;mBADZ,EAAE,IAAI;;;;;;;;;;;;;;;AAOlC;GAdgB;;QACG,oJAAW;;;KADd", "debugId": null}}, {"offset": {"line": 171, "column": 0}, "map": {"version": 3, "sources": ["file:///T:/Development/TranscriptionModule/transcription-demo/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;;AAEA;;;AAEA,SAAS,MAAM,KAA4D;QAA5D,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC,GAA5D;IACb,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 203, "column": 0}, "map": {"version": 3, "sources": ["file:///T:/Development/TranscriptionModule/transcription-demo/src/lib/convexClient.ts"], "sourcesContent": ["\"use client\";\n\nimport { ConvexHttpClient } from \"convex/browser\";\n\nexport function getConvex(): ConvexHttpClient | null {\n  const url = process.env.NEXT_PUBLIC_CONVEX_URL;\n  if (!url) return null;\n  try {\n    return new ConvexHttpClient(url);\n  } catch {\n    return null;\n  }\n}\n\n"], "names": [], "mappings": ";;;;AAKc;AAHd;AAAA;AAFA;;AAIO,SAAS;IACd,MAAM;IACN;;IACA,IAAI;QACF,OAAO,IAAI,sLAAgB,CAAC;IAC9B,EAAE,UAAM;QACN,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 229, "column": 0}, "map": {"version": 3, "sources": ["file:///T:/Development/TranscriptionModule/transcription-demo/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,KAEoC;QAFpC,EACd,GAAG,OAC+C,GAFpC;IAGd,qBAAO,6LAAC,6KAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,YAAY,KAEgC;QAFhC,EACnB,GAAG,OACgD,GAFhC;IAGnB,qBAAO,6LAAC,8KAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,YAAY,KAEgC;QAFhC,EACnB,GAAG,OACgD,GAFhC;IAGnB,qBAAO,6LAAC,8KAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,KAOtB;QAPsB,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ,GAPsB;IAQrB,qBACE,6LAAC,gLAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,IAAA,4HAAE,EACX,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,6KAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,8OAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MAxBS;AA0BT,SAAS,cAAc,KAKgC;QALhC,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD,GALhC;IAMrB,qBACE,6LAAC,+KAAsB;kBACrB,cAAA,6LAAC,gLAAuB;YACtB,aAAU;YACV,WAAW,IAAA,4HAAE,EACX,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,iLAAwB;oBACvB,WAAW,IAAA,4HAAE,EACX,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,KAGgC;QAHhC,EACnB,SAAS,EACT,GAAG,OACgD,GAHhC;IAInB,qBACE,6LAAC,8KAAqB;QACpB,aAAU;QACV,WAAW,IAAA,4HAAE,EAAC,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,WAAW,KAIgC;QAJhC,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C,GAJhC;IAKlB,qBACE,6LAAC,6KAAoB;QACnB,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,6aACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,sLAA6B;8BAC5B,cAAA,6LAAC,wNAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,6LAAC,iLAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,KAGgC;QAHhC,EACvB,SAAS,EACT,GAAG,OACoD,GAHhC;IAIvB,qBACE,6LAAC,kLAAyB;QACxB,aAAU;QACV,WAAW,IAAA,4HAAE,EAAC,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,KAGgC;QAHhC,EAC5B,SAAS,EACT,GAAG,OACyD,GAHhC;IAI5B,qBACE,6LAAC,uLAA8B;QAC7B,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,wOAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAhBS;AAkBT,SAAS,uBAAuB,KAGgC;QAHhC,EAC9B,SAAS,EACT,GAAG,OAC2D,GAHhC;IAI9B,qBACE,6LAAC,yLAAgC;QAC/B,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,8OAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAhBS", "debugId": null}}, {"offset": {"line": 496, "column": 0}, "map": {"version": 3, "sources": ["file:///T:/Development/TranscriptionModule/transcription-demo/src/components/ui/accordion.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AccordionPrimitive from \"@radix-ui/react-accordion\"\nimport { ChevronDownIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Accordion({\n  ...props\n}: React.ComponentProps<typeof AccordionPrimitive.Root>) {\n  return <AccordionPrimitive.Root data-slot=\"accordion\" {...props} />\n}\n\nfunction AccordionItem({\n  className,\n  ...props\n}: React.ComponentProps<typeof AccordionPrimitive.Item>) {\n  return (\n    <AccordionPrimitive.Item\n      data-slot=\"accordion-item\"\n      className={cn(\"border-b last:border-b-0\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction AccordionTrigger({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof AccordionPrimitive.Trigger>) {\n  return (\n    <AccordionPrimitive.Header className=\"flex\">\n      <AccordionPrimitive.Trigger\n        data-slot=\"accordion-trigger\"\n        className={cn(\n          \"focus-visible:border-ring focus-visible:ring-ring/50 flex flex-1 items-start justify-between gap-4 rounded-md py-4 text-left text-sm font-medium transition-all outline-none hover:underline focus-visible:ring-[3px] disabled:pointer-events-none disabled:opacity-50 [&[data-state=open]>svg]:rotate-180\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        <ChevronDownIcon className=\"text-muted-foreground pointer-events-none size-4 shrink-0 translate-y-0.5 transition-transform duration-200\" />\n      </AccordionPrimitive.Trigger>\n    </AccordionPrimitive.Header>\n  )\n}\n\nfunction AccordionContent({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof AccordionPrimitive.Content>) {\n  return (\n    <AccordionPrimitive.Content\n      data-slot=\"accordion-content\"\n      className=\"data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down overflow-hidden text-sm\"\n      {...props}\n    >\n      <div className={cn(\"pt-0 pb-4\", className)}>{children}</div>\n    </AccordionPrimitive.Content>\n  )\n}\n\nexport { Accordion, AccordionItem, AccordionTrigger, AccordionContent }\n"], "names": [], "mappings": ";;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,UAAU,KAEoC;QAFpC,EACjB,GAAG,OACkD,GAFpC;IAGjB,qBAAO,6LAAC,gLAAuB;QAAC,aAAU;QAAa,GAAG,KAAK;;;;;;AACjE;KAJS;AAMT,SAAS,cAAc,KAGgC;QAHhC,EACrB,SAAS,EACT,GAAG,OACkD,GAHhC;IAIrB,qBACE,6LAAC,gLAAuB;QACtB,aAAU;QACV,WAAW,IAAA,4HAAE,EAAC,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,iBAAiB,KAIgC;QAJhC,EACxB,SAAS,EACT,QAAQ,EACR,GAAG,OACqD,GAJhC;IAKxB,qBACE,6LAAC,kLAAyB;QAAC,WAAU;kBACnC,cAAA,6LAAC,mLAA0B;YACzB,aAAU;YACV,WAAW,IAAA,4HAAE,EACX,8SACA;YAED,GAAG,KAAK;;gBAER;8BACD,6LAAC,8OAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MApBS;AAsBT,SAAS,iBAAiB,KAIgC;QAJhC,EACxB,SAAS,EACT,QAAQ,EACR,GAAG,OACqD,GAJhC;IAKxB,qBACE,6LAAC,mLAA0B;QACzB,aAAU;QACV,WAAU;QACT,GAAG,KAAK;kBAET,cAAA,6LAAC;YAAI,WAAW,IAAA,4HAAE,EAAC,aAAa;sBAAa;;;;;;;;;;;AAGnD;MAdS", "debugId": null}}, {"offset": {"line": 604, "column": 0}, "map": {"version": 3, "sources": ["file:///T:/Development/TranscriptionModule/transcription-demo/src/app/report-templates/_components/Toolbar.tsx"], "sourcesContent": ["\"use client\";\n\nimport { Bold, Italic, Underline, List, ListOrdered, AlignLeft, AlignCenter, AlignRight } from \"lucide-react\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\";\n\nexport function Toolbar() {\n  function exec(command: string, value?: string) {\n    try {\n      document.execCommand(command, false, value);\n    } catch {}\n  }\n\n  return (\n    <div className=\"flex flex-wrap items-center gap-2 p-2 rounded-lg border bg-muted/30\">\n      <Select defaultValue=\"Arial\" onValueChange={(v) => exec(\"fontName\", v)}>\n        <SelectTrigger className=\"h-8 w-[160px]\"><SelectValue placeholder=\"Font\"/></SelectTrigger>\n        <SelectContent>\n          <SelectItem value=\"Arial\">Arial</SelectItem>\n          <SelectItem value=\"Georgia\">Georgia</SelectItem>\n          <SelectItem value=\"Verdana\">Verdana</SelectItem>\n        </SelectContent>\n      </Select>\n      <Select defaultValue=\"3\" onValueChange={(v) => exec(\"fontSize\", v)}>\n        <SelectTrigger className=\"h-8 w-[140px]\"><SelectValue placeholder=\"Size\"/></SelectTrigger>\n        <SelectContent>\n          <SelectItem value=\"2\">Small</SelectItem>\n          <SelectItem value=\"3\">Normal</SelectItem>\n          <SelectItem value=\"5\">Heading</SelectItem>\n          <SelectItem value=\"6\">Title</SelectItem>\n        </SelectContent>\n      </Select>\n\n      <div className=\"w-px h-6 bg-border mx-1\"/>\n      <Button size=\"icon\" variant=\"ghost\" className=\"h-8 w-8\" onMouseDown={(e) => e.preventDefault()} onClick={() => exec(\"bold\")}>\n        <Bold className=\"h-4 w-4\" />\n      </Button>\n      <Button size=\"icon\" variant=\"ghost\" className=\"h-8 w-8\" onMouseDown={(e) => e.preventDefault()} onClick={() => exec(\"italic\")}>\n        <Italic className=\"h-4 w-4\" />\n      </Button>\n      <Button size=\"icon\" variant=\"ghost\" className=\"h-8 w-8\" onMouseDown={(e) => e.preventDefault()} onClick={() => exec(\"underline\")}>\n        <Underline className=\"h-4 w-4\" />\n      </Button>\n\n      <div className=\"w-px h-6 bg-border mx-1\"/>\n      <Button size=\"icon\" variant=\"ghost\" className=\"h-8 w-8\" onMouseDown={(e) => e.preventDefault()} onClick={() => exec(\"justifyLeft\")}>\n        <AlignLeft className=\"h-4 w-4\" />\n      </Button>\n      <Button size=\"icon\" variant=\"ghost\" className=\"h-8 w-8\" onMouseDown={(e) => e.preventDefault()} onClick={() => exec(\"justifyCenter\")}>\n        <AlignCenter className=\"h-4 w-4\" />\n      </Button>\n      <Button size=\"icon\" variant=\"ghost\" className=\"h-8 w-8\" onMouseDown={(e) => e.preventDefault()} onClick={() => exec(\"justifyRight\")}>\n        <AlignRight className=\"h-4 w-4\" />\n      </Button>\n\n      <div className=\"w-px h-6 bg-border mx-1\"/>\n      <Button size=\"icon\" variant=\"ghost\" className=\"h-8 w-8\" onMouseDown={(e) => e.preventDefault()} onClick={() => { document.execCommand(\"formatBlock\", false, \"div\"); exec(\"insertUnorderedList\"); }}>\n        <List className=\"h-4 w-4\" />\n      </Button>\n      <Button size=\"icon\" variant=\"ghost\" className=\"h-8 w-8\" onMouseDown={(e) => e.preventDefault()} onClick={() => { document.execCommand(\"formatBlock\", false, \"div\"); exec(\"insertOrderedList\"); }}>\n        <ListOrdered className=\"h-4 w-4\" />\n      </Button>\n    </div>\n  );\n}\n\n"], "names": [], "mappings": ";;;;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAJA;;;;;AAMO,SAAS;IACd,SAAS,KAAK,OAAe,EAAE,KAAc;QAC3C,IAAI;YACF,SAAS,WAAW,CAAC,SAAS,OAAO;QACvC,EAAE,UAAM,CAAC;IACX;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,+IAAM;gBAAC,cAAa;gBAAQ,eAAe,CAAC,IAAM,KAAK,YAAY;;kCAClE,6LAAC,sJAAa;wBAAC,WAAU;kCAAgB,cAAA,6LAAC,oJAAW;4BAAC,aAAY;;;;;;;;;;;kCAClE,6LAAC,sJAAa;;0CACZ,6LAAC,mJAAU;gCAAC,OAAM;0CAAQ;;;;;;0CAC1B,6LAAC,mJAAU;gCAAC,OAAM;0CAAU;;;;;;0CAC5B,6LAAC,mJAAU;gCAAC,OAAM;0CAAU;;;;;;;;;;;;;;;;;;0BAGhC,6LAAC,+IAAM;gBAAC,cAAa;gBAAI,eAAe,CAAC,IAAM,KAAK,YAAY;;kCAC9D,6LAAC,sJAAa;wBAAC,WAAU;kCAAgB,cAAA,6LAAC,oJAAW;4BAAC,aAAY;;;;;;;;;;;kCAClE,6LAAC,sJAAa;;0CACZ,6LAAC,mJAAU;gCAAC,OAAM;0CAAI;;;;;;0CACtB,6LAAC,mJAAU;gCAAC,OAAM;0CAAI;;;;;;0CACtB,6LAAC,mJAAU;gCAAC,OAAM;0CAAI;;;;;;0CACtB,6LAAC,mJAAU;gCAAC,OAAM;0CAAI;;;;;;;;;;;;;;;;;;0BAI1B,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC,+IAAM;gBAAC,MAAK;gBAAO,SAAQ;gBAAQ,WAAU;gBAAU,aAAa,CAAC,IAAM,EAAE,cAAc;gBAAI,SAAS,IAAM,KAAK;0BAClH,cAAA,6LAAC,6MAAI;oBAAC,WAAU;;;;;;;;;;;0BAElB,6LAAC,+IAAM;gBAAC,MAAK;gBAAO,SAAQ;gBAAQ,WAAU;gBAAU,aAAa,CAAC,IAAM,EAAE,cAAc;gBAAI,SAAS,IAAM,KAAK;0BAClH,cAAA,6LAAC,mNAAM;oBAAC,WAAU;;;;;;;;;;;0BAEpB,6LAAC,+IAAM;gBAAC,MAAK;gBAAO,SAAQ;gBAAQ,WAAU;gBAAU,aAAa,CAAC,IAAM,EAAE,cAAc;gBAAI,SAAS,IAAM,KAAK;0BAClH,cAAA,6LAAC,4NAAS;oBAAC,WAAU;;;;;;;;;;;0BAGvB,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC,+IAAM;gBAAC,MAAK;gBAAO,SAAQ;gBAAQ,WAAU;gBAAU,aAAa,CAAC,IAAM,EAAE,cAAc;gBAAI,SAAS,IAAM,KAAK;0BAClH,cAAA,6LAAC,yOAAS;oBAAC,WAAU;;;;;;;;;;;0BAEvB,6LAAC,+IAAM;gBAAC,MAAK;gBAAO,SAAQ;gBAAQ,WAAU;gBAAU,aAAa,CAAC,IAAM,EAAE,cAAc;gBAAI,SAAS,IAAM,KAAK;0BAClH,cAAA,6LAAC,8OAAW;oBAAC,WAAU;;;;;;;;;;;0BAEzB,6LAAC,+IAAM;gBAAC,MAAK;gBAAO,SAAQ;gBAAQ,WAAU;gBAAU,aAAa,CAAC,IAAM,EAAE,cAAc;gBAAI,SAAS,IAAM,KAAK;0BAClH,cAAA,6LAAC,yOAAU;oBAAC,WAAU;;;;;;;;;;;0BAGxB,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC,+IAAM;gBAAC,MAAK;gBAAO,SAAQ;gBAAQ,WAAU;gBAAU,aAAa,CAAC,IAAM,EAAE,cAAc;gBAAI,SAAS;oBAAQ,SAAS,WAAW,CAAC,eAAe,OAAO;oBAAQ,KAAK;gBAAwB;0BAC/L,cAAA,6LAAC,6MAAI;oBAAC,WAAU;;;;;;;;;;;0BAElB,6LAAC,+IAAM;gBAAC,MAAK;gBAAO,SAAQ;gBAAQ,WAAU;gBAAU,aAAa,CAAC,IAAM,EAAE,cAAc;gBAAI,SAAS;oBAAQ,SAAS,WAAW,CAAC,eAAe,OAAO;oBAAQ,KAAK;gBAAsB;0BAC7L,cAAA,6LAAC,sOAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI/B;KA1DgB", "debugId": null}}, {"offset": {"line": 941, "column": 0}, "map": {"version": 3, "sources": ["file:///T:/Development/TranscriptionModule/transcription-demo/src/app/report-templates/_components/blocks/AiBlock.tsx"], "sourcesContent": ["\"use client\";\n\nimport { Brain, GripVertical } from \"lucide-react\";\nimport type { AiBlock as AiBlockT } from \"../types\";\n\nexport function AiBlock({ block, onDragStart, onDropInto, onContentChange }: {\n  block: AiBlockT;\n  onDragStart?: (e: React.DragEvent, id: string) => void;\n  onDropInto?: (e: React.DragEvent<HTMLDivElement>, parentId: string) => void;\n  onContentChange?: (id: string, html: string) => void;\n}) {\n  return (\n    <div\n      contentEditable={false}\n      draggable\n      onDragStart={(e) => onDragStart?.(e, block.id)}\n      className=\"my-2 p-3 rounded-lg border border-orange-300 bg-orange-50 text-orange-800 cursor-grab\"\n      data-id={block.id}\n    >\n      <div className=\"flex items-center gap-2 font-semibold mb-2\">\n        <GripVertical size={16} className=\"text-muted-foreground\" />\n        <Brain size={16} /> AI Instruction\n      </div>\n      <div\n        onDrop={(e) => onDropInto?.(e, block.id)}\n        onDragOver={(e) => { e.preventDefault(); e.stopPropagation(); }}\n        onInput={(e) => onContentChange?.(block.id, (e.currentTarget as HTMLDivElement).innerHTML)}\n        contentEditable\n        dir=\"ltr\"\n        suppressContentEditableWarning\n        data-placeholder=\"Write instructions and drag Findings here...\"\n        className=\"w-full p-2 border rounded bg-white text-foreground min-h-[56px] focus:outline-none\"\n        dangerouslySetInnerHTML={{ __html: block.innerContent }}\n      />\n    </div>\n  );\n}\n\n"], "names": [], "mappings": ";;;;;AAEA;AAAA;AAFA;;;AAKO,SAAS,QAAQ,KAKvB;QALuB,EAAE,KAAK,EAAE,WAAW,EAAE,UAAU,EAAE,eAAe,EAKxE,GALuB;IAMtB,qBACE,6LAAC;QACC,iBAAiB;QACjB,SAAS;QACT,aAAa,CAAC,IAAM,wBAAA,kCAAA,YAAc,GAAG,MAAM,EAAE;QAC7C,WAAU;QACV,WAAS,MAAM,EAAE;;0BAEjB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,yOAAY;wBAAC,MAAM;wBAAI,WAAU;;;;;;kCAClC,6LAAC,gNAAK;wBAAC,MAAM;;;;;;oBAAM;;;;;;;0BAErB,6LAAC;gBACC,QAAQ,CAAC,IAAM,uBAAA,iCAAA,WAAa,GAAG,MAAM,EAAE;gBACvC,YAAY,CAAC;oBAAQ,EAAE,cAAc;oBAAI,EAAE,eAAe;gBAAI;gBAC9D,SAAS,CAAC,IAAM,4BAAA,sCAAA,gBAAkB,MAAM,EAAE,EAAE,AAAC,EAAE,aAAa,CAAoB,SAAS;gBACzF,eAAe;gBACf,KAAI;gBACJ,8BAA8B;gBAC9B,oBAAiB;gBACjB,WAAU;gBACV,yBAAyB;oBAAE,QAAQ,MAAM,YAAY;gBAAC;;;;;;;;;;;;AAI9D;KA/BgB", "debugId": null}}, {"offset": {"line": 1022, "column": 0}, "map": {"version": 3, "sources": ["file:///T:/Development/TranscriptionModule/transcription-demo/src/app/report-templates/_components/blocks/CategoryBlock.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useMemo, useRef, useState, useEffect } from \"react\";\nimport { GripVertical, ChevronDown, Search } from \"lucide-react\";\nimport type { CategoryBlock as CategoryBlockT, DetectionItem } from \"../types\";\n\nexport function CategoryBlock({ block, onDragStart, onDropOver, onDropOn, allFindings, onSelectionChange }: {\n  block: CategoryBlockT;\n  onDragStart?: (e: React.DragEvent, id: string) => void;\n  onDropOver?: (e: React.DragEvent) => void;\n  onDropOn?: (e: React.DragEvent, id: string) => void;\n  allFindings: DetectionItem[];\n  onSelectionChange?: (id: string, ids: string[]) => void;\n}) {\n  const [open, setOpen] = useState(false);\n  const [q, setQ] = useState(\"\");\n  const wrapRef = useRef<HTMLDivElement | null>(null);\n\n  useEffect(() => {\n    const h = (ev: MouseEvent) => {\n      if (wrapRef.current && !wrapRef.current.contains(ev.target as Node)) setOpen(false);\n    };\n    window.addEventListener(\"mousedown\", h);\n    return () => window.removeEventListener(\"mousedown\", h);\n  }, []);\n\n  const items = useMemo(() => allFindings.filter((f) => f.category === block.category), [allFindings, block.category]);\n  const filtered = useMemo(() => items.filter((f) => f.name.toLowerCase().includes(q.toLowerCase())), [items, q]);\n\n  function toggle(id: string) {\n    const next = block.selectedFindingIds.includes(id) ? block.selectedFindingIds.filter((x) => x !== id) : [...block.selectedFindingIds, id];\n    onSelectionChange?.(block.id, next);\n  }\n\n  return (\n    <div\n      ref={wrapRef}\n      contentEditable={false}\n      draggable\n      onDragStart={(e) => onDragStart?.(e, block.id)}\n      onDragOver={onDropOver}\n      onDrop={(e) => onDropOn?.(e, block.id)}\n      className=\"my-2 p-2 rounded-lg border border-border bg-muted/30 flex items-start gap-2 cursor-grab\"\n      data-id={block.id}\n    >\n      <GripVertical size={16} className=\"text-muted-foreground mt-1\" />\n      <div className=\"flex-1\">\n        <div className=\"flex items-center justify-between\">\n          <span className=\"font-medium text-foreground\">{block.categoryName}</span>\n          <button type=\"button\" className=\"flex items-center gap-1 text-sm text-primary\" onClick={() => setOpen((v) => !v)}>\n            {block.selectedFindingIds.length} / {items.length} selected <ChevronDown size={16} className={`transition-transform ${open ? \"rotate-180\" : \"\"}`} />\n          </button>\n        </div>\n        {open && (\n          <div className=\"mt-2 p-2 rounded-md bg-background border shadow-sm\">\n            <div className=\"relative mb-2\">\n              <Search size={14} className=\"absolute left-2 top-1/2 -translate-y-1/2 text-muted-foreground\" />\n              <input value={q} onChange={(e) => setQ(e.target.value)} placeholder=\"Search findings...\" className=\"w-full pl-7 pr-2 py-1 border rounded-md text-sm\"/>\n            </div>\n            <div className=\"max-h-40 overflow-y-auto\">\n              {filtered.map((f) => (\n                <label key={f.id} className=\"flex items-center gap-2 p-1.5 rounded hover:bg-muted/50\">\n                  <input type=\"checkbox\" className=\"h-4 w-4\" checked={block.selectedFindingIds.includes(f.id)} onChange={() => toggle(f.id)} />\n                  <span className=\"text-sm\">{f.name}</span>\n                </label>\n              ))}\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;;;AAHA;;;AAMO,SAAS,cAAc,KAO7B;QAP6B,EAAE,KAAK,EAAE,WAAW,EAAE,UAAU,EAAE,QAAQ,EAAE,WAAW,EAAE,iBAAiB,EAOvG,GAP6B;;IAQ5B,MAAM,CAAC,MAAM,QAAQ,GAAG,IAAA,yKAAQ,EAAC;IACjC,MAAM,CAAC,GAAG,KAAK,GAAG,IAAA,yKAAQ,EAAC;IAC3B,MAAM,UAAU,IAAA,uKAAM,EAAwB;IAE9C,IAAA,0KAAS;mCAAC;YACR,MAAM;6CAAI,CAAC;oBACT,IAAI,QAAQ,OAAO,IAAI,CAAC,QAAQ,OAAO,CAAC,QAAQ,CAAC,GAAG,MAAM,GAAW,QAAQ;gBAC/E;;YACA,OAAO,gBAAgB,CAAC,aAAa;YACrC;2CAAO,IAAM,OAAO,mBAAmB,CAAC,aAAa;;QACvD;kCAAG,EAAE;IAEL,MAAM,QAAQ,IAAA,wKAAO;wCAAC,IAAM,YAAY,MAAM;gDAAC,CAAC,IAAM,EAAE,QAAQ,KAAK,MAAM,QAAQ;;uCAAG;QAAC;QAAa,MAAM,QAAQ;KAAC;IACnH,MAAM,WAAW,IAAA,wKAAO;2CAAC,IAAM,MAAM,MAAM;mDAAC,CAAC,IAAM,EAAE,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,EAAE,WAAW;;0CAAM;QAAC;QAAO;KAAE;IAE9G,SAAS,OAAO,EAAU;QACxB,MAAM,OAAO,MAAM,kBAAkB,CAAC,QAAQ,CAAC,MAAM,MAAM,kBAAkB,CAAC,MAAM,CAAC,CAAC,IAAM,MAAM,MAAM;eAAI,MAAM,kBAAkB;YAAE;SAAG;QACzI,8BAAA,wCAAA,kBAAoB,MAAM,EAAE,EAAE;IAChC;IAEA,qBACE,6LAAC;QACC,KAAK;QACL,iBAAiB;QACjB,SAAS;QACT,aAAa,CAAC,IAAM,wBAAA,kCAAA,YAAc,GAAG,MAAM,EAAE;QAC7C,YAAY;QACZ,QAAQ,CAAC,IAAM,qBAAA,+BAAA,SAAW,GAAG,MAAM,EAAE;QACrC,WAAU;QACV,WAAS,MAAM,EAAE;;0BAEjB,6LAAC,yOAAY;gBAAC,MAAM;gBAAI,WAAU;;;;;;0BAClC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAA+B,MAAM,YAAY;;;;;;0CACjE,6LAAC;gCAAO,MAAK;gCAAS,WAAU;gCAA+C,SAAS,IAAM,QAAQ,CAAC,IAAM,CAAC;;oCAC3G,MAAM,kBAAkB,CAAC,MAAM;oCAAC;oCAAI,MAAM,MAAM;oCAAC;kDAAU,6LAAC,sOAAW;wCAAC,MAAM;wCAAI,WAAW,AAAC,wBAAgD,OAAzB,OAAO,eAAe;;;;;;;;;;;;;;;;;;oBAG/I,sBACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,mNAAM;wCAAC,MAAM;wCAAI,WAAU;;;;;;kDAC5B,6LAAC;wCAAM,OAAO;wCAAG,UAAU,CAAC,IAAM,KAAK,EAAE,MAAM,CAAC,KAAK;wCAAG,aAAY;wCAAqB,WAAU;;;;;;;;;;;;0CAErG,6LAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAC,kBACb,6LAAC;wCAAiB,WAAU;;0DAC1B,6LAAC;gDAAM,MAAK;gDAAW,WAAU;gDAAU,SAAS,MAAM,kBAAkB,CAAC,QAAQ,CAAC,EAAE,EAAE;gDAAG,UAAU,IAAM,OAAO,EAAE,EAAE;;;;;;0DACxH,6LAAC;gDAAK,WAAU;0DAAW,EAAE,IAAI;;;;;;;uCAFvB,EAAE,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWhC;GAlEgB;KAAA", "debugId": null}}, {"offset": {"line": 1233, "column": 0}, "map": {"version": 3, "sources": ["file:///T:/Development/TranscriptionModule/transcription-demo/src/app/report-templates/_components/blocks/FindingBlock.tsx"], "sourcesContent": ["\"use client\";\n\nimport { GripVertical, List } from \"lucide-react\";\nimport type { FindingBlock } from \"../types\";\n\nexport function FindingBlockInline({ block, onDragStart }: { block: FindingBlock; onDragStart?: (e: React.DragEvent, id: string) => void; }) {\n  return (\n    <span\n      contentEditable={false}\n      draggable\n      onDragStart={(e) => onDragStart?.(e, block.id)}\n      className=\"mx-1 p-1 pr-2 rounded border border-blue-300 bg-blue-50 text-blue-800 inline-flex items-center gap-1.5 align-middle text-sm cursor-grab\"\n      data-inline\n      data-inline-id={block.id}\n    >\n      <GripVertical size={14} className=\"text-muted-foreground\" />\n      <List size={14} />\n      <span className=\"font-medium\">{block.findingName}</span>\n      <select\n        className=\"text-xs bg-white rounded border-border p-0.5 ml-1\"\n        defaultValue={block.selectedValue}\n        onChange={(e) => { block.selectedValue = e.target.value as any; /* state updated by parent via re-render from input sync */ }}\n      >\n        <option value=\"value\">Value</option>\n        <option value=\"name\">Name</option>\n      </select>\n    </span>\n  );\n}\n\n"], "names": [], "mappings": ";;;;;AAEA;AAAA;AAFA;;;AAKO,SAAS,mBAAmB,KAAwG;QAAxG,EAAE,KAAK,EAAE,WAAW,EAAoF,GAAxG;IACjC,qBACE,6LAAC;QACC,iBAAiB;QACjB,SAAS;QACT,aAAa,CAAC,IAAM,wBAAA,kCAAA,YAAc,GAAG,MAAM,EAAE;QAC7C,WAAU;QACV,aAAW;QACX,kBAAgB,MAAM,EAAE;;0BAExB,6LAAC,yOAAY;gBAAC,MAAM;gBAAI,WAAU;;;;;;0BAClC,6LAAC,6MAAI;gBAAC,MAAM;;;;;;0BACZ,6LAAC;gBAAK,WAAU;0BAAe,MAAM,WAAW;;;;;;0BAChD,6LAAC;gBACC,WAAU;gBACV,cAAc,MAAM,aAAa;gBACjC,UAAU,CAAC;oBAAQ,MAAM,aAAa,GAAG,EAAE,MAAM,CAAC,KAAK,EAAS,yDAAyD;gBAAG;;kCAE5H,6LAAC;wBAAO,OAAM;kCAAQ;;;;;;kCACtB,6LAAC;wBAAO,OAAM;kCAAO;;;;;;;;;;;;;;;;;;AAI7B;KAvBgB", "debugId": null}}, {"offset": {"line": 1322, "column": 0}, "map": {"version": 3, "sources": ["file:///T:/Development/TranscriptionModule/transcription-demo/src/app/report-templates/_components/EditorCanvas.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useEffect, useRef } from \"react\";\nimport { createRoot, Root } from \"react-dom/client\";\nimport { Toolbar } from \"./Toolbar\";\nimport { AiBlock as AiBlockCmp } from \"./blocks/AiBlock\";\nimport { CategoryBlock as CategoryBlockCmp } from \"./blocks/CategoryBlock\";\nimport { FindingBlockInline } from \"./blocks/FindingBlock\";\nimport type { Block, DetectionItem, AiBlock, CategoryBlock, TextBlock, FindingBlock } from \"./types\";\n\nexport function EditorCanvas({ blocks, setBlocks, detectionItems }: { blocks: Block[]; setBlocks: (updater: (prev: Block[]) => Block[]) => void; detectionItems: DetectionItem[]; }) {\n  const editorRef = useRef<HTMLDivElement | null>(null);\n  const rootsRef = useRef<Map<string, Root>>(new Map());\n\n  // Ensure there is always at least one editable TEXT block\n  useEffect(() => {\n    if (blocks.length === 0) {\n      const id = `b_${Date.now()}_${Math.random().toString(36).slice(2)}`;\n      setBlocks((prev) => (prev.length === 0 ? [{ id, type: \"TEXT\", content: \"<p></p>\" } as TextBlock] : prev));\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [blocks.length]);\n\n  // Sync top-level blocks and inline placeholders\n  useEffect(() => {\n    const editor = editorRef.current!;\n    const nextRoots = new Map<string, Root>();\n\n    // Ensure an element exists for each top-level block in order\n    blocks.forEach((b, idx) => {\n      let el = editor.querySelector(`[data-id=\"${b.id}\"]`) as HTMLElement | null;\n      if (!el) {\n        el = document.createElement(\"div\");\n        el.dataset.id = b.id;\n        editor.insertBefore(el, editor.children[idx] ?? null);\n      }\n\n      if (b.type === \"TEXT\") {\n        if (el.contentEditable !== \"true\") el.contentEditable = \"true\";\n        el.className = \"min-h-[120px] py-1 outline-none break-words\";\n        if (el.innerHTML !== b.content) el.innerHTML = b.content || \"<p></p>\";\n      } else {\n        if (el.contentEditable === \"true\") el.contentEditable = \"false\";\n        const key = `top:${b.id}`;\n        const root = rootsRef.current.get(key) ?? createRoot(el);\n        nextRoots.set(key, root);\n        if (b.type === \"AI_BLOCK\") {\n          root.render(<AiBlockCmp block={b as AiBlock} onDragStart={handleDragStart} onDropInto={(e) => handleDropIntoInner(e, b.id)} onContentChange={handleAiInnerChange} />);\n        } else if (b.type === \"CATEGORY_BLOCK\") {\n          root.render(<CategoryBlockCmp block={b as CategoryBlock} onDragStart={handleDragStart} onDropOver={handleDragOver} onDropOn={handleBlockDrop} allFindings={detectionItems} onSelectionChange={handleCategorySelectionChange} />);\n        } else if (b.type === \"FINDING_BLOCK\") {\n          // Render as block-level if user dropped as its own block\n          root.render(<FindingBlockInline block={b as FindingBlock} onDragStart={handleDragStart} />);\n        }\n      }\n    });\n\n    // Remove any extra top-level nodes\n    const knownIds = new Set(blocks.map((b) => b.id));\n    Array.from(editor.children).forEach((child) => {\n      const id = (child as HTMLElement).dataset.id;\n      if (!id || !knownIds.has(id)) child.remove();\n    });\n\n    // Mount inline placeholders for finding and category blocks within TEXT or AI inner areas\n    for (const ib of blocks) {\n      if (ib.type === \"FINDING_BLOCK\" || ib.type === \"CATEGORY_BLOCK\") {\n        const selector = `[data-inline-id=\"${ib.id}\"]`;\n        const nodes = editor.querySelectorAll(selector);\n        nodes.forEach((node, i) => {\n          const key = `inline:${ib.id}:${i}`;\n          const root = rootsRef.current.get(key) ?? createRoot(node as Element);\n          nextRoots.set(key, root);\n          if (ib.type === \"FINDING_BLOCK\") {\n            root.render(<FindingBlockInline block={ib as FindingBlock} onDragStart={handleDragStart} />);\n          } else {\n            root.render(\n              <CategoryBlockCmp\n                block={ib as CategoryBlock}\n                onDragStart={handleDragStart}\n                onDropOver={handleDragOver}\n                onDropOn={handleBlockDrop}\n                allFindings={detectionItems}\n                onSelectionChange={handleCategorySelectionChange}\n              />\n            );\n          }\n        });\n      }\n    }\n\n    // Unmount roots that no longer have targets\n    rootsRef.current.forEach((root, key) => {\n      if (!nextRoots.has(key)) {\n        try { root.unmount(); } catch {}\n      }\n    });\n    rootsRef.current = nextRoots;\n  }, [blocks, detectionItems]);\n\n  // Event handlers\n  function handleInput(e: React.FormEvent<HTMLDivElement>) {\n    const target = e.target as HTMLElement;\n    const host = target.closest(\"[data-id]\") as HTMLElement | null;\n    const id = host?.dataset?.id;\n    if (!id) return;\n    setBlocks((prev) => prev.map((b) => (b.id === id && b.type === \"TEXT\" ? { ...(b as TextBlock), content: host.innerHTML } : b)));\n  }\n\n  function handleAiInnerChange(id: string, html: string) {\n    setBlocks((prev) => prev.map((b) => (b.id === id && b.type === \"AI_BLOCK\" ? { ...(b as AiBlock), innerContent: html } : b)));\n  }\n\n  function handleCategorySelectionChange(id: string, selected: string[]) {\n    setBlocks((prev) => prev.map((b) => (b.id === id && b.type === \"CATEGORY_BLOCK\" ? { ...(b as CategoryBlock), selectedFindingIds: selected } : b)));\n  }\n\n  function handleDragStart(e: React.DragEvent, id: string) {\n    e.stopPropagation();\n    e.dataTransfer.setData(\"application/x-source-id\", id);\n  }\n\n  function handleDragOver(e: React.DragEvent) {\n    e.preventDefault();\n    e.stopPropagation();\n    const payloadRaw = e.dataTransfer.getData(\"application/json\");\n    const fromId = e.dataTransfer.getData(\"application/x-source-id\");\n    if (payloadRaw || fromId) e.dataTransfer.dropEffect = payloadRaw ? \"copy\" : \"move\";\n  }\n\n  function caretRangeAtPoint(e: React.DragEvent): Range | null {\n    const anyDoc = document as any;\n    if (anyDoc.caretRangeFromPoint) return anyDoc.caretRangeFromPoint(e.clientX, e.clientY);\n    const pos = (anyDoc.caretPositionFromPoint?.(e.clientX, e.clientY));\n    if (pos) { const r = document.createRange(); r.setStart(pos.offsetNode, pos.offset); r.collapse(true); return r; }\n    return null;\n  }\n\n  function insertNodeAtRange(range: Range, node: Node) {\n    range.deleteContents();\n    range.insertNode(node);\n    // Move caret after inserted node\n    const sel = window.getSelection();\n    if (sel) { sel.removeAllRanges(); const r = document.createRange(); r.setStartAfter(node); r.collapse(true); sel.addRange(r); }\n  }\n\n  function handleCanvasDrop(e: React.DragEvent<HTMLDivElement>) {\n    e.preventDefault();\n    const payloadRaw = e.dataTransfer.getData(\"application/json\");\n    const fromId = e.dataTransfer.getData(\"application/x-source-id\");\n    const data = payloadRaw ? (JSON.parse(payloadRaw) as any) : undefined;\n    let range = caretRangeAtPoint(e);\n\n    // If empty editor or no caret range, create a range at the end so drops work\n    if (!range) {\n      const editor = editorRef.current!;\n      const last = editor.lastChild;\n      const r = document.createRange();\n      if (last) { r.setStartAfter(last); } else { editor.appendChild(document.createTextNode(\"\\u200b\")); r.setStart(editor.firstChild!, 1); }\n      r.collapse(true);\n      range = r;\n    }\n\n    // If dropping an existing block\n    if (!data && fromId) {\n      const targetEl = (range?.startContainer as Node | null) ? (range!.startContainer as Node).parentElement : null;\n      const hostEl = targetEl?.closest(\"[data-id]\") as HTMLElement | null;\n      const hostId = hostEl?.dataset.id;\n      const srcBlock = blocks.find((b) => b.id === fromId);\n\n      // Move inline finding/category inside TEXT at caret\n      if (srcBlock && (srcBlock.type === \"FINDING_BLOCK\" || srcBlock.type === \"CATEGORY_BLOCK\") && hostId && blocks.find((b) => b.id === hostId)?.type === \"TEXT\" && range) {\n        const editor = editorRef.current!;\n        editor.querySelectorAll(`[data-inline-id=\"${fromId}\"]`).forEach((n) => n.parentElement?.removeChild(n));\n        const span = document.createElement(\"span\");\n        span.setAttribute(\"data-inline-id\", fromId);\n        span.setAttribute(\"contenteditable\", \"false\");\n        insertNodeAtRange(range, span);\n        setBlocks((prev) => prev.map((b) => (b.id === hostId && b.type === \"TEXT\" ? { ...(b as TextBlock), content: hostEl!.innerHTML } : b)));\n        return;\n      }\n\n      // Otherwise reorder top-level blocks\n      const targetBlockEl = hostEl;\n      const targetId = targetBlockEl?.dataset.id;\n      if (!targetId || targetId === fromId) return;\n      setBlocks((prev) => {\n        const arr = [...prev];\n        const si = arr.findIndex((b) => b.id === fromId);\n        const ti = arr.findIndex((b) => b.id === targetId);\n        if (si < 0 || ti < 0) return prev;\n        const [m] = arr.splice(si, 1);\n        arr.splice(ti, 0, m);\n        return arr;\n      });\n      return;\n    }\n\n    if (!data) return;\n\n    // New block from sidebar\n    const newId = `b_${Date.now()}_${Math.random().toString(36).slice(2)}`;\n\n    if (data.type === \"FINDING_BLOCK\" && range) {\n      // Inline insert into nearest TEXT or AI inner area\n      const host = (range.startContainer as Node).parentElement?.closest(\"[data-id]\") as HTMLElement | null;\n      const placeholder = document.createElement(\"span\");\n      placeholder.setAttribute(\"data-inline-id\", newId);\n      placeholder.setAttribute(\"contenteditable\", \"false\");\n      insertNodeAtRange(range, placeholder);\n\n      if (host && blocks.find((b) => b.id === host.dataset.id)?.type === \"TEXT\") {\n        const hostId = host.dataset.id!;\n        setBlocks((prev) => {\n          const next = prev.map((b) => (b.id === hostId && b.type === \"TEXT\" ? { ...(b as TextBlock), content: host.innerHTML } : b));\n          next.push({ id: newId, type: \"FINDING_BLOCK\", findingId: data.findingId, findingName: data.findingName, selectedValue: \"value\" } as FindingBlock);\n          return next;\n        });\n        return;\n      }\n\n      // If not inside TEXT, just add as block-level right after target block\n      const targetEl = (range.startContainer as Node).parentElement?.closest(\"[data-id]\") as HTMLElement | null;\n      const targetIndex = targetEl ? blocks.findIndex((b) => b.id === targetEl.dataset.id) : blocks.length - 1;\n      setBlocks((prev) => {\n        const next = [...prev];\n        next.splice(Math.max(0, targetIndex + 1), 0, { id: newId, type: \"FINDING_BLOCK\", findingId: data.findingId, findingName: data.findingName, selectedValue: \"value\" } as FindingBlock);\n        return next;\n      });\n      return;\n    }\n\n    if (data.type === \"AI_BLOCK\" || data.type === \"CATEGORY_BLOCK\") {\n      const hostEl = range ? (range.startContainer as Node).parentElement?.closest(\"[data-id]\") as HTMLElement | null : null;\n      const hostId = hostEl?.dataset.id;\n      const hostIsText = !!(hostId && blocks.find((b) => b.id === hostId)?.type === \"TEXT\");\n\n      // If dropping into TEXT, split the TEXT around caret and insert block between\n      if (hostIsText && range && hostEl) {\n        const marker = `__SPLIT__${newId}__`;\n        const textNode = document.createTextNode(marker);\n        insertNodeAtRange(range, textNode);\n        const html = hostEl.innerHTML;\n        const parts = html.split(marker);\n        const pre = parts[0] ?? \"\";\n        const post = parts[1] ?? \"\";\n        setBlocks((prev) => {\n          const index = prev.findIndex((b) => b.id === hostId);\n          const next: Block[] = [...prev];\n          const newBlock = data.type === \"AI_BLOCK\"\n            ? ({ id: newId, type: \"AI_BLOCK\", innerContent: \"\" } as AiBlock)\n            : ({ id: newId, type: \"CATEGORY_BLOCK\", category: data.category, categoryName: data.categoryName, selectedFindingIds: [] } as CategoryBlock);\n          // Replace host TEXT with pre TEXT, new block, and post TEXT\n          next.splice(index, 1,\n            { id: `${hostId}-pre`, type: \"TEXT\", content: pre } as TextBlock,\n            newBlock,\n            { id: `${hostId}-post`, type: \"TEXT\", content: post } as TextBlock,\n          );\n          return next;\n        });\n        return;\n      }\n\n      // Otherwise insert as its own block near target\n      const idx = hostEl ? blocks.findIndex((b) => b.id === hostEl.dataset.id) : blocks.length - 1;\n      setBlocks((prev) => {\n        const next = [...prev];\n        const block = data.type === \"AI_BLOCK\"\n          ? ({ id: newId, type: \"AI_BLOCK\", innerContent: \"\" } as AiBlock)\n          : ({ id: newId, type: \"CATEGORY_BLOCK\", category: data.category, categoryName: data.categoryName, selectedFindingIds: [] } as CategoryBlock);\n        next.splice(Math.max(0, idx + 1), 0, block);\n        return next;\n      });\n      return;\n    }\n  }\n\n  function handleBlockDrop(e: React.DragEvent, targetId: string) {\n    e.preventDefault(); e.stopPropagation();\n    const fromId = e.dataTransfer.getData(\"application/x-source-id\");\n    if (!fromId || fromId === targetId) return;\n    setBlocks((prev) => {\n      const arr = [...prev];\n      const si = arr.findIndex((b) => b.id === fromId);\n      const ti = arr.findIndex((b) => b.id === targetId);\n      if (si < 0 || ti < 0) return prev;\n      const [m] = arr.splice(si, 1);\n      arr.splice(ti, 0, m);\n      return arr;\n    });\n  }\n\n  function handleDropIntoInner(e: React.DragEvent<HTMLDivElement>, parentId: string) {\n    e.preventDefault(); e.stopPropagation();\n    const payloadRaw = e.dataTransfer.getData(\"application/json\");\n    const fromId = e.dataTransfer.getData(\"application/x-source-id\");\n    const host = e.currentTarget as HTMLDivElement;\n\n    const range = caretRangeAtPoint(e);\n    if (!range) return;\n\n    // Move existing inline finding/category within AI block\n    if (!payloadRaw && fromId) {\n      const src = blocks.find((b) => b.id === fromId);\n      if (src && (src.type === \"FINDING_BLOCK\" || src.type === \"CATEGORY_BLOCK\")) {\n        host.querySelectorAll(`[data-inline-id=\"${fromId}\"]`).forEach((n) => n.parentElement?.removeChild(n));\n        const span = document.createElement(\"span\");\n        span.setAttribute(\"data-inline-id\", fromId);\n        span.setAttribute(\"contenteditable\", \"false\");\n        insertNodeAtRange(range, span);\n        const html = host.innerHTML;\n        setBlocks((prev) => prev.map((b) => (b.id === parentId && b.type === \"AI_BLOCK\" ? { ...(b as AiBlock), innerContent: html } : b)));\n      }\n      return;\n    }\n\n    if (!payloadRaw) return;\n    const data = JSON.parse(payloadRaw) as any;\n    if (data.type !== \"FINDING_BLOCK\" && data.type !== \"CATEGORY_BLOCK\") return;\n\n    const span = document.createElement(\"span\");\n    const id = `b_${Date.now()}_${Math.random().toString(36).slice(2)}`;\n    span.setAttribute(\"data-inline-id\", id);\n    span.setAttribute(\"contenteditable\", \"false\");\n    insertNodeAtRange(range, span);\n\n    const html = host.innerHTML;\n    setBlocks((prev) => {\n      const next = prev.map((b) => (b.id === parentId && b.type === \"AI_BLOCK\" ? { ...(b as AiBlock), innerContent: html } : b));\n      if (data.type === \"FINDING_BLOCK\") {\n        next.push({ id, type: \"FINDING_BLOCK\", findingId: data.findingId, findingName: data.findingName, selectedValue: \"value\" } as FindingBlock);\n      } else {\n        next.push({ id, type: \"CATEGORY_BLOCK\", category: data.category, categoryName: data.categoryName, selectedFindingIds: [] } as CategoryBlock);\n      }\n      return next;\n    });\n  }\n\n  return (\n    <div className=\"flex flex-col h-full bg-background rounded-md border p-3\">\n      <Toolbar />\n      <div\n        ref={editorRef}\n        dir=\"ltr\"\n        className=\"flex-1 p-3 rounded-md overflow-auto focus:outline-none\"\n        onInput={handleInput}\n        onDrop={handleCanvasDrop}\n        onDragEnter={handleDragOver}\n        onDragOver={handleDragOver}\n      />\n    </div>\n  );\n}\n\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AAUO,SAAS,aAAa,KAAsJ;QAAtJ,EAAE,MAAM,EAAE,SAAS,EAAE,cAAc,EAAmH,GAAtJ;;IAC3B,MAAM,YAAY,IAAA,uKAAM,EAAwB;IAChD,MAAM,WAAW,IAAA,uKAAM,EAAoB,IAAI;IAE/C,0DAA0D;IAC1D,IAAA,0KAAS;kCAAC;YACR,IAAI,OAAO,MAAM,KAAK,GAAG;gBACvB,MAAM,KAAK,AAAC,KAAkB,OAAd,KAAK,GAAG,IAAG,KAAuC,OAApC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,KAAK,CAAC;gBAC/D;8CAAU,CAAC,OAAU,KAAK,MAAM,KAAK,IAAI;4BAAC;gCAAE;gCAAI,MAAM;gCAAQ,SAAS;4BAAU;yBAAe,GAAG;;YACrG;QACA,uDAAuD;QACzD;iCAAG;QAAC,OAAO,MAAM;KAAC;IAElB,gDAAgD;IAChD,IAAA,0KAAS;kCAAC;YACR,MAAM,SAAS,UAAU,OAAO;YAChC,MAAM,YAAY,IAAI;YAEtB,6DAA6D;YAC7D,OAAO,OAAO;0CAAC,CAAC,GAAG;oBACjB,IAAI,KAAK,OAAO,aAAa,CAAC,AAAC,aAAiB,OAAL,EAAE,EAAE,EAAC;oBAChD,IAAI,CAAC,IAAI;wBACP,KAAK,SAAS,aAAa,CAAC;wBAC5B,GAAG,OAAO,CAAC,EAAE,GAAG,EAAE,EAAE;4BACI;wBAAxB,OAAO,YAAY,CAAC,IAAI,CAAA,uBAAA,OAAO,QAAQ,CAAC,IAAI,cAApB,kCAAA,uBAAwB;oBAClD;oBAEA,IAAI,EAAE,IAAI,KAAK,QAAQ;wBACrB,IAAI,GAAG,eAAe,KAAK,QAAQ,GAAG,eAAe,GAAG;wBACxD,GAAG,SAAS,GAAG;wBACf,IAAI,GAAG,SAAS,KAAK,EAAE,OAAO,EAAE,GAAG,SAAS,GAAG,EAAE,OAAO,IAAI;oBAC9D,OAAO;wBACL,IAAI,GAAG,eAAe,KAAK,QAAQ,GAAG,eAAe,GAAG;wBACxD,MAAM,MAAM,AAAC,OAAW,OAAL,EAAE,EAAE;4BACV;wBAAb,MAAM,OAAO,CAAA,wBAAA,SAAS,OAAO,CAAC,GAAG,CAAC,kBAArB,mCAAA,wBAA6B,IAAA,mLAAU,EAAC;wBACrD,UAAU,GAAG,CAAC,KAAK;wBACnB,IAAI,EAAE,IAAI,KAAK,YAAY;4BACzB,KAAK,MAAM,eAAC,6LAAC,oLAAU;gCAAC,OAAO;gCAAc,aAAa;gCAAiB,UAAU;8DAAE,CAAC,IAAM,oBAAoB,GAAG,EAAE,EAAE;;gCAAG,iBAAiB;;;;;;wBAC/I,OAAO,IAAI,EAAE,IAAI,KAAK,kBAAkB;4BACtC,KAAK,MAAM,eAAC,6LAAC,gMAAgB;gCAAC,OAAO;gCAAoB,aAAa;gCAAiB,YAAY;gCAAgB,UAAU;gCAAiB,aAAa;gCAAgB,mBAAmB;;;;;;wBAChM,OAAO,IAAI,EAAE,IAAI,KAAK,iBAAiB;4BACrC,yDAAyD;4BACzD,KAAK,MAAM,eAAC,6LAAC,oMAAkB;gCAAC,OAAO;gCAAmB,aAAa;;;;;;wBACzE;oBACF;gBACF;;YAEA,mCAAmC;YACnC,MAAM,WAAW,IAAI,IAAI,OAAO,GAAG;0CAAC,CAAC,IAAM,EAAE,EAAE;;YAC/C,MAAM,IAAI,CAAC,OAAO,QAAQ,EAAE,OAAO;0CAAC,CAAC;oBACnC,MAAM,KAAK,AAAC,MAAsB,OAAO,CAAC,EAAE;oBAC5C,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,KAAK,MAAM,MAAM;gBAC5C;;YAEA,0FAA0F;YAC1F,KAAK,MAAM,MAAM,OAAQ;gBACvB,IAAI,GAAG,IAAI,KAAK,mBAAmB,GAAG,IAAI,KAAK,kBAAkB;oBAC/D,MAAM,WAAW,AAAC,oBAAyB,OAAN,GAAG,EAAE,EAAC;oBAC3C,MAAM,QAAQ,OAAO,gBAAgB,CAAC;oBACtC,MAAM,OAAO;kDAAC,CAAC,MAAM;4BACnB,MAAM,MAAM,AAAC,UAAkB,OAAT,GAAG,EAAE,EAAC,KAAK,OAAF;gCAClB;4BAAb,MAAM,OAAO,CAAA,wBAAA,SAAS,OAAO,CAAC,GAAG,CAAC,kBAArB,mCAAA,wBAA6B,IAAA,mLAAU,EAAC;4BACrD,UAAU,GAAG,CAAC,KAAK;4BACnB,IAAI,GAAG,IAAI,KAAK,iBAAiB;gCAC/B,KAAK,MAAM,eAAC,6LAAC,oMAAkB;oCAAC,OAAO;oCAAoB,aAAa;;;;;;4BAC1E,OAAO;gCACL,KAAK,MAAM,eACT,6LAAC,gMAAgB;oCACf,OAAO;oCACP,aAAa;oCACb,YAAY;oCACZ,UAAU;oCACV,aAAa;oCACb,mBAAmB;;;;;;4BAGzB;wBACF;;gBACF;YACF;YAEA,4CAA4C;YAC5C,SAAS,OAAO,CAAC,OAAO;0CAAC,CAAC,MAAM;oBAC9B,IAAI,CAAC,UAAU,GAAG,CAAC,MAAM;wBACvB,IAAI;4BAAE,KAAK,OAAO;wBAAI,EAAE,UAAM,CAAC;oBACjC;gBACF;;YACA,SAAS,OAAO,GAAG;QACrB;iCAAG;QAAC;QAAQ;KAAe;IAE3B,iBAAiB;IACjB,SAAS,YAAY,CAAkC;YAG1C;QAFX,MAAM,SAAS,EAAE,MAAM;QACvB,MAAM,OAAO,OAAO,OAAO,CAAC;QAC5B,MAAM,KAAK,iBAAA,4BAAA,gBAAA,KAAM,OAAO,cAAb,oCAAA,cAAe,EAAE;QAC5B,IAAI,CAAC,IAAI;QACT,UAAU,CAAC,OAAS,KAAK,GAAG,CAAC,CAAC,IAAO,EAAE,EAAE,KAAK,MAAM,EAAE,IAAI,KAAK,SAAS;oBAAE,GAAI,CAAC;oBAAgB,SAAS,KAAK,SAAS;gBAAC,IAAI;IAC7H;IAEA,SAAS,oBAAoB,EAAU,EAAE,IAAY;QACnD,UAAU,CAAC,OAAS,KAAK,GAAG,CAAC,CAAC,IAAO,EAAE,EAAE,KAAK,MAAM,EAAE,IAAI,KAAK,aAAa;oBAAE,GAAI,CAAC;oBAAc,cAAc;gBAAK,IAAI;IAC1H;IAEA,SAAS,8BAA8B,EAAU,EAAE,QAAkB;QACnE,UAAU,CAAC,OAAS,KAAK,GAAG,CAAC,CAAC,IAAO,EAAE,EAAE,KAAK,MAAM,EAAE,IAAI,KAAK,mBAAmB;oBAAE,GAAI,CAAC;oBAAoB,oBAAoB;gBAAS,IAAI;IAChJ;IAEA,SAAS,gBAAgB,CAAkB,EAAE,EAAU;QACrD,EAAE,eAAe;QACjB,EAAE,YAAY,CAAC,OAAO,CAAC,2BAA2B;IACpD;IAEA,SAAS,eAAe,CAAkB;QACxC,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,MAAM,aAAa,EAAE,YAAY,CAAC,OAAO,CAAC;QAC1C,MAAM,SAAS,EAAE,YAAY,CAAC,OAAO,CAAC;QACtC,IAAI,cAAc,QAAQ,EAAE,YAAY,CAAC,UAAU,GAAG,aAAa,SAAS;IAC9E;IAEA,SAAS,kBAAkB,CAAkB;YAG9B;QAFb,MAAM,SAAS;QACf,IAAI,OAAO,mBAAmB,EAAE,OAAO,OAAO,mBAAmB,CAAC,EAAE,OAAO,EAAE,EAAE,OAAO;QACtF,MAAM,MAAM,CAAC,iCAAA,OAAO,sBAAsB,AAAwB,cAArD,qDAAA,oCAAA,QAAgC,EAAE,OAAO,EAAE,EAAE,OAAO;QACjE,IAAI,KAAK;YAAE,MAAM,IAAI,SAAS,WAAW;YAAI,EAAE,QAAQ,CAAC,IAAI,UAAU,EAAE,IAAI,MAAM;YAAG,EAAE,QAAQ,CAAC;YAAO,OAAO;QAAG;QACjH,OAAO;IACT;IAEA,SAAS,kBAAkB,KAAY,EAAE,IAAU;QACjD,MAAM,cAAc;QACpB,MAAM,UAAU,CAAC;QACjB,iCAAiC;QACjC,MAAM,MAAM,OAAO,YAAY;QAC/B,IAAI,KAAK;YAAE,IAAI,eAAe;YAAI,MAAM,IAAI,SAAS,WAAW;YAAI,EAAE,aAAa,CAAC;YAAO,EAAE,QAAQ,CAAC;YAAO,IAAI,QAAQ,CAAC;QAAI;IAChI;IAEA,SAAS,iBAAiB,CAAkC;QAC1D,EAAE,cAAc;QAChB,MAAM,aAAa,EAAE,YAAY,CAAC,OAAO,CAAC;QAC1C,MAAM,SAAS,EAAE,YAAY,CAAC,OAAO,CAAC;QACtC,MAAM,OAAO,aAAc,KAAK,KAAK,CAAC,cAAsB;QAC5D,IAAI,QAAQ,kBAAkB;QAE9B,6EAA6E;QAC7E,IAAI,CAAC,OAAO;YACV,MAAM,SAAS,UAAU,OAAO;YAChC,MAAM,OAAO,OAAO,SAAS;YAC7B,MAAM,IAAI,SAAS,WAAW;YAC9B,IAAI,MAAM;gBAAE,EAAE,aAAa,CAAC;YAAO,OAAO;gBAAE,OAAO,WAAW,CAAC,SAAS,cAAc,CAAC;gBAAY,EAAE,QAAQ,CAAC,OAAO,UAAU,EAAG;YAAI;YACtI,EAAE,QAAQ,CAAC;YACX,QAAQ;QACV;QAEA,gCAAgC;QAChC,IAAI,CAAC,QAAQ,QAAQ;gBAOoF;YANvG,MAAM,WAAW,CAAC,kBAAA,4BAAA,MAAO,cAAc,AAAe,IAAI,AAAC,MAAO,cAAc,CAAU,aAAa,GAAG;YAC1G,MAAM,SAAS,qBAAA,+BAAA,SAAU,OAAO,CAAC;YACjC,MAAM,SAAS,mBAAA,6BAAA,OAAQ,OAAO,CAAC,EAAE;YACjC,MAAM,WAAW,OAAO,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;YAE7C,oDAAoD;YACpD,IAAI,YAAY,CAAC,SAAS,IAAI,KAAK,mBAAmB,SAAS,IAAI,KAAK,gBAAgB,KAAK,UAAU,EAAA,eAAA,OAAO,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,qBAA5B,mCAAA,aAAqC,IAAI,MAAK,UAAU,OAAO;gBACpK,MAAM,SAAS,UAAU,OAAO;gBAChC,OAAO,gBAAgB,CAAC,AAAC,oBAA0B,OAAP,QAAO,OAAK,OAAO,CAAC,CAAC;wBAAM;4BAAA,mBAAA,EAAE,aAAa,cAAf,uCAAA,iBAAiB,WAAW,CAAC;;gBACpG,MAAM,OAAO,SAAS,aAAa,CAAC;gBACpC,KAAK,YAAY,CAAC,kBAAkB;gBACpC,KAAK,YAAY,CAAC,mBAAmB;gBACrC,kBAAkB,OAAO;gBACzB,UAAU,CAAC,OAAS,KAAK,GAAG,CAAC,CAAC,IAAO,EAAE,EAAE,KAAK,UAAU,EAAE,IAAI,KAAK,SAAS;4BAAE,GAAI,CAAC;4BAAgB,SAAS,OAAQ,SAAS;wBAAC,IAAI;gBAClI;YACF;YAEA,qCAAqC;YACrC,MAAM,gBAAgB;YACtB,MAAM,WAAW,0BAAA,oCAAA,cAAe,OAAO,CAAC,EAAE;YAC1C,IAAI,CAAC,YAAY,aAAa,QAAQ;YACtC,UAAU,CAAC;gBACT,MAAM,MAAM;uBAAI;iBAAK;gBACrB,MAAM,KAAK,IAAI,SAAS,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;gBACzC,MAAM,KAAK,IAAI,SAAS,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;gBACzC,IAAI,KAAK,KAAK,KAAK,GAAG,OAAO;gBAC7B,MAAM,CAAC,EAAE,GAAG,IAAI,MAAM,CAAC,IAAI;gBAC3B,IAAI,MAAM,CAAC,IAAI,GAAG;gBAClB,OAAO;YACT;YACA;QACF;QAEA,IAAI,CAAC,MAAM;QAEX,yBAAyB;QACzB,MAAM,QAAQ,AAAC,KAAkB,OAAd,KAAK,GAAG,IAAG,KAAuC,OAApC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,KAAK,CAAC;QAElE,IAAI,KAAK,IAAI,KAAK,mBAAmB,OAAO;gBAE7B,gBAMD,eAWK;YAlBjB,mDAAmD;YACnD,MAAM,QAAO,iBAAA,AAAC,MAAM,cAAc,CAAU,aAAa,cAA5C,qCAAA,eAA8C,OAAO,CAAC;YACnE,MAAM,cAAc,SAAS,aAAa,CAAC;YAC3C,YAAY,YAAY,CAAC,kBAAkB;YAC3C,YAAY,YAAY,CAAC,mBAAmB;YAC5C,kBAAkB,OAAO;YAEzB,IAAI,QAAQ,EAAA,gBAAA,OAAO,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,KAAK,OAAO,CAAC,EAAE,eAA3C,oCAAA,cAA8C,IAAI,MAAK,QAAQ;gBACzE,MAAM,SAAS,KAAK,OAAO,CAAC,EAAE;gBAC9B,UAAU,CAAC;oBACT,MAAM,OAAO,KAAK,GAAG,CAAC,CAAC,IAAO,EAAE,EAAE,KAAK,UAAU,EAAE,IAAI,KAAK,SAAS;4BAAE,GAAI,CAAC;4BAAgB,SAAS,KAAK,SAAS;wBAAC,IAAI;oBACxH,KAAK,IAAI,CAAC;wBAAE,IAAI;wBAAO,MAAM;wBAAiB,WAAW,KAAK,SAAS;wBAAE,aAAa,KAAK,WAAW;wBAAE,eAAe;oBAAQ;oBAC/H,OAAO;gBACT;gBACA;YACF;YAEA,uEAAuE;YACvE,MAAM,YAAW,kBAAA,AAAC,MAAM,cAAc,CAAU,aAAa,cAA5C,sCAAA,gBAA8C,OAAO,CAAC;YACvE,MAAM,cAAc,WAAW,OAAO,SAAS,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,SAAS,OAAO,CAAC,EAAE,IAAI,OAAO,MAAM,GAAG;YACvG,UAAU,CAAC;gBACT,MAAM,OAAO;uBAAI;iBAAK;gBACtB,KAAK,MAAM,CAAC,KAAK,GAAG,CAAC,GAAG,cAAc,IAAI,GAAG;oBAAE,IAAI;oBAAO,MAAM;oBAAiB,WAAW,KAAK,SAAS;oBAAE,aAAa,KAAK,WAAW;oBAAE,eAAe;gBAAQ;gBAClK,OAAO;YACT;YACA;QACF;QAEA,IAAI,KAAK,IAAI,KAAK,cAAc,KAAK,IAAI,KAAK,kBAAkB;gBACvC,iBAES;YAFhC,MAAM,SAAS,SAAQ,kBAAA,AAAC,MAAM,cAAc,CAAU,aAAa,cAA5C,sCAAA,gBAA8C,OAAO,CAAC,eAAqC;YAClH,MAAM,SAAS,mBAAA,6BAAA,OAAQ,OAAO,CAAC,EAAE;YACjC,MAAM,aAAa,CAAC,CAAC,CAAC,UAAU,EAAA,gBAAA,OAAO,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,qBAA5B,oCAAA,cAAqC,IAAI,MAAK,MAAM;YAEpF,8EAA8E;YAC9E,IAAI,cAAc,SAAS,QAAQ;gBACjC,MAAM,SAAS,AAAC,YAAiB,OAAN,OAAM;gBACjC,MAAM,WAAW,SAAS,cAAc,CAAC;gBACzC,kBAAkB,OAAO;gBACzB,MAAM,OAAO,OAAO,SAAS;gBAC7B,MAAM,QAAQ,KAAK,KAAK,CAAC;oBACb;gBAAZ,MAAM,MAAM,CAAA,UAAA,KAAK,CAAC,EAAE,cAAR,qBAAA,UAAY;oBACX;gBAAb,MAAM,OAAO,CAAA,WAAA,KAAK,CAAC,EAAE,cAAR,sBAAA,WAAY;gBACzB,UAAU,CAAC;oBACT,MAAM,QAAQ,KAAK,SAAS,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;oBAC7C,MAAM,OAAgB;2BAAI;qBAAK;oBAC/B,MAAM,WAAW,KAAK,IAAI,KAAK,aAC1B;wBAAE,IAAI;wBAAO,MAAM;wBAAY,cAAc;oBAAG,IAChD;wBAAE,IAAI;wBAAO,MAAM;wBAAkB,UAAU,KAAK,QAAQ;wBAAE,cAAc,KAAK,YAAY;wBAAE,oBAAoB,EAAE;oBAAC;oBAC3H,4DAA4D;oBAC5D,KAAK,MAAM,CAAC,OAAO,GACjB;wBAAE,IAAI,AAAC,GAAS,OAAP,QAAO;wBAAO,MAAM;wBAAQ,SAAS;oBAAI,GAClD,UACA;wBAAE,IAAI,AAAC,GAAS,OAAP,QAAO;wBAAQ,MAAM;wBAAQ,SAAS;oBAAK;oBAEtD,OAAO;gBACT;gBACA;YACF;YAEA,gDAAgD;YAChD,MAAM,MAAM,SAAS,OAAO,SAAS,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,OAAO,OAAO,CAAC,EAAE,IAAI,OAAO,MAAM,GAAG;YAC3F,UAAU,CAAC;gBACT,MAAM,OAAO;uBAAI;iBAAK;gBACtB,MAAM,QAAQ,KAAK,IAAI,KAAK,aACvB;oBAAE,IAAI;oBAAO,MAAM;oBAAY,cAAc;gBAAG,IAChD;oBAAE,IAAI;oBAAO,MAAM;oBAAkB,UAAU,KAAK,QAAQ;oBAAE,cAAc,KAAK,YAAY;oBAAE,oBAAoB,EAAE;gBAAC;gBAC3H,KAAK,MAAM,CAAC,KAAK,GAAG,CAAC,GAAG,MAAM,IAAI,GAAG;gBACrC,OAAO;YACT;YACA;QACF;IACF;IAEA,SAAS,gBAAgB,CAAkB,EAAE,QAAgB;QAC3D,EAAE,cAAc;QAAI,EAAE,eAAe;QACrC,MAAM,SAAS,EAAE,YAAY,CAAC,OAAO,CAAC;QACtC,IAAI,CAAC,UAAU,WAAW,UAAU;QACpC,UAAU,CAAC;YACT,MAAM,MAAM;mBAAI;aAAK;YACrB,MAAM,KAAK,IAAI,SAAS,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;YACzC,MAAM,KAAK,IAAI,SAAS,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;YACzC,IAAI,KAAK,KAAK,KAAK,GAAG,OAAO;YAC7B,MAAM,CAAC,EAAE,GAAG,IAAI,MAAM,CAAC,IAAI;YAC3B,IAAI,MAAM,CAAC,IAAI,GAAG;YAClB,OAAO;QACT;IACF;IAEA,SAAS,oBAAoB,CAAkC,EAAE,QAAgB;QAC/E,EAAE,cAAc;QAAI,EAAE,eAAe;QACrC,MAAM,aAAa,EAAE,YAAY,CAAC,OAAO,CAAC;QAC1C,MAAM,SAAS,EAAE,YAAY,CAAC,OAAO,CAAC;QACtC,MAAM,OAAO,EAAE,aAAa;QAE5B,MAAM,QAAQ,kBAAkB;QAChC,IAAI,CAAC,OAAO;QAEZ,wDAAwD;QACxD,IAAI,CAAC,cAAc,QAAQ;YACzB,MAAM,MAAM,OAAO,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;YACxC,IAAI,OAAO,CAAC,IAAI,IAAI,KAAK,mBAAmB,IAAI,IAAI,KAAK,gBAAgB,GAAG;gBAC1E,KAAK,gBAAgB,CAAC,AAAC,oBAA0B,OAAP,QAAO,OAAK,OAAO,CAAC,CAAC;wBAAM;4BAAA,mBAAA,EAAE,aAAa,cAAf,uCAAA,iBAAiB,WAAW,CAAC;;gBAClG,MAAM,OAAO,SAAS,aAAa,CAAC;gBACpC,KAAK,YAAY,CAAC,kBAAkB;gBACpC,KAAK,YAAY,CAAC,mBAAmB;gBACrC,kBAAkB,OAAO;gBACzB,MAAM,OAAO,KAAK,SAAS;gBAC3B,UAAU,CAAC,OAAS,KAAK,GAAG,CAAC,CAAC,IAAO,EAAE,EAAE,KAAK,YAAY,EAAE,IAAI,KAAK,aAAa;4BAAE,GAAI,CAAC;4BAAc,cAAc;wBAAK,IAAI;YAChI;YACA;QACF;QAEA,IAAI,CAAC,YAAY;QACjB,MAAM,OAAO,KAAK,KAAK,CAAC;QACxB,IAAI,KAAK,IAAI,KAAK,mBAAmB,KAAK,IAAI,KAAK,kBAAkB;QAErE,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,MAAM,KAAK,AAAC,KAAkB,OAAd,KAAK,GAAG,IAAG,KAAuC,OAApC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,KAAK,CAAC;QAC/D,KAAK,YAAY,CAAC,kBAAkB;QACpC,KAAK,YAAY,CAAC,mBAAmB;QACrC,kBAAkB,OAAO;QAEzB,MAAM,OAAO,KAAK,SAAS;QAC3B,UAAU,CAAC;YACT,MAAM,OAAO,KAAK,GAAG,CAAC,CAAC,IAAO,EAAE,EAAE,KAAK,YAAY,EAAE,IAAI,KAAK,aAAa;oBAAE,GAAI,CAAC;oBAAc,cAAc;gBAAK,IAAI;YACvH,IAAI,KAAK,IAAI,KAAK,iBAAiB;gBACjC,KAAK,IAAI,CAAC;oBAAE;oBAAI,MAAM;oBAAiB,WAAW,KAAK,SAAS;oBAAE,aAAa,KAAK,WAAW;oBAAE,eAAe;gBAAQ;YAC1H,OAAO;gBACL,KAAK,IAAI,CAAC;oBAAE;oBAAI,MAAM;oBAAkB,UAAU,KAAK,QAAQ;oBAAE,cAAc,KAAK,YAAY;oBAAE,oBAAoB,EAAE;gBAAC;YAC3H;YACA,OAAO;QACT;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,0KAAO;;;;;0BACR,6LAAC;gBACC,KAAK;gBACL,KAAI;gBACJ,WAAU;gBACV,SAAS;gBACT,QAAQ;gBACR,aAAa;gBACb,YAAY;;;;;;;;;;;;AAIpB;GAtVgB;KAAA", "debugId": null}}, {"offset": {"line": 1852, "column": 0}, "map": {"version": 3, "sources": ["file:///T:/Development/TranscriptionModule/transcription-demo/src/app/report-templates/_components/BlockSidebar.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useMemo, useState } from \"react\";\nimport { Brain, LayoutGrid, List, Search, ChevronDown } from \"lucide-react\";\nimport type { DetectionItem } from \"./types\";\n\nexport function BlockSidebar({ detectionItems }: { detectionItems: DetectionItem[] }) {\n  const [q, setQ] = useState(\"\");\n  const [cat, setCat] = useState(\"all\");\n  const [open, setOpen] = useState<{general: boolean; categories: boolean; findings: boolean}>({ general: true, categories: true, findings: true });\n\n  const categories = useMemo(() => Array.from(new Set(detectionItems.map((d) => d.category))), [detectionItems]);\n  const filtered = useMemo(\n    () => detectionItems.filter((d) => (cat === \"all\" || d.category === cat) && d.name.toLowerCase().includes(q.toLowerCase())),\n    [detectionItems, q, cat]\n  );\n\n  function onDragStart(e: React.DragEvent, type: string, data: Record<string, unknown>) {\n    e.dataTransfer.effectAllowed = \"copyMove\";\n    e.dataTransfer.setData(\"application/json\", JSON.stringify({ type, ...data }));\n  }\n\n  return (\n    <div className=\"w-80 flex-shrink-0 border-l bg-muted/20 h-full flex flex-col\">\n      <h3 className=\"text-sm font-semibold px-4 py-3 border-b\">Add Blocks</h3>\n      <div className=\"flex-1 overflow-y-auto sidebar-scrollbar\">\n        <div className=\"border-b\">\n          <button className=\"w-full flex items-center justify-between px-3 py-2 text-xs uppercase text-muted-foreground\" onClick={() => setOpen((o) => ({ ...o, general: !o.general }))}>\n            <span>General</span><ChevronDown size={14} className={`transition-transform ${open.general ? \"rotate-180\" : \"\"}`} />\n          </button>\n          {open.general && (\n            <div className=\"px-3 pb-3\">\n              <div\n                draggable\n                onDragStart={(e) => onDragStart(e, \"AI_BLOCK\", {})}\n                className=\"p-2 rounded-lg border-2 border-orange-400 bg-orange-50 text-orange-700 flex items-center gap-2 cursor-grab\"\n              >\n                <Brain size={16} /> <span className=\"text-sm font-medium\">AI Instruction</span>\n              </div>\n            </div>\n          )}\n        </div>\n\n        <div className=\"border-b\">\n          <button className=\"w-full flex items-center justify-between px-3 py-2 text-xs uppercase text-muted-foreground\" onClick={() => setOpen((o) => ({ ...o, categories: !o.categories }))}>\n            <span>Categories</span><ChevronDown size={14} className={`transition-transform ${open.categories ? \"rotate-180\" : \"\"}`} />\n          </button>\n          {open.categories && (\n            <div className=\"px-3 pb-3 space-y-2\">\n              {categories.map((c) => (\n                <div key={c}\n                  draggable\n                  onDragStart={(e) => onDragStart(e, \"CATEGORY_BLOCK\", { category: c, categoryName: c })}\n                  className=\"p-2 rounded-lg border-2 border-border bg-muted/40 text-foreground flex items-center gap-2 cursor-grab\"\n                >\n                  <LayoutGrid size={16} /> <span className=\"text-sm font-medium\">{c}</span>\n                </div>\n              ))}\n            </div>\n          )}\n        </div>\n\n        <div>\n          <button className=\"w-full flex items-center justify-between px-3 py-2 text-xs uppercase text-muted-foreground\" onClick={() => setOpen((o) => ({ ...o, findings: !o.findings }))}>\n            <span>Individual Findings</span><ChevronDown size={14} className={`transition-transform ${open.findings ? \"rotate-180\" : \"\"}`} />\n          </button>\n          {open.findings && (\n            <div className=\"px-3 pb-3 space-y-2\">\n              <div className=\"relative\">\n                <Search size={14} className=\"absolute left-2 top-1/2 -translate-y-1/2 text-muted-foreground\" />\n                <input value={q} onChange={(e) => setQ(e.target.value)} placeholder=\"Search findings...\" className=\"w-full pl-7 pr-2 py-2 border rounded-md text-sm\" />\n              </div>\n              <select value={cat} onChange={(e) => setCat(e.target.value)} className=\"w-full p-2 border rounded-md text-sm\">\n                <option value=\"all\">All Categories</option>\n                {categories.map((c) => (<option key={c} value={c}>{c}</option>))}\n              </select>\n              <div className=\"max-h-[300px] overflow-y-auto sidebar-scrollbar pr-1 space-y-2\">\n                {filtered.map((it) => (\n                  <div key={it.id}\n                    draggable\n                    onDragStart={(e) => onDragStart(e, \"FINDING_BLOCK\", { findingId: it.id, findingName: it.name })}\n                    className=\"p-2 rounded-lg border-2 border-blue-400 bg-blue-50 text-blue-700 flex items-center gap-2 cursor-grab\"\n                  >\n                    <List size={16} /> <span className=\"text-sm font-medium\">{it.name}</span>\n                  </div>\n                ))}\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;AAMO,SAAS,aAAa,KAAuD;QAAvD,EAAE,cAAc,EAAuC,GAAvD;;IAC3B,MAAM,CAAC,GAAG,KAAK,GAAG,IAAA,yKAAQ,EAAC;IAC3B,MAAM,CAAC,KAAK,OAAO,GAAG,IAAA,yKAAQ,EAAC;IAC/B,MAAM,CAAC,MAAM,QAAQ,GAAG,IAAA,yKAAQ,EAA6D;QAAE,SAAS;QAAM,YAAY;QAAM,UAAU;IAAK;IAE/I,MAAM,aAAa,IAAA,wKAAO;4CAAC,IAAM,MAAM,IAAI,CAAC,IAAI,IAAI,eAAe,GAAG;oDAAC,CAAC,IAAM,EAAE,QAAQ;;2CAAK;QAAC;KAAe;IAC7G,MAAM,WAAW,IAAA,wKAAO;0CACtB,IAAM,eAAe,MAAM;kDAAC,CAAC,IAAM,CAAC,QAAQ,SAAS,EAAE,QAAQ,KAAK,GAAG,KAAK,EAAE,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,EAAE,WAAW;;yCACvH;QAAC;QAAgB;QAAG;KAAI;IAG1B,SAAS,YAAY,CAAkB,EAAE,IAAY,EAAE,IAA6B;QAClF,EAAE,YAAY,CAAC,aAAa,GAAG;QAC/B,EAAE,YAAY,CAAC,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC;YAAE;YAAM,GAAG,IAAI;QAAC;IAC5E;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAA2C;;;;;;0BACzD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAO,WAAU;gCAA6F,SAAS,IAAM,QAAQ,CAAC,IAAM,CAAC;4CAAE,GAAG,CAAC;4CAAE,SAAS,CAAC,EAAE,OAAO;wCAAC,CAAC;;kDACzK,6LAAC;kDAAK;;;;;;kDAAc,6LAAC,sOAAW;wCAAC,MAAM;wCAAI,WAAW,AAAC,wBAAwD,OAAjC,KAAK,OAAO,GAAG,eAAe;;;;;;;;;;;;4BAE7G,KAAK,OAAO,kBACX,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,SAAS;oCACT,aAAa,CAAC,IAAM,YAAY,GAAG,YAAY,CAAC;oCAChD,WAAU;;sDAEV,6LAAC,gNAAK;4CAAC,MAAM;;;;;;wCAAM;sDAAC,6LAAC;4CAAK,WAAU;sDAAsB;;;;;;;;;;;;;;;;;;;;;;;kCAMlE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAO,WAAU;gCAA6F,SAAS,IAAM,QAAQ,CAAC,IAAM,CAAC;4CAAE,GAAG,CAAC;4CAAE,YAAY,CAAC,EAAE,UAAU;wCAAC,CAAC;;kDAC/K,6LAAC;kDAAK;;;;;;kDAAiB,6LAAC,sOAAW;wCAAC,MAAM;wCAAI,WAAW,AAAC,wBAA2D,OAApC,KAAK,UAAU,GAAG,eAAe;;;;;;;;;;;;4BAEnH,KAAK,UAAU,kBACd,6LAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC,kBACf,6LAAC;wCACC,SAAS;wCACT,aAAa,CAAC,IAAM,YAAY,GAAG,kBAAkB;gDAAE,UAAU;gDAAG,cAAc;4CAAE;wCACpF,WAAU;;0DAEV,6LAAC,mOAAU;gDAAC,MAAM;;;;;;4CAAM;0DAAC,6LAAC;gDAAK,WAAU;0DAAuB;;;;;;;uCALxD;;;;;;;;;;;;;;;;kCAYlB,6LAAC;;0CACC,6LAAC;gCAAO,WAAU;gCAA6F,SAAS,IAAM,QAAQ,CAAC,IAAM,CAAC;4CAAE,GAAG,CAAC;4CAAE,UAAU,CAAC,EAAE,QAAQ;wCAAC,CAAC;;kDAC3K,6LAAC;kDAAK;;;;;;kDAA0B,6LAAC,sOAAW;wCAAC,MAAM;wCAAI,WAAW,AAAC,wBAAyD,OAAlC,KAAK,QAAQ,GAAG,eAAe;;;;;;;;;;;;4BAE1H,KAAK,QAAQ,kBACZ,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,mNAAM;gDAAC,MAAM;gDAAI,WAAU;;;;;;0DAC5B,6LAAC;gDAAM,OAAO;gDAAG,UAAU,CAAC,IAAM,KAAK,EAAE,MAAM,CAAC,KAAK;gDAAG,aAAY;gDAAqB,WAAU;;;;;;;;;;;;kDAErG,6LAAC;wCAAO,OAAO;wCAAK,UAAU,CAAC,IAAM,OAAO,EAAE,MAAM,CAAC,KAAK;wCAAG,WAAU;;0DACrE,6LAAC;gDAAO,OAAM;0DAAM;;;;;;4CACnB,WAAW,GAAG,CAAC,CAAC,kBAAO,6LAAC;oDAAe,OAAO;8DAAI;mDAAd;;;;;;;;;;;kDAEvC,6LAAC;wCAAI,WAAU;kDACZ,SAAS,GAAG,CAAC,CAAC,mBACb,6LAAC;gDACC,SAAS;gDACT,aAAa,CAAC,IAAM,YAAY,GAAG,iBAAiB;wDAAE,WAAW,GAAG,EAAE;wDAAE,aAAa,GAAG,IAAI;oDAAC;gDAC7F,WAAU;;kEAEV,6LAAC,6MAAI;wDAAC,MAAM;;;;;;oDAAM;kEAAC,6LAAC;wDAAK,WAAU;kEAAuB,GAAG,IAAI;;;;;;;+CALzD,GAAG,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAejC;GAvFgB;KAAA", "debugId": null}}, {"offset": {"line": 2220, "column": 0}, "map": {"version": 3, "sources": ["file:///T:/Development/TranscriptionModule/transcription-demo/src/app/report-templates/_components/ReportPreview.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useEffect, useState } from \"react\";\nimport type { Block, DetectionItem } from \"./types\";\n\nexport function ReportPreview({ blocks, detectionItems, active }: { blocks: Block[]; detectionItems: DetectionItem[]; active: boolean }) {\n  const [report, setReport] = useState<string>(\"\");\n  const [loading, setLoading] = useState(false);\n\n  function valueForFinding(id: string): string {\n    // Demo resolver: just return placeholder or the finding name\n    const meta = detectionItems.find((d) => d.id === id);\n    return meta ? `[${meta.name} value]` : \"[value]\";\n  }\n\n  async function generate() {\n    setLoading(true);\n    let content = \"\";\n    for (const b of blocks) {\n      if (b.type === \"TEXT\") {\n        const div = document.createElement(\"div\");\n        div.innerHTML = b.content;\n        content += div.innerText + \"\\n\";\n      } else if (b.type === \"AI_BLOCK\") {\n        content += `\\n--- AI Summary for \"${stripHtml(b.innerContent).slice(0, 80)}\" ---\\n[Simulating AI response...]\\n\\n`;\n      } else if (b.type === \"FINDING_BLOCK\") {\n        content += ` ${valueForFinding(b.findingId)} `;\n      } else if (b.type === \"CATEGORY_BLOCK\") {\n        content += `\\n**${b.categoryName}**\\n`;\n        for (const id of b.selectedFindingIds) {\n          const meta = detectionItems.find((d) => d.id === id);\n          content += `- ${meta?.name ?? id}: ${valueForFinding(id)}\\n`;\n        }\n        content += \"\\n\";\n      }\n    }\n    await new Promise((r) => setTimeout(r, 200));\n    setReport(content.replace(\"[Simulating AI response...]\", \"Based on findings, values are within expected ranges.\"));\n    setLoading(false);\n  }\n\n  useEffect(() => {\n    if (active) {\n      void generate();\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [active, JSON.stringify(blocks)]);\n\n  return (\n    <div className=\"flex flex-col h-full\">\n      <div className=\"flex-1 bg-muted/30 rounded-md p-3 whitespace-pre-wrap font-mono text-xs overflow-auto\">\n        {report || (active ? \"Building preview...\" : \"Open preview to see the generated report.\")}\n      </div>\n    </div>\n  );\n}\n\nfunction stripHtml(html: string) {\n  const d = document.createElement(\"div\"); d.innerHTML = html; return d.innerText;\n}\n\n"], "names": [], "mappings": ";;;;;AAEA;;;AAFA;;AAKO,SAAS,cAAc,KAAyG;QAAzG,EAAE,MAAM,EAAE,cAAc,EAAE,MAAM,EAAyE,GAAzG;;IAC5B,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,yKAAQ,EAAS;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,yKAAQ,EAAC;IAEvC,SAAS,gBAAgB,EAAU;QACjC,6DAA6D;QAC7D,MAAM,OAAO,eAAe,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;QACjD,OAAO,OAAO,AAAC,IAAa,OAAV,KAAK,IAAI,EAAC,aAAW;IACzC;IAEA,eAAe;QACb,WAAW;QACX,IAAI,UAAU;QACd,KAAK,MAAM,KAAK,OAAQ;YACtB,IAAI,EAAE,IAAI,KAAK,QAAQ;gBACrB,MAAM,MAAM,SAAS,aAAa,CAAC;gBACnC,IAAI,SAAS,GAAG,EAAE,OAAO;gBACzB,WAAW,IAAI,SAAS,GAAG;YAC7B,OAAO,IAAI,EAAE,IAAI,KAAK,YAAY;gBAChC,WAAW,AAAC,yBAA+D,OAAvC,UAAU,EAAE,YAAY,EAAE,KAAK,CAAC,GAAG,KAAI;YAC7E,OAAO,IAAI,EAAE,IAAI,KAAK,iBAAiB;gBACrC,WAAW,AAAC,IAAgC,OAA7B,gBAAgB,EAAE,SAAS,GAAE;YAC9C,OAAO,IAAI,EAAE,IAAI,KAAK,kBAAkB;gBACtC,WAAW,AAAC,OAAqB,OAAf,EAAE,YAAY,EAAC;gBACjC,KAAK,MAAM,MAAM,EAAE,kBAAkB,CAAE;oBACrC,MAAM,OAAO,eAAe,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;wBACjC;oBAAhB,WAAW,AAAC,KAAyB,OAArB,CAAA,aAAA,iBAAA,2BAAA,KAAM,IAAI,cAAV,wBAAA,aAAc,IAAG,MAAwB,OAApB,gBAAgB,KAAI;gBAC3D;gBACA,WAAW;YACb;QACF;QACA,MAAM,IAAI,QAAQ,CAAC,IAAM,WAAW,GAAG;QACvC,UAAU,QAAQ,OAAO,CAAC,+BAA+B;QACzD,WAAW;IACb;IAEA,IAAA,0KAAS;mCAAC;YACR,IAAI,QAAQ;gBACV,KAAK;YACP;QACA,uDAAuD;QACzD;kCAAG;QAAC;QAAQ,KAAK,SAAS,CAAC;KAAQ;IAEnC,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACZ,UAAU,CAAC,SAAS,wBAAwB,2CAA2C;;;;;;;;;;;AAIhG;GAlDgB;KAAA;AAoDhB,SAAS,UAAU,IAAY;IAC7B,MAAM,IAAI,SAAS,aAAa,CAAC;IAAQ,EAAE,SAAS,GAAG;IAAM,OAAO,EAAE,SAAS;AACjF", "debugId": null}}, {"offset": {"line": 2309, "column": 0}, "map": {"version": 3, "sources": ["file:///T:/Development/TranscriptionModule/transcription-demo/src/app/report-templates/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useEffect, useMemo, useState } from \"react\";\nimport { WorkspaceNav } from \"@/components/workspace-nav\";\nimport { Button } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport { getConvex } from \"@/lib/convexClient\";\n\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\";\nimport { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from \"@/components/ui/accordion\";\n\nimport { EditorCanvas } from \"./_components/EditorCanvas\";\nimport { BlockSidebar } from \"./_components/BlockSidebar\";\nimport { ReportPreview } from \"./_components/ReportPreview\";\nimport type { Block, DetectionItem } from \"./_components/types\";\n\nexport default function ReportTemplatesPage() {\n  const [title, setTitle] = useState(\"Consultation Report\");\n  const [blocks, setBlocks] = useState<Block[]>([\n    { id: crypto.randomUUID(), type: \"TEXT\", content: \"<h2>Consultation Report</h2><div>Start typing your report here...</div>\" },\n  ]);\n  const [detectionItems, setDetectionItems] = useState<DetectionItem[]>([]);\n  const [previewOpen, setPreviewOpen] = useState(false);\n\n\n  const convex = getConvex();\n  const loose = convex as unknown as { query: (name: string, args?: unknown) => Promise<unknown>; mutation: (name: string, args?: unknown) => Promise<unknown>; action: (name: string, args?: unknown) => Promise<unknown> };\n\n  type RawTemplate = { _id?: string; id?: string; title?: string; blocks?: unknown[]; published?: boolean; ownerUserId?: string; updatedAt?: number };\n  const [templates, setTemplates] = useState<Array<{ id?: string; title: string; blocks: Block[] }>>([]);\n  const [currentId, setCurrentId] = useState<string | undefined>(undefined);\n\n  function normalizeBlocks(rawBlocks: unknown[] | undefined): Block[] {\n    if (!rawBlocks) return [];\n    return rawBlocks.map((rb) => {\n      const any = rb as any;\n      if (any && typeof any === \"object\" && typeof any.type === \"string\") return any as Block;\n      // Convert prior editor shapes to TEXT for compatibility\n      if (any.kind === \"text\") return { id: crypto.randomUUID(), type: \"TEXT\", content: (any.value ?? \"\") as string } as Block;\n      if (any.kind === \"section\") return { id: crypto.randomUUID(), type: \"TEXT\", content: `<h3>${any.value ?? \"\"}</h3>` } as Block;\n      if (any.kind === \"field\") return { id: crypto.randomUUID(), type: \"TEXT\", content: `<p><strong>${any.label ?? any.value ?? \"Field\"}</strong>: ______</p>` } as Block;\n      return { id: crypto.randomUUID(), type: \"TEXT\", content: \"\" } as Block;\n    });\n  }\n\n  function normalizeTemplate(raw: unknown) {\n    const r = raw as RawTemplate;\n    const id = r.id ?? (r._id ? String(r._id) : undefined);\n    return { id, title: r.title ?? \"Untitled\", blocks: normalizeBlocks(r.blocks) } as { id?: string; title: string; blocks: Block[] };\n  }\n\n  useEffect(() => {\n    let ignore = false;\n    async function loadTemplates() {\n      if (!convex) return;\n      try {\n        const list = await (loose.query(\"templates:listTemplates\", {}) as Promise<unknown>);\n        const items = (list as unknown[]).map(normalizeTemplate);\n        if (!ignore) {\n          setTemplates(items);\n          const first = items[0];\n          if (first) { setCurrentId(first.id); setTitle(first.title); setBlocks(first.blocks); }\n        }\n      } catch {}\n    }\n    loadTemplates();\n    return () => { ignore = true; };\n  }, [convex, loose]);\n\n  // Load available definitions for sidebar (Convex)\n  useEffect(() => {\n    let cancelled = false;\n    async function load() {\n      if (!convex) return;\n      try {\n        const res = await loose.query(\"detections:listDefinitions\", {});\n        const items = (res as unknown[]).map((r) => {\n          const doc = r as { id?: string; _id?: string; name?: string; category?: string };\n          const id = doc.id ?? (doc._id ? String(doc._id) : crypto.randomUUID());\n          return { id, name: doc.name ?? \"\", category: doc.category ?? \"Uncategorized\" } as DetectionItem;\n        });\n        if (!cancelled) setDetectionItems(items);\n      } catch {}\n    }\n    load();\n    return () => { cancelled = true; };\n  }, [convex, loose]);\n\n  const leftPanel = (\n    <Accordion\n      type=\"single\"\n      defaultValue=\"editor\"\n      onValueChange={(v) => setPreviewOpen(v === \"preview\")}\n      className=\"h-[calc(100vh-8rem)]\"\n    >\n      <AccordionItem value=\"editor\" className=\"border-none\">\n        <AccordionTrigger className=\"justify-between\">\n          <div className=\"text-sm font-medium\">Editor</div>\n        </AccordionTrigger>\n        <AccordionContent className=\"pt-2 flex flex-col min-h-[calc(100vh-18rem)]\">\n          <div className=\"flex items-center justify-between gap-2 mb-2\">\n            <Input value={title} onChange={(e) => setTitle(e.target.value)} className=\"h-9\" />\n          </div>\n          <EditorCanvas blocks={blocks} setBlocks={(updater) => setBlocks((prev) => updater(prev))} detectionItems={detectionItems} />\n        </AccordionContent>\n      </AccordionItem>\n      <AccordionItem value=\"preview\" className=\"border-none\">\n        <AccordionTrigger className=\"justify-between\">\n          <div className=\"text-sm font-medium\">Preview</div>\n        </AccordionTrigger>\n        <AccordionContent className=\"pt-2 flex-1 min-h-0\">\n          <div className=\"flex-1 min-h-0\">\n            <ReportPreview blocks={blocks} detectionItems={detectionItems} active={previewOpen} />\n          </div>\n        </AccordionContent>\n      </AccordionItem>\n    </Accordion>\n  );\n\n  return (\n    <div className=\"container mx-auto p-6 space-y-4\">\n      <WorkspaceNav />\n      <style>{`[data-placeholder]:empty::before { content: attr(data-placeholder); color: #9ca3af; font-style: italic; } .sidebar-scrollbar::-webkit-scrollbar{width:8px}.sidebar-scrollbar::-webkit-scrollbar-thumb{background:#d1d5db;border-radius:4px}.sidebar-scrollbar::-webkit-scrollbar-thumb:hover{background:#9ca3af}`}</style>\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center gap-2\">\n          <span className=\"text-sm text-muted-foreground\">Template</span>\n          <Select value={currentId ?? \"\"} onValueChange={(id) => {\n            setCurrentId(id);\n            const t = templates.find((x) => x.id === id);\n            if (t) { setTitle(t.title); setBlocks(t.blocks); }\n          }}>\n            <SelectTrigger className=\"w-[260px]\"><SelectValue placeholder=\"Select template\" /></SelectTrigger>\n            <SelectContent>\n              {templates.map((t) => (<SelectItem key={t.id} value={t.id!}>{t.title}</SelectItem>))}\n            </SelectContent>\n          </Select>\n          <Button variant=\"outline\" onClick={() => {\n            const id = `local_${crypto.randomUUID()}`;\n            const t = { id, title: \"Untitled Template\", blocks: [] as Block[] };\n            setTemplates((prev) => [...prev, t]);\n            setCurrentId(id);\n            setTitle(t.title);\n            setBlocks(t.blocks);\n          }}>New</Button>\n          <Button variant=\"outline\" onClick={() => {\n            if (!currentId) return;\n            const base = templates.find((t) => t.id === currentId);\n            if (!base) return;\n            const id = `local_${crypto.randomUUID()}`;\n            const title = `${base.title} (copy)`;\n            const blocks = base.blocks.map((b) => ({ ...b, id: crypto.randomUUID() })) as Block[];\n            const clone = { id, title, blocks };\n            setTemplates((prev) => [...prev, clone]);\n            setCurrentId(id);\n            setTitle(title);\n            setBlocks(blocks);\n          }}>Clone</Button>\n          <Button variant=\"destructive\" onClick={async () => {\n            if (convex && currentId) {\n              await loose.mutation(\"templates:removeTemplate\", { id: currentId });\n              const list = await (loose.query(\"templates:listTemplates\", {}) as Promise<unknown>);\n              const items = (list as unknown[]).map(normalizeTemplate);\n              setTemplates(items);\n              const first = items[0];\n              if (first) { setCurrentId(first.id); setTitle(first.title); setBlocks(first.blocks); } else { setCurrentId(undefined); setTitle(\"Untitled Template\"); setBlocks([]); }\n            }\n          }}>Delete</Button>\n        </div>\n        <div className=\"flex items-center gap-2\">\n          <Button variant=\"outline\" onClick={async () => {\n            if (!convex) return;\n            await loose.action(\"templates:seedTemplates\", {});\n            const list = await (loose.query(\"templates:listTemplates\", {}) as Promise<unknown>);\n            const items = (list as unknown[]).map(normalizeTemplate);\n            setTemplates(items);\n            const first = items[0];\n            if (first) { setCurrentId(first.id); setTitle(first.title); setBlocks(first.blocks); }\n          }}>Seed Templates</Button>\n          <Button variant=\"secondary\" onClick={async () => {\n            if (convex) {\n              await loose.mutation(\"templates:upsertTemplate\", { id: currentId, title, blocks, published: false, ownerUserId: \"demo-user\" });\n              const list = await (loose.query(\"templates:listTemplates\", {}) as Promise<unknown>);\n              setTemplates((list as unknown[]).map(normalizeTemplate));\n            }\n          }}>Save</Button>\n          <Button onClick={async () => {\n            if (convex) {\n              await loose.mutation(\"templates:upsertTemplate\", { id: currentId, title, blocks, published: true, ownerUserId: \"demo-user\" });\n              const list = await (loose.query(\"templates:listTemplates\", {}) as Promise<unknown>);\n              setTemplates((list as unknown[]).map(normalizeTemplate));\n            }\n          }}>Publish</Button>\n        </div>\n      </div>\n\n      <div className=\"grid grid-cols-[1fr_320px] gap-4\">\n        {leftPanel}\n        <BlockSidebar detectionItems={detectionItems} />\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;;;AAbA;;;;;;;;;;;AAgBe,SAAS;;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,yKAAQ,EAAC;IACnC,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,yKAAQ,EAAU;QAC5C;YAAE,IAAI,OAAO,UAAU;YAAI,MAAM;YAAQ,SAAS;QAA0E;KAC7H;IACD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,yKAAQ,EAAkB,EAAE;IACxE,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,yKAAQ,EAAC;IAG/C,MAAM,SAAS,IAAA,0IAAS;IACxB,MAAM,QAAQ;IAGd,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,yKAAQ,EAAyD,EAAE;IACrG,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,yKAAQ,EAAqB;IAE/D,SAAS,gBAAgB,SAAgC;QACvD,IAAI,CAAC,WAAW,OAAO,EAAE;QACzB,OAAO,UAAU,GAAG,CAAC,CAAC;YACpB,MAAM,MAAM;YACZ,IAAI,OAAO,OAAO,QAAQ,YAAY,OAAO,IAAI,IAAI,KAAK,UAAU,OAAO;gBAEQ;YADnF,wDAAwD;YACxD,IAAI,IAAI,IAAI,KAAK,QAAQ,OAAO;gBAAE,IAAI,OAAO,UAAU;gBAAI,MAAM;gBAAQ,SAAU,CAAA,aAAA,IAAI,KAAK,cAAT,wBAAA,aAAa;YAAc;gBAClB;YAA5F,IAAI,IAAI,IAAI,KAAK,WAAW,OAAO;gBAAE,IAAI,OAAO,UAAU;gBAAI,MAAM;gBAAQ,SAAS,AAAC,OAAsB,OAAhB,CAAA,cAAA,IAAI,KAAK,cAAT,yBAAA,cAAa,IAAG;YAAO;gBAClB,YAAA;YAAjG,IAAI,IAAI,IAAI,KAAK,SAAS,OAAO;gBAAE,IAAI,OAAO,UAAU;gBAAI,MAAM;gBAAQ,SAAS,AAAC,cAA+C,OAAlC,CAAA,OAAA,CAAA,aAAA,IAAI,KAAK,cAAT,wBAAA,aAAa,IAAI,KAAK,cAAtB,kBAAA,OAA0B,SAAQ;YAAuB;YAC1J,OAAO;gBAAE,IAAI,OAAO,UAAU;gBAAI,MAAM;gBAAQ,SAAS;YAAG;QAC9D;IACF;IAEA,SAAS,kBAAkB,GAAY;QACrC,MAAM,IAAI;YACC;QAAX,MAAM,KAAK,CAAA,QAAA,EAAE,EAAE,cAAJ,mBAAA,QAAS,EAAE,GAAG,GAAG,OAAO,EAAE,GAAG,IAAI;YACxB;QAApB,OAAO;YAAE;YAAI,OAAO,CAAA,WAAA,EAAE,KAAK,cAAP,sBAAA,WAAW;YAAY,QAAQ,gBAAgB,EAAE,MAAM;QAAE;IAC/E;IAEA,IAAA,0KAAS;yCAAC;YACR,IAAI,SAAS;YACb,eAAe;gBACb,IAAI,CAAC,QAAQ;gBACb,IAAI;oBACF,MAAM,OAAO,MAAO,MAAM,KAAK,CAAC,2BAA2B,CAAC;oBAC5D,MAAM,QAAQ,AAAC,KAAmB,GAAG,CAAC;oBACtC,IAAI,CAAC,QAAQ;wBACX,aAAa;wBACb,MAAM,QAAQ,KAAK,CAAC,EAAE;wBACtB,IAAI,OAAO;4BAAE,aAAa,MAAM,EAAE;4BAAG,SAAS,MAAM,KAAK;4BAAG,UAAU,MAAM,MAAM;wBAAG;oBACvF;gBACF,EAAE,UAAM,CAAC;YACX;YACA;YACA;iDAAO;oBAAQ,SAAS;gBAAM;;QAChC;wCAAG;QAAC;QAAQ;KAAM;IAElB,kDAAkD;IAClD,IAAA,0KAAS;yCAAC;YACR,IAAI,YAAY;YAChB,eAAe;gBACb,IAAI,CAAC,QAAQ;gBACb,IAAI;oBACF,MAAM,MAAM,MAAM,MAAM,KAAK,CAAC,8BAA8B,CAAC;oBAC7D,MAAM,QAAQ,AAAC,IAAkB,GAAG;oEAAC,CAAC;4BACpC,MAAM,MAAM;gCACD;4BAAX,MAAM,KAAK,CAAA,UAAA,IAAI,EAAE,cAAN,qBAAA,UAAW,IAAI,GAAG,GAAG,OAAO,IAAI,GAAG,IAAI,OAAO,UAAU;gCAChD,WAA0B;4BAA7C,OAAO;gCAAE;gCAAI,MAAM,CAAA,YAAA,IAAI,IAAI,cAAR,uBAAA,YAAY;gCAAI,UAAU,CAAA,gBAAA,IAAI,QAAQ,cAAZ,2BAAA,gBAAgB;4BAAgB;wBAC/E;;oBACA,IAAI,CAAC,WAAW,kBAAkB;gBACpC,EAAE,UAAM,CAAC;YACX;YACA;YACA;iDAAO;oBAAQ,YAAY;gBAAM;;QACnC;wCAAG;QAAC;QAAQ;KAAM;IAElB,MAAM,0BACJ,6LAAC,qJAAS;QACR,MAAK;QACL,cAAa;QACb,eAAe,CAAC,IAAM,eAAe,MAAM;QAC3C,WAAU;;0BAEV,6LAAC,yJAAa;gBAAC,OAAM;gBAAS,WAAU;;kCACtC,6LAAC,4JAAgB;wBAAC,WAAU;kCAC1B,cAAA,6LAAC;4BAAI,WAAU;sCAAsB;;;;;;;;;;;kCAEvC,6LAAC,4JAAgB;wBAAC,WAAU;;0CAC1B,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,6IAAK;oCAAC,OAAO;oCAAO,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;oCAAG,WAAU;;;;;;;;;;;0CAE5E,6LAAC,oLAAY;gCAAC,QAAQ;gCAAQ,WAAW,CAAC,UAAY,UAAU,CAAC,OAAS,QAAQ;gCAAQ,gBAAgB;;;;;;;;;;;;;;;;;;0BAG9G,6LAAC,yJAAa;gBAAC,OAAM;gBAAU,WAAU;;kCACvC,6LAAC,4JAAgB;wBAAC,WAAU;kCAC1B,cAAA,6LAAC;4BAAI,WAAU;sCAAsB;;;;;;;;;;;kCAEvC,6LAAC,4JAAgB;wBAAC,WAAU;kCAC1B,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,sLAAa;gCAAC,QAAQ;gCAAQ,gBAAgB;gCAAgB,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAOjF,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,yJAAY;;;;;0BACb,6LAAC;0BAAQ;;;;;;0BACT,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAAgC;;;;;;0CAChD,6LAAC,+IAAM;gCAAC,OAAO,sBAAA,uBAAA,YAAa;gCAAI,eAAe,CAAC;oCAC9C,aAAa;oCACb,MAAM,IAAI,UAAU,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;oCACzC,IAAI,GAAG;wCAAE,SAAS,EAAE,KAAK;wCAAG,UAAU,EAAE,MAAM;oCAAG;gCACnD;;kDACE,6LAAC,sJAAa;wCAAC,WAAU;kDAAY,cAAA,6LAAC,oJAAW;4CAAC,aAAY;;;;;;;;;;;kDAC9D,6LAAC,sJAAa;kDACX,UAAU,GAAG,CAAC,CAAC,kBAAO,6LAAC,mJAAU;gDAAY,OAAO,EAAE,EAAE;0DAAI,EAAE,KAAK;+CAA5B,EAAE,EAAE;;;;;;;;;;;;;;;;0CAGhD,6LAAC,+IAAM;gCAAC,SAAQ;gCAAU,SAAS;oCACjC,MAAM,KAAK,AAAC,SAA4B,OAApB,OAAO,UAAU;oCACrC,MAAM,IAAI;wCAAE;wCAAI,OAAO;wCAAqB,QAAQ,EAAE;oCAAY;oCAClE,aAAa,CAAC,OAAS;+CAAI;4CAAM;yCAAE;oCACnC,aAAa;oCACb,SAAS,EAAE,KAAK;oCAChB,UAAU,EAAE,MAAM;gCACpB;0CAAG;;;;;;0CACH,6LAAC,+IAAM;gCAAC,SAAQ;gCAAU,SAAS;oCACjC,IAAI,CAAC,WAAW;oCAChB,MAAM,OAAO,UAAU,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;oCAC5C,IAAI,CAAC,MAAM;oCACX,MAAM,KAAK,AAAC,SAA4B,OAApB,OAAO,UAAU;oCACrC,MAAM,QAAQ,AAAC,GAAa,OAAX,KAAK,KAAK,EAAC;oCAC5B,MAAM,SAAS,KAAK,MAAM,CAAC,GAAG,CAAC,CAAC,IAAM,CAAC;4CAAE,GAAG,CAAC;4CAAE,IAAI,OAAO,UAAU;wCAAG,CAAC;oCACxE,MAAM,QAAQ;wCAAE;wCAAI;wCAAO;oCAAO;oCAClC,aAAa,CAAC,OAAS;+CAAI;4CAAM;yCAAM;oCACvC,aAAa;oCACb,SAAS;oCACT,UAAU;gCACZ;0CAAG;;;;;;0CACH,6LAAC,+IAAM;gCAAC,SAAQ;gCAAc,SAAS;oCACrC,IAAI,UAAU,WAAW;wCACvB,MAAM,MAAM,QAAQ,CAAC,4BAA4B;4CAAE,IAAI;wCAAU;wCACjE,MAAM,OAAO,MAAO,MAAM,KAAK,CAAC,2BAA2B,CAAC;wCAC5D,MAAM,QAAQ,AAAC,KAAmB,GAAG,CAAC;wCACtC,aAAa;wCACb,MAAM,QAAQ,KAAK,CAAC,EAAE;wCACtB,IAAI,OAAO;4CAAE,aAAa,MAAM,EAAE;4CAAG,SAAS,MAAM,KAAK;4CAAG,UAAU,MAAM,MAAM;wCAAG,OAAO;4CAAE,aAAa;4CAAY,SAAS;4CAAsB,UAAU,EAAE;wCAAG;oCACvK;gCACF;0CAAG;;;;;;;;;;;;kCAEL,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+IAAM;gCAAC,SAAQ;gCAAU,SAAS;oCACjC,IAAI,CAAC,QAAQ;oCACb,MAAM,MAAM,MAAM,CAAC,2BAA2B,CAAC;oCAC/C,MAAM,OAAO,MAAO,MAAM,KAAK,CAAC,2BAA2B,CAAC;oCAC5D,MAAM,QAAQ,AAAC,KAAmB,GAAG,CAAC;oCACtC,aAAa;oCACb,MAAM,QAAQ,KAAK,CAAC,EAAE;oCACtB,IAAI,OAAO;wCAAE,aAAa,MAAM,EAAE;wCAAG,SAAS,MAAM,KAAK;wCAAG,UAAU,MAAM,MAAM;oCAAG;gCACvF;0CAAG;;;;;;0CACH,6LAAC,+IAAM;gCAAC,SAAQ;gCAAY,SAAS;oCACnC,IAAI,QAAQ;wCACV,MAAM,MAAM,QAAQ,CAAC,4BAA4B;4CAAE,IAAI;4CAAW;4CAAO;4CAAQ,WAAW;4CAAO,aAAa;wCAAY;wCAC5H,MAAM,OAAO,MAAO,MAAM,KAAK,CAAC,2BAA2B,CAAC;wCAC5D,aAAa,AAAC,KAAmB,GAAG,CAAC;oCACvC;gCACF;0CAAG;;;;;;0CACH,6LAAC,+IAAM;gCAAC,SAAS;oCACf,IAAI,QAAQ;wCACV,MAAM,MAAM,QAAQ,CAAC,4BAA4B;4CAAE,IAAI;4CAAW;4CAAO;4CAAQ,WAAW;4CAAM,aAAa;wCAAY;wCAC3H,MAAM,OAAO,MAAO,MAAM,KAAK,CAAC,2BAA2B,CAAC;wCAC5D,aAAa,AAAC,KAAmB,GAAG,CAAC;oCACvC;gCACF;0CAAG;;;;;;;;;;;;;;;;;;0BAIP,6LAAC;gBAAI,WAAU;;oBACZ;kCACD,6LAAC,oLAAY;wBAAC,gBAAgB;;;;;;;;;;;;;;;;;;AAItC;GAzLwB;KAAA", "debugId": null}}]}