"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import type { AutoDetectedFinding, Block, DetectionItem } from "./types";

export function ReportPreview({ blocks, detectionItems }: { blocks: Block[]; detectionItems: DetectionItem[] }) {
  const [report, setReport] = useState<string>("");
  const [loading, setLoading] = useState(false);

  function valueForFinding(id: string): string {
    // Demo resolver: just return placeholder or the finding name
    const meta = detectionItems.find((d) => d.id === id);
    return meta ? `[${meta.name} value]` : "[value]";
  }

  async function generate() {
    setLoading(true);
    let content = "";
    for (const b of blocks) {
      if (b.type === "TEXT") {
        const div = document.createElement("div");
        div.innerHTML = b.content;
        content += div.innerText + "\n";
      } else if (b.type === "AI_BLOCK") {
        content += `\n--- AI Summary for "${stripHtml(b.innerContent).slice(0, 80)}" ---\n[Simulating AI response...]\n\n`;
      } else if (b.type === "FINDING_BLOCK") {
        content += ` ${valueForFinding(b.findingId)} `;
      } else if (b.type === "CATEGORY_BLOCK") {
        content += `\n**${b.categoryName}**\n`;
        for (const id of b.selectedFindingIds) {
          const meta = detectionItems.find((d) => d.id === id);
          content += `- ${meta?.name ?? id}: ${valueForFinding(id)}\n`;
        }
        content += "\n";
      }
    }
    await new Promise((r) => setTimeout(r, 500));
    setReport(content.replace("[Simulating AI response...]", "Based on findings, values are within expected ranges."));
    setLoading(false);
  }

  return (
    <div className="flex flex-col h-full">
      <div className="flex items-center justify-between gap-2 mb-2">
        <Button onClick={generate} disabled={loading || blocks.length === 0}>{loading ? "Generating..." : "Generate Report"}</Button>
      </div>
      <div className="flex-1 bg-muted/30 rounded-md p-3 whitespace-pre-wrap font-mono text-xs overflow-auto">
        {report || "Report preview will appear here..."}
      </div>
    </div>
  );
}

function stripHtml(html: string) {
  const d = document.createElement("div"); d.innerHTML = html; return d.innerText;
}

