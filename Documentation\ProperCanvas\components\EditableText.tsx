import React, { useRef, useCallback } from 'react';
import { NavDirection, EditableTextProps } from '../types';

const EditableText = (props: EditableTextProps) => {
    const { 
        id,
        blockType,
        content,
        onUpdatePart, 
        onDropPart, 
        onSplitPart,
        onMergePart,
        onSelectPart,
        onNavigate,
        onDragStart,
        showIndicator, 
        hideIndicator,
        ref,
    } = props;
    const dropIndexRef = useRef<number>(0);
    const localRef = useRef<HTMLSpanElement | null>(null);

    // This callback ref pattern is a robust way to handle both a local ref
    // and a forwarded ref from props, matching the pattern in other blocks.
    const setRefs = useCallback((node: HTMLSpanElement | null) => {
        localRef.current = node;
        if (typeof ref === 'function') {
          ref(node);
        } else if (ref) {
          (ref as React.MutableRefObject<HTMLSpanElement | null>).current = node;
        }
    }, [ref]);

    const handleInput = (e: React.FormEvent<HTMLSpanElement>) => {
        // Handle immediate updates if needed, though onBlur is often sufficient
    };
    
    const handleBlur = (e: React.FocusEvent<HTMLSpanElement>) => {
        const newText = e.currentTarget.innerText ?? '';
        if (newText !== content) {
            onUpdatePart(id, { content: newText });
        }
    };

    const handleFocus = () => {
        onSelectPart(id);
    };

    const getDropInfo = useCallback((clientX: number, clientY: number): { index: number, rect: DOMRect } | null => {
        let range: Range | null = null;
        if (document.caretRangeFromPoint) {
            range = document.caretRangeFromPoint(clientX, clientY);
        } else if ((document as any).caretPositionFromPoint) {
            const pos = (document as any).caretPositionFromPoint(clientX, clientY);
            if (pos) {
              range = document.createRange();
              range.setStart(pos.offsetNode, pos.offset);
              range.collapse(true);
            }
        }
        
        const spanEl = localRef.current;
        if (!range || !spanEl || !spanEl.contains(range.startContainer)) { return null; }
        
        const tempRange = document.createRange();
        tempRange.selectNodeContents(spanEl);
        tempRange.setEnd(range.startContainer, range.startOffset);
        const index = tempRange.toString().length;

        return { index, rect: range.getBoundingClientRect() };
    }, []);

    const handleDragLeave = (e: React.DragEvent<HTMLSpanElement>) => {
        e.preventDefault();
        hideIndicator();
    };

    const handleDragOver = (e: React.DragEvent<HTMLSpanElement>) => {
        e.preventDefault();
        const dropInfo = getDropInfo(e.clientX, e.clientY);
        if (dropInfo) {
            e.stopPropagation();
            dropIndexRef.current = dropInfo.index;
            showIndicator(dropInfo.rect, 'caret');
        } else {
            hideIndicator();
        }
    };
    
    const handleDrop = (e: React.DragEvent<HTMLSpanElement>) => {
        e.preventDefault();
        e.stopPropagation();
        hideIndicator();
        
        const lineIndex = parseInt((e.currentTarget.closest('.canvas-line') as HTMLElement)?.dataset.lineIndex ?? '0', 10);
        const draggedData = JSON.parse(e.dataTransfer.getData('application/json'));
        onDropPart({ draggedData, targetPartId: id, lineIndex, index: dropIndexRef.current, mode: 'split' });
    };

    const handleKeyDown = (e: React.KeyboardEvent<HTMLSpanElement>) => {
        const selection = window.getSelection();
        if (!selection || selection.rangeCount === 0) {
            return;
        }

        const el = e.currentTarget;
        const range = selection.getRangeAt(0);
        const textContent = el.innerText || '';
        const isCollapsed = range.startOffset === range.endOffset;

        const atStartOfText = isCollapsed && range.startOffset === 0;
        const atEndOfText = isCollapsed && range.startOffset === textContent.length;

        if (e.key === 'Enter') {
            e.preventDefault();
            onSplitPart(id, range.startOffset, textContent);
            return;
        }
        
        if (e.key === 'Backspace' && atStartOfText) {
            e.preventDefault();
            onMergePart(id);
            return;
        }
        
        // Navigation Logic
        let isAtBoundary = false;
        let direction: NavDirection | null = null;
        
        if (e.key === 'ArrowLeft' && atStartOfText) { isAtBoundary = true; direction = 'left'; }
        if (e.key === 'ArrowRight' && atEndOfText) { isAtBoundary = true; direction = 'right'; }
        
        if (e.key === 'ArrowUp' || e.key === 'ArrowDown') {
            const caretRects = range.getClientRects();
            if (caretRects.length === 0) { // Empty field
                isAtBoundary = true;
            } else {
                const caretRect = caretRects[0];
                const elRect = el.getBoundingClientRect();
                const tolerance = 2; // px tolerance
                const isAtTop = caretRect.top <= elRect.top + tolerance;
                const isAtBottom = caretRect.bottom >= elRect.bottom - tolerance;
                if ((e.key === 'ArrowUp' && isAtTop) || (e.key === 'ArrowDown' && isAtBottom)) {
                    isAtBoundary = true;
                }
            }
            direction = e.key === 'ArrowUp' ? 'up' : 'down';
        }
        
        if (isAtBoundary && direction) {
            e.preventDefault();
            onNavigate(id, direction);
        } else if (e.key.startsWith('Arrow')) {
            // Not at a boundary, so allow default text editing behavior (moving the cursor)
            // and PREVENT the event from bubbling up to a higher-level navigator.
            e.stopPropagation();
        }
    };
    
    return (
        <span
            ref={setRefs}
            contentEditable
            suppressContentEditableWarning={true}
            onInput={handleInput}
            onBlur={handleBlur}
            onFocus={handleFocus}
            onDragLeave={handleDragLeave}
            onDragOver={handleDragOver}
            onDrop={handleDrop}
            onKeyDown={handleKeyDown}
            style={{ whiteSpace: 'pre-wrap' }}
            className={`outline-none rounded -mx-1 px-1 transition-colors duration-150 hover:bg-gray-100 focus:bg-indigo-100/50`}
            draggable={true}
            onDragStart={(e) => onDragStart(e, { id, blockType, content })}
            data-canvas-element="true"
            data-part-id={id}
            // Use a key to force re-render if content is changed externally, resetting innerText
            key={id}
        >
          {content}
        </span>
    );
};

export default EditableText;