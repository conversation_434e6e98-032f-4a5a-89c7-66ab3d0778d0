import { useEffect, useRef, useState } from 'react';

interface DragInsertionCursorOptions {
  blinkInterval?: number;
  insertionSymbol?: string;
  cooldownTime?: number;
}

export function useDragInsertionCursor(
  targetRef: React.RefObject<HTMLElement>,
  isDragging: boolean,
  options: DragInsertionCursorOptions = {}
) {
  const {
    blinkInterval = 500,
    insertionSymbol = '|',
    cooldownTime = 1000
  } = options;

  const [isVisible, setIsVisible] = useState(false);
  const [insertionPoint, setInsertionPoint] = useState<{ x: number; y: number } | null>(null);
  const cooldownRef = useRef<number>(0);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // Start/stop blinking based on drag state
  useEffect(() => {
    if (isDragging && insertionPoint) {
      // Reset cooldown and start visible
      cooldownRef.current = cooldownTime;
      setIsVisible(true);

      // Clear any existing interval
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }

      // Start blinking interval
      intervalRef.current = setInterval(() => {
        if (cooldownRef.current > 0) {
          cooldownRef.current -= blinkInterval;
          setIsVisible(true); // Stay visible during cooldown
        } else {
          setIsVisible(prev => !prev); // Start blinking after cooldown
        }
      }, blinkInterval);

    } else {
      // Stop blinking when not dragging
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      setIsVisible(false);
      setInsertionPoint(null);
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isDragging, insertionPoint, blinkInterval, cooldownTime]);

  // Function to update insertion point position
  const updateInsertionPoint = (clientX: number, clientY: number) => {
    const target = targetRef.current;
    if (!target || !isDragging) return;

    // Use caretRangeFromPoint to get precise text position
    let range: Range | null = null;
    if ((document as any).caretRangeFromPoint) {
      range = (document as any).caretRangeFromPoint(clientX, clientY);
    } else if ((document as any).caretPositionFromPoint) {
      const pos = (document as any).caretPositionFromPoint(clientX, clientY);
      if (pos) {
        range = document.createRange();
        range.setStart(pos.offsetNode, pos.offset);
        range.collapse(true);
      }
    }

    if (range) {
      const rect = range.getBoundingClientRect();
      const targetRect = target.getBoundingClientRect();
      
      setInsertionPoint({
        x: rect.left - targetRect.left,
        y: rect.top - targetRect.top
      });
      
      // Reset cooldown when position updates
      cooldownRef.current = cooldownTime;
      setIsVisible(true);
    }
  };

  // Reset insertion point
  const resetInsertionPoint = () => {
    setInsertionPoint(null);
    setIsVisible(false);
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  };

  return {
    insertionPoint: isVisible ? insertionPoint : null,
    updateInsertionPoint,
    resetInsertionPoint
  };
}
