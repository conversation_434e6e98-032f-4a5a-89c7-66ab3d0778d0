"use client";

import { GripVertical, List } from "lucide-react";
import type { FindingBlock } from "../types";

export function FindingBlockInline({ block, onDragStart }: { block: FindingBlock; onDragStart?: (e: React.DragEvent, id: string) => void; }) {
  return (
    <span
      contentEditable={false}
      draggable
      onDragStart={(e) => onDragStart?.(e, block.id)}
      className="mx-1 p-1 pr-2 rounded border border-blue-300 bg-blue-50 text-blue-800 inline-flex items-center gap-1.5 align-middle text-sm cursor-grab"
      data-inline
      data-inline-id={block.id}
    >
      <GripVertical size={14} className="text-muted-foreground" />
      <List size={14} />
      <span className="font-medium">{block.findingName}</span>
      <select
        className="text-xs bg-white rounded border-border p-0.5 ml-1"
        defaultValue={block.selectedValue}
        onChange={(e) => { block.selectedValue = e.target.value as any; /* state updated by parent via re-render from input sync */ }}
      >
        <option value="value">Value</option>
        <option value="name">Name</option>
      </select>
    </span>
  );
}

