
import React from 'react';
import { BlockType } from '../types';
import { CLINICAL_EXAM_FINDINGS } from '../constants';
import { VariableIcon, AIInstructionIcon, CategoryIcon } from './icons/Icons';

interface DraggableItemProps {
    blockType: BlockType;
    children: React.ReactNode;
}

const Sidebar: React.FC = () => {
    const handleDragStart = (e: React.DragEvent<HTMLDivElement>, partData: any) => {
        e.dataTransfer.effectAllowed = 'copy';
        e.dataTransfer.setData('application/json', JSON.stringify({ newPart: partData }));
    };

    const createBlockData = (blockType: BlockType) => {
        switch(blockType) {
            case BlockType.Variable:
                return { blockType: BlockType.Variable, name: 'New Variable' };
            case BlockType.AIInstruction:
                return { 
                    blockType: BlockType.AIInstruction, 
                    parts: [
                        [{ id: `text-new-${Date.now()}`, blockType: BlockType.Text, content: 'new AI instruction' }]
                    ] 
                };
            case BlockType.Category:
                return { blockType: BlockType.Category, category: 'Clinical Exam', options: CLINICAL_EXAM_FINDINGS, selected: [] };
            default:
                return {};
        }
    };
    
    const DraggableItem: React.FC<DraggableItemProps> = ({ blockType, children }) => (
        <div
            draggable
            onDragStart={(e) => handleDragStart(e, createBlockData(blockType))}
            className="flex items-center p-3 text-sm font-medium text-gray-700 bg-gray-50 hover:bg-gray-200 rounded-md transition-colors duration-150 cursor-grab active:cursor-grabbing"
        >
            {children}
        </div>
    );

    return (
        <aside className="w-64 flex-shrink-0 bg-white rounded-lg shadow-lg border border-gray-200 p-4">
            <h2 className="text-lg font-semibold text-gray-800 mb-4">Blocks</h2>
            <div className="space-y-3">
                <DraggableItem blockType={BlockType.Variable}>
                    <VariableIcon className="w-5 h-5 mr-3 text-blue-500" />
                    Variable
                </DraggableItem>
                <DraggableItem blockType={BlockType.AIInstruction}>
                    <AIInstructionIcon className="w-5 h-5 mr-3 text-green-500" />
                    AI Instruction
                </DraggableItem>
                <DraggableItem blockType={BlockType.Category}>
                    <CategoryIcon className="w-5 h-5 mr-3 text-purple-500" />
                    Category Finding
                </DraggableItem>
            </div>
        </aside>
    );
};

export default Sidebar;