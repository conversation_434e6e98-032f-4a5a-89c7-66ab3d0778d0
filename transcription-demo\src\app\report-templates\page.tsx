"use client";

import { useEffect, useMemo, useState } from "react";
import { WorkspaceNav } from "@/components/workspace-nav";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { getConvex } from "@/lib/convexClient";

import { EditorCanvas } from "./_components/EditorCanvas";
import { BlockSidebar } from "./_components/BlockSidebar";
import { ReportPreview } from "./_components/ReportPreview";
import type { Block, DetectionItem } from "./_components/types";

export default function ReportTemplatesPage() {
  const [title, setTitle] = useState("Consultation Report");
  const [blocks, setBlocks] = useState<Block[]>([
    { id: crypto.randomUUID(), type: "TEXT", content: "<h2>Consultation Report</h2><div>Start typing your report here...</div>" },
  ]);
  const [detectionItems, setDetectionItems] = useState<DetectionItem[]>([]);
  const [previewOpen, setPreviewOpen] = useState(false);

  const convex = getConvex();
  const loose = convex as unknown as { query: (name: string, args?: unknown) => Promise<unknown>; mutation: (name: string, args?: unknown) => Promise<unknown>; action: (name: string, args?: unknown) => Promise<unknown>; };

  // Template management
  type RawTemplate = { _id?: string; id?: string; title?: string; blocks?: unknown[]; published?: boolean; ownerUserId?: string; updatedAt?: number };
  const [templates, setTemplates] = useState<Array<{ id?: string; title: string; blocks: Block[] }>>([]);
  const [currentId, setCurrentId] = useState<string | undefined>(undefined);

  function normalizeBlocks(rawBlocks: unknown[] | undefined): Block[] {
    if (!rawBlocks) return [];
    return rawBlocks.map((rb) => {
      const any = rb as any;
      if (any && typeof any === "object" && typeof any.type === "string") return any as Block;
      // Convert prior editor shapes to TEXT for compatibility
      if (any.kind === "text") return { id: crypto.randomUUID(), type: "TEXT", content: (any.value ?? "") as string } as Block;
      if (any.kind === "section") return { id: crypto.randomUUID(), type: "TEXT", content: `<h3>${any.value ?? ""}</h3>` } as Block;
      if (any.kind === "field") return { id: crypto.randomUUID(), type: "TEXT", content: `<p><strong>${any.label ?? any.value ?? "Field"}</strong>: ______</p>` } as Block;
      return { id: crypto.randomUUID(), type: "TEXT", content: "" } as Block;
    });
  }

  function normalizeTemplate(raw: unknown) {
    const r = raw as RawTemplate;
    const id = r.id ?? (r._id ? String(r._id) : undefined);
    return { id, title: r.title ?? "Untitled", blocks: normalizeBlocks(r.blocks) } as { id?: string; title: string; blocks: Block[] };
  }

  // Load templates from Convex (only once on mount)
  useEffect(() => {
    let ignore = false;
    async function loadTemplates() {
      if (!convex) return;
      try {
        const list = await (loose.query("templates:listTemplates", {}) as Promise<unknown>);
        const items = (list as unknown[]).map(normalizeTemplate);
        if (!ignore) {
          setTemplates(items);
          // Only set initial template if we don't have one selected yet
          if (!currentId && items.length > 0) {
            const first = items[0];
            setCurrentId(first.id);
            setTitle(first.title);
            setBlocks(first.blocks);
          }
        }
      } catch {}
    }
    loadTemplates();
    return () => { ignore = true; };
  }, [convex, loose]); // Removed currentId dependency to prevent overwriting

  // Load available definitions for sidebar (Convex)
  useEffect(() => {
    let cancelled = false;
    async function load() {
      if (!convex) return;
      try {
        const res = await loose.query("detections:listDefinitions", {});
        const items = (res as unknown[]).map((r) => {
          const doc = r as { id?: string; _id?: string; name?: string; category?: string };
          const id = doc.id ?? (doc._id ? String(doc._id) : crypto.randomUUID());
          return { id, name: doc.name ?? "", category: doc.category ?? "Uncategorized" } as DetectionItem;
        });
        if (!cancelled) setDetectionItems(items);
      } catch {}
    }
    load();
    return () => { cancelled = true; };
  }, [convex, loose]);

  const leftPanel = (
    <div className="flex flex-col h-[calc(100vh-8rem)]">
      {/* Editor Panel (expanded by default, collapses when preview is open) */}
      {!previewOpen ? (
        <div className="flex-1 flex flex-col gap-3">
          <div className="flex items-center justify-between gap-2">
            <Input value={title} onChange={(e) => setTitle(e.target.value)} className="h-9" />
          </div>
          <EditorCanvas blocks={blocks} setBlocks={(updater) => setBlocks((prev) => updater(prev))} detectionItems={detectionItems} />
        </div>
      ) : (
        <div className="shrink-0 border rounded-md px-3 h-10 flex items-center justify-between">
          <div className="text-sm text-muted-foreground truncate">(v) Hide Preview • {title}</div>
          <Button variant="ghost" size="sm" onClick={() => setPreviewOpen(false)}>Hide Preview</Button>
        </div>
      )}

      {/* Preview Panel (collapsed footer by default) */}
      {!previewOpen ? (
        <div className="shrink-0 border rounded-md px-3 py-2 mt-3 flex items-center justify-between bg-muted/20">
          <div className="text-sm text-muted-foreground">(^) Show Preview</div>
          <div className="flex items-center gap-2">
            <Button variant="secondary" onClick={() => setPreviewOpen(true)}>Show Preview</Button>
          </div>
        </div>
      ) : (
        <div className="flex-1 mt-3 min-h-0">
          <ReportPreview blocks={blocks} detectionItems={detectionItems} />
        </div>
      )}
    </div>
  );

  return (
    <div className="container mx-auto p-6 space-y-4">
      <WorkspaceNav />
      <style>{`[data-placeholder]:empty::before { content: attr(data-placeholder); color: #9ca3af; font-style: italic; } .sidebar-scrollbar::-webkit-scrollbar{width:8px}.sidebar-scrollbar::-webkit-scrollbar-thumb{background:#d1d5db;border-radius:4px}.sidebar-scrollbar::-webkit-scrollbar-thumb:hover{background:#9ca3af}`}</style>

      {/* Template Management Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <span className="text-sm text-muted-foreground">Template</span>
          <Select value={currentId ?? ""} onValueChange={(id) => {
            setCurrentId(id);
            const t = templates.find((x) => x.id === id);
            if (t) { setTitle(t.title); setBlocks(t.blocks); }
          }}>
            <SelectTrigger className="w-[260px]"><SelectValue placeholder="Select template" /></SelectTrigger>
            <SelectContent>
              {templates.map((t) => (<SelectItem key={t.id} value={t.id!}>{t.title}</SelectItem>))}
            </SelectContent>
          </Select>
          <Button variant="outline" onClick={() => {
            const id = `local_${crypto.randomUUID()}`;
            const t = { id, title: "Untitled Template", blocks: [] as Block[] };
            setTemplates((prev) => [...prev, t]);
            setCurrentId(id);
            setTitle(t.title);
            setBlocks(t.blocks);
          }}>New</Button>
          <Button variant="outline" onClick={() => {
            if (!currentId) return;
            const base = templates.find((t) => t.id === currentId);
            if (!base) return;
            const id = `local_${crypto.randomUUID()}`;
            const title = `${base.title} (copy)`;
            const blocks = base.blocks.map((b) => ({ ...b, id: crypto.randomUUID() })) as Block[];
            const clone = { id, title, blocks };
            setTemplates((prev) => [...prev, clone]);
            setCurrentId(id);
            setTitle(title);
            setBlocks(blocks);
          }}>Clone</Button>
          <Button variant="secondary" onClick={async () => {
            if (convex) {
              await loose.mutation("templates:upsertTemplate", { id: currentId, title, blocks, published: false, ownerUserId: "demo-user" });
              const list = await (loose.query("templates:listTemplates", {}) as Promise<unknown>);
              setTemplates((list as unknown[]).map(normalizeTemplate));
            }
          }}>Save</Button>
        </div>
      </div>

      <div className="grid grid-cols-[1fr_320px] gap-4">
        {leftPanel}
        <BlockSidebar detectionItems={detectionItems} />
      </div>
    </div>
  );
}
